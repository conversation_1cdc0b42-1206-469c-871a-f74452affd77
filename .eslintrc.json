{"extends": ["next/core-web-vitals", "next/typescript"], "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "prefer-const": "error", "no-var": "error", "no-console": "warn", "react-hooks/exhaustive-deps": "warn", "react/no-unescaped-entities": "off"}, "overrides": [{"files": ["**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "env": {"jest": true}}]}