version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ottiq-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ottiq_dev
      POSTGRES_USER: ottiq_user
      POSTGRES_PASSWORD: ottiq_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - ottiq-network

  # MinIO Object Storage (S3-compatible)
  minio:
    image: minio/minio:latest
    container_name: ottiq-minio
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: ottiq_minio
      MINIO_ROOT_PASSWORD: ottiq_minio_password
    ports:
      - "9000:9000"  # API
      - "9001:9001"  # Console
    volumes:
      - minio_data:/data
    networks:
      - ottiq-network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: ottiq-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ottiq-network

  # Mailhog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: ottiq-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - ottiq-network

volumes:
  postgres_data:
    driver: local
  minio_data:
    driver: local
  redis_data:
    driver: local

networks:
  ottiq-network:
    driver: bridge
