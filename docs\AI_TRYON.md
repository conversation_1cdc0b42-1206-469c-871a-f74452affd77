# AI Try-On Implementation

This document describes the AI Try-On feature implementation for Ottiq, which allows customers to see themselves wearing custom designs using Hugging Face's OOTDiffusion model.

## Overview

The AI Try-On feature enables users to:
- Upload their portrait photo
- Select a customized design
- Generate an AI-powered try-on image showing them wearing the design
- View results with confidence scores
- Share and download the results

## Architecture

### Components

1. **API Route**: `/api/ai-tryon/create`
   - Handles image upload and validation
   - Manages AI try-on job lifecycle
   - Integrates with Hugging Face OOTDiffusion

2. **Hugging Face Service**: `src/lib/services/huggingface.ts`
   - Manages connection to OOTDiffusion Space
   - Handles retry logic and error handling
   - Processes AI model requests

3. **Image Upload Utils**: `src/lib/utils/imageUpload.ts`
   - Validates and processes uploaded images
   - Handles security scanning
   - Optimizes images for AI processing

4. **React Hook**: `src/hooks/useAiTryOn.ts`
   - Manages frontend state
   - Handles image selection and upload
   - Polls for job completion

5. **Demo Component**: `src/components/AiTryOnDemo.tsx`
   - Complete UI for AI try-on flow
   - Mobile-first responsive design
   - Progress tracking and error handling

### Database Schema

The `AiTryOnJob` model tracks try-on requests:

```prisma
model AiTryOnJob {
  id              String        @id @default(cuid())
  status          AiTryOnStatus @default(PENDING)
  jobId           String?       // External AI service job ID
  userPhotoUrl    String        // User's photo for try-on
  customizationId String
  customization   Customization @relation(fields: [customizationId], references: [id], onDelete: Cascade)
  resultImageUrl  String?       // Generated try-on image
  confidence      Float?        // AI confidence score
  processingTime  Int?          // Processing time in seconds
  errorMessage    String?
  retryCount      Int           @default(0)
  userId          String
  user            User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
}

enum AiTryOnStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}
```

## API Endpoints

### POST /api/ai-tryon/create

Creates a new AI try-on job.

**Request Body (JSON):**
```json
{
  "userPhotoUrl": "data:image/jpeg;base64,...",
  "customizationId": "custom-123",
  "userId": "user-123",
  "contextScene": "outdoor" // optional
}
```

**Request Body (FormData):**
```
userPhoto: File
customizationId: string
userId: string
contextScene: string (optional)
```

**Response:**
```json
{
  "success": true,
  "data": {
    "jobId": "job-123",
    "status": "COMPLETED",
    "resultImageUrl": "data:image/jpeg;base64,...",
    "confidence": 0.85
  },
  "message": "AI try-on completed successfully"
}
```

### GET /api/ai-tryon/create?jobId=xxx

Checks the status of an AI try-on job.

**Response:**
```json
{
  "success": true,
  "data": {
    "jobId": "job-123",
    "status": "PROCESSING",
    "estimatedProcessingTime": 60
  }
}
```

## Usage Examples

### Basic Usage with React Hook

```tsx
import { useAiTryOn } from '@/hooks/useAiTryOn';

function MyComponent() {
  const {
    selectedImage,
    imagePreview,
    resultImageUrl,
    isProcessing,
    error,
    selectImage,
    generateTryOn,
    reset
  } = useAiTryOn();

  const handleFileSelect = (file: File) => {
    selectImage(file);
  };

  const handleTryOn = () => {
    generateTryOn('customization-id', 'user-id', 'outdoor');
  };

  return (
    <div>
      <input 
        type="file" 
        onChange={(e) => handleFileSelect(e.target.files[0])} 
      />
      {imagePreview && (
        <img src={imagePreview} alt="Preview" />
      )}
      <button onClick={handleTryOn} disabled={!selectedImage || isProcessing}>
        {isProcessing ? 'Processing...' : 'Try It On!'}
      </button>
      {resultImageUrl && (
        <img src={resultImageUrl} alt="Try-on result" />
      )}
      {error && <p>Error: {error}</p>}
    </div>
  );
}
```

### Direct API Usage

```typescript
// Upload and generate try-on
const formData = new FormData();
formData.append('userPhoto', file);
formData.append('customizationId', 'custom-123');
formData.append('userId', 'user-123');
formData.append('contextScene', 'outdoor');

const response = await fetch('/api/ai-tryon/create', {
  method: 'POST',
  body: formData,
});

const result = await response.json();

// Check job status
const statusResponse = await fetch(`/api/ai-tryon/create?jobId=${result.data.jobId}`);
const status = await statusResponse.json();
```

## Configuration

### Environment Variables

Add these to your `.env.local`:

```bash
# Hugging Face OOTDiffusion Space URL
HUGGINGFACE_OOTD_SPACE_URL="https://huggingface.co/spaces/levihsu/OOTDiffusion"

# Optional: Hugging Face API Token
HUGGINGFACE_API_TOKEN="your-token-here"

# AI Try-On Settings
AI_IMAGE_TARGET_SIZE="512"
AI_TRYON_RATE_LIMIT="10"
MAX_CONCURRENT_AI_JOBS="5"
AI_TRYON_TIMEOUT="120000"

# Image Security
ENABLE_IMAGE_SECURITY_SCAN="false"
```

### Image Processing Settings

The system automatically:
- Resizes images to 512x512 for optimal AI processing
- Validates file types (JPEG, PNG, WebP)
- Enforces size limits (5MB for portraits)
- Scans for security threats (optional)

## Error Handling

The implementation includes comprehensive error handling:

1. **Validation Errors**: Invalid file types, sizes, or formats
2. **Authentication Errors**: Invalid user or customization access
3. **AI Service Errors**: Hugging Face API failures or timeouts
4. **Network Errors**: Connection issues and retries
5. **Rate Limiting**: Too many requests per user

## Performance Considerations

1. **Image Optimization**: Images are automatically resized and compressed
2. **Caching**: Results can be cached to avoid reprocessing
3. **Rate Limiting**: Prevents abuse and manages API costs
4. **Async Processing**: Jobs are processed asynchronously with status polling
5. **Retry Logic**: Automatic retries for transient failures

## Security

1. **Image Validation**: Strict file type and size validation
2. **Malware Scanning**: Optional security scanning for uploads
3. **Access Control**: Users can only try on their own customizations
4. **Data Privacy**: Images are processed securely and can be auto-deleted
5. **Rate Limiting**: Prevents abuse and DoS attacks

## Testing

Run the test suite:

```bash
# Unit tests
npm test src/__tests__/ai-tryon.test.ts

# Hook tests
npm test src/__tests__/useAiTryOn.test.ts

# Integration tests
npm run test:e2e
```

## Deployment

1. Set up environment variables in production
2. Configure Hugging Face Space access
3. Set up image storage (S3/MinIO)
4. Configure monitoring and logging
5. Set appropriate rate limits

## Troubleshooting

### Common Issues

1. **"Hugging Face client not initialized"**
   - Check HUGGINGFACE_OOTD_SPACE_URL is set correctly
   - Verify network connectivity to Hugging Face

2. **"Invalid image format"**
   - Ensure uploaded files are valid images
   - Check file size limits

3. **"AI try-on generation failed"**
   - Check Hugging Face Space availability
   - Verify image quality and format

4. **Slow processing times**
   - Hugging Face Spaces can be slow during peak times
   - Consider upgrading to a dedicated instance

### Monitoring

Monitor these metrics:
- Try-on success rate
- Average processing time
- Error rates by type
- User engagement with results

## Future Enhancements

1. **Multiple Scenes**: Support for different background contexts
2. **Batch Processing**: Process multiple designs at once
3. **Style Transfer**: Additional AI models for different effects
4. **Real-time Preview**: Faster preview generation
5. **Mobile App**: Native mobile implementation
