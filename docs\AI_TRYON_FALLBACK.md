# AI Try-On Fallback System & Admin Controls

This document describes the comprehensive fallback system and admin controls for the AI Try-On feature in Ottiq, ensuring reliability and brand trust.

## Overview

The AI Try-On Fallback System provides:
- **Admin Controls**: Product-level toggles for AI try-on availability
- **Automatic Fallback**: Queue system for manual processing when AI fails
- **Monitoring Dashboard**: Real-time system health and performance metrics
- **Manual Processing**: Colab notebook integration for admin processing

## Architecture

### Database Schema

#### Product Model Extensions
```sql
-- Added to products table
aiTryOnEnabled BOOLEAN DEFAULT true  -- Admin toggle for AI try-on
aiTryOnPriority INTEGER DEFAULT 1    -- Priority for fallback queue (1=high, 5=low)
```

#### AI Try-On Job Status Extensions
```sql
-- New statuses added to AiTryOnStatus enum
QUEUED_FOR_FALLBACK    -- Job failed and queued for manual processing
FALLBACK_PROCESSING    -- Admin is manually processing
FALLBACK_COMPLETED     -- Manually processed successfully
```

#### Fallback Queue Model
```sql
CREATE TABLE ai_try_on_fallback_queue (
  id                  TEXT PRIMARY KEY,
  aiTryOnJobId        TEXT UNIQUE REFERENCES ai_try_on_jobs(id),
  status              FallbackQueueStatus DEFAULT 'PENDING',
  priority            INTEGER DEFAULT 1,
  assignedTo          TEXT,              -- Admin user ID
  colabNotebookUrl    TEXT,              -- URL to Colab notebook
  processingNotes     TEXT,              -- Admin notes
  estimatedCompletion TIMESTAMP,         -- When admin expects completion
  originalError       TEXT,              -- Error that caused fallback
  retryCount          INTEGER DEFAULT 0,
  lastRetryAt         TIMESTAMP,
  createdAt           TIMESTAMP DEFAULT NOW(),
  updatedAt           TIMESTAMP DEFAULT NOW()
);
```

## Admin Controls

### Product-Level AI Try-On Toggle

Admins can enable/disable AI try-on for individual products:

```typescript
// Enable AI try-on for a product
PUT /api/admin/ai-tryon/products
{
  "productId": "product-123",
  "aiTryOnEnabled": true,
  "aiTryOnPriority": 2
}

// Bulk update multiple products
POST /api/admin/ai-tryon/products/bulk
{
  "productIds": ["product-1", "product-2"],
  "aiTryOnEnabled": false
}
```

### Priority System

Products have priority levels (1-5) that determine fallback queue ordering:
- **1 (Critical)**: High-value products, processed first
- **2 (High)**: Important products
- **3 (Medium)**: Standard products (default)
- **4 (Low)**: Less important products
- **5 (Lowest)**: Lowest priority products

## Fallback Queue System

### Automatic Fallback Trigger

When an AI try-on job fails after maximum retries (default: 3), it's automatically added to the fallback queue:

```typescript
// Fallback logic in AI try-on service
if (retryCount >= maxRetries) {
  await prisma.$transaction(async (tx) => {
    // Update job status
    await tx.aiTryOnJob.update({
      where: { id: jobId },
      data: {
        status: 'QUEUED_FOR_FALLBACK',
        errorMessage: error.message,
        retryCount: retryCount,
      },
    });

    // Create fallback queue entry
    await tx.aiTryOnFallbackQueue.create({
      data: {
        aiTryOnJobId: jobId,
        status: 'PENDING',
        priority: product.aiTryOnPriority,
        originalError: error.message,
        retryCount: retryCount,
      },
    });
  });
}
```

### Queue Management

Admins can manage the fallback queue through the dashboard:

```typescript
// Get queue items
GET /api/admin/ai-tryon/fallback-queue?status=PENDING&priority=1

// Claim a job
POST /api/admin/ai-tryon/fallback-queue
{
  "queueId": "queue-123",
  "estimatedCompletion": "2024-01-15T14:00:00Z"
}

// Update job status
PUT /api/admin/ai-tryon/fallback-queue
{
  "queueId": "queue-123",
  "status": "PROCESSING",
  "processingNotes": "Working on manual processing",
  "colabNotebookUrl": "https://colab.research.google.com/..."
}

// Complete job
POST /api/admin/ai-tryon/fallback-queue/complete
{
  "queueId": "queue-123",
  "resultImageUrl": "data:image/jpeg;base64,...",
  "processingNotes": "Manually processed successfully",
  "confidence": 0.9
}
```

## Admin Dashboard

### System Status Overview

The dashboard provides real-time system health monitoring:

- **System Health**: Overall status (healthy/warning/critical)
- **Success Rate**: Percentage of successful AI try-ons
- **Queue Length**: Number of jobs waiting for manual processing
- **Average Processing Time**: Time to complete AI try-ons
- **Recent Failures**: Failed jobs in the last 24 hours

### Health Indicators

```typescript
// System health calculation
let systemHealth: 'healthy' | 'warning' | 'critical' = 'healthy';

if (successRate < 0.7 || queueLength > 10 || recentFailures > 5 || avgProcessingTime > 120) {
  systemHealth = 'critical';
} else if (successRate < 0.9 || queueLength > 5 || recentFailures > 2 || avgProcessingTime > 60) {
  systemHealth = 'warning';
}
```

### Dashboard Tabs

1. **Product Settings**: Manage AI try-on toggles and priorities
2. **Fallback Queue**: View and process failed jobs
3. **Analytics**: Performance metrics and trends
4. **System Settings**: Configure system parameters

## Manual Processing Workflow

### Colab Notebook Integration

For manual processing, admins can use Colab notebooks:

1. **Claim Job**: Admin claims a job from the queue
2. **Generate Notebook**: System provides Colab notebook URL with pre-filled data
3. **Manual Processing**: Admin processes the try-on manually
4. **Upload Result**: Admin uploads the result image
5. **Complete Job**: Job is marked as completed

### Processing Notes

Admins can add notes during processing:
- Technical issues encountered
- Processing approach used
- Quality assessment
- Recommendations for improvement

## API Endpoints

### Admin Authentication

All admin endpoints require authentication and admin role:

```typescript
// Check admin access
const session = await getServerSession(authOptions);
if (!session?.user || session.user.role !== 'admin') {
  return NextResponse.json(
    { success: false, error: 'Unauthorized - Admin access required' },
    { status: 401 }
  );
}
```

### Product Management

- `GET /api/admin/ai-tryon/products` - List products with AI try-on settings
- `PUT /api/admin/ai-tryon/products` - Update product settings
- `POST /api/admin/ai-tryon/products/bulk` - Bulk update products

### Fallback Queue

- `GET /api/admin/ai-tryon/fallback-queue` - List queue items
- `POST /api/admin/ai-tryon/fallback-queue` - Claim job
- `PUT /api/admin/ai-tryon/fallback-queue` - Update job
- `POST /api/admin/ai-tryon/fallback-queue/complete` - Complete job

### Analytics & Monitoring

- `GET /api/admin/ai-tryon/stats` - System statistics
- `GET /api/admin/ai-tryon/analytics` - Detailed analytics

## Configuration

### Environment Variables

```bash
# AI Try-On Fallback Settings
AI_TRYON_MAX_RETRIES=3
AI_TRYON_FALLBACK_ENABLED=true
AI_TRYON_QUEUE_PRIORITY_LEVELS=5

# Admin Settings
ADMIN_EMAILS="<EMAIL>,<EMAIL>"
ADMIN_NOTIFICATION_WEBHOOK=""

# Colab Integration
COLAB_TEMPLATE_NOTEBOOK_URL=""
COLAB_API_KEY=""
```

### System Settings

Configurable through admin dashboard:
- Maximum concurrent AI jobs
- Fallback threshold (retry count)
- Auto-retry enabled/disabled
- Admin notifications enabled/disabled
- Maintenance mode toggle

## Monitoring & Alerts

### Automated Alerts

The system can send alerts when:
- Success rate drops below threshold
- Queue length exceeds limit
- Processing time increases significantly
- System health becomes critical

### Performance Metrics

Key metrics tracked:
- Daily job counts and success rates
- Category performance comparison
- User engagement trends
- Top performing products
- Recent failure analysis

## Security Considerations

### Admin Access Control

- Role-based authentication required
- Admin emails configured in environment
- Session-based authorization
- Audit logging for admin actions

### Data Privacy

- User photos automatically deleted after retention period
- Processing notes encrypted at rest
- Admin access logged and monitored
- GDPR compliance maintained

## Troubleshooting

### Common Issues

1. **High Queue Length**
   - Check AI service availability
   - Verify admin processing capacity
   - Consider temporary product disabling

2. **Low Success Rate**
   - Review AI service configuration
   - Check image quality requirements
   - Analyze failure patterns

3. **Slow Processing**
   - Monitor AI service performance
   - Check network connectivity
   - Review resource allocation

### Emergency Procedures

1. **System Overload**
   - Enable maintenance mode
   - Disable AI try-on for non-critical products
   - Scale admin processing team

2. **AI Service Failure**
   - All jobs automatically queue for fallback
   - Admins notified immediately
   - Manual processing workflow activated

## Future Enhancements

### Planned Features

- **Auto-retry with backoff**: Intelligent retry scheduling
- **ML-based prioritization**: AI-driven queue prioritization
- **Batch processing**: Process multiple jobs simultaneously
- **Quality scoring**: Automated quality assessment
- **Performance optimization**: Caching and optimization

### Integration Opportunities

- **External AI services**: Multiple AI provider support
- **Workflow automation**: Automated processing pipelines
- **Advanced analytics**: ML-powered insights
- **Mobile admin app**: Mobile queue management
