# Ottiq Pricing Engine Documentation

## 🎯 Overview

The Ottiq Pricing Engine is designed with **emotional transparency** at its core. Rather than just showing numbers, it frames value and builds trust through clear, human-readable messaging that helps customers understand and feel good about their purchase decisions.

## ✨ Key Features

### 🧠 Emotional Intelligence
- **Value Framing**: Every price component includes emotional messaging
- **Quality Promises**: Clear commitments about what customers receive
- **Lifestyle Context**: Pricing tied to aspirational outcomes
- **Transparency**: Detailed breakdowns that build trust

### 🔒 Server-Side Security
- All sensitive calculations performed server-side
- No pricing logic exposed to client
- Secure API endpoints with validation
- Rate limiting and error handling

### 🎨 Admin Interface
- Visual rule builder for non-technical users
- Real-time pricing preview
- A/B testing capabilities
- Analytics and performance tracking

## 🏗 Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client App    │───▶│   API Routes    │───▶│ Pricing Engine  │
│                 │    │                 │    │                 │
│ - Calculator    │    │ - /compute      │    │ - Rule Engine   │
│ - Display       │    │ - /preview      │    │ - Validation    │
│ - Admin UI      │    │ - /rules        │    │ - Messaging     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                               ┌─────────────────┐
                                               │   Database      │
                                               │                 │
                                               │ - Products      │
                                               │ - Rules         │
                                               │ - Analytics     │
                                               └─────────────────┘
```

## 🚀 API Endpoints

### POST `/api/pricing/compute`
Calculate pricing for a specific request with emotional messaging.

**Request:**
```json
{
  "productId": "string",
  "quantity": 1,
  "printSize": "medium",
  "qualityTier": "standard",
  "fabricId": "optional",
  "userId": "optional"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "basePrice": 29.99,
    "totalPrice": 44.99,
    "currency": "USD",
    "breakdown": [
      {
        "id": "base_price",
        "name": "Base Price",
        "amount": 29.99,
        "type": "base",
        "valueMessage": "Premium design and craftsmanship included",
        "icon": "✨"
      },
      {
        "id": "fabric_surcharge",
        "name": "Premium Fabric",
        "amount": 15.00,
        "type": "surcharge",
        "valueMessage": "Luxurious feel and enhanced durability",
        "icon": "🧵"
      }
    ],
    "valueMessage": "Premium upgrades for an elevated experience",
    "qualityPromise": "Crafted with premium materials for an elevated wearing experience",
    "priceJustification": [
      "Ethically sourced materials",
      "Expert craftsmanship",
      "Luxurious feel and enhanced durability"
    ],
    "calculatedAt": "2024-01-15T10:30:00Z",
    "validUntil": "2024-01-16T10:30:00Z",
    "priceId": "price_abc123"
  }
}
```

### POST `/api/pricing/preview`
Test multiple pricing scenarios for admin use.

### GET/POST/PUT/DELETE `/api/pricing/rules`
Manage pricing rules through the admin interface.

## 🎨 Emotional Messaging Framework

### Value Messages by Context

**Fabric Upgrades:**
- "Luxurious feel and enhanced durability"
- "Sustainable choice for conscious consumers"
- "Premium comfort that lasts"

**Print Sizes:**
- Small: "Refined simplicity that speaks volumes"
- Medium: "Perfect balance of impact and sophistication"
- Large: "Commanding attention with confident style"
- Full Coverage: "Complete creative freedom for maximum impact"

**Quality Tiers:**
- Standard: "Reliable quality that fits your lifestyle"
- Premium: "Indulge in the finer details that make all the difference"
- Luxury: "Investment in timeless quality and unmatched sophistication"

### Quality Promises

**Standard:** "Built to last through countless wears and washes"
**Premium:** "Crafted with premium materials for an elevated wearing experience"
**Luxury:** "Heirloom-quality craftsmanship that defines true luxury"

## 🔧 Usage Examples

### Basic Pricing Calculator
```tsx
import { PricingCalculator } from '@/components/pricing/PricingCalculator';

<PricingCalculator
  productId="product-123"
  initialQuantity={1}
  onPriceCalculated={(response) => {
    console.log('Price calculated:', response.data?.totalPrice);
  }}
/>
```

### Custom Hook Usage
```tsx
import { usePricing } from '@/hooks/usePricing';

const { calculatePrice, loading, error } = usePricing();

const handleCalculate = async () => {
  const result = await calculatePrice({
    productId: 'product-123',
    quantity: 2,
    qualityTier: 'premium'
  });
  
  if (result?.success) {
    console.log('Value message:', result.data.valueMessage);
  }
};
```

### Admin Rule Management
```tsx
import { usePricingRules } from '@/hooks/usePricing';

const { rules, createRule, loading } = usePricingRules();

const handleCreateRule = async () => {
  await createRule({
    name: 'Holiday Discount',
    type: 'seasonal_promotion',
    modifier: -20,
    modifierType: 'percentage',
    customerMessage: 'Holiday savings',
    valueFraming: 'Celebrate the season with special pricing'
  });
};
```

## 🧪 Testing

### Run Pricing Tests
```bash
npm run pricing:test
```

### Unit Tests
```bash
npm test src/__tests__/pricing/
```

### API Tests
```bash
npm test src/__tests__/api/pricing.test.ts
```

## 📊 Analytics & Monitoring

The pricing engine tracks:
- **Conversion Impact**: How pricing affects purchase decisions
- **Rule Performance**: Which rules drive the most value
- **Emotional Resonance**: Customer response to messaging
- **Price Elasticity**: Demand sensitivity to price changes

## 🔐 Security Considerations

1. **Server-Side Only**: All pricing logic runs on the server
2. **Input Validation**: Comprehensive request validation with Zod
3. **Rate Limiting**: Prevents abuse of pricing endpoints
4. **Error Handling**: Graceful degradation without exposing internals
5. **Audit Trail**: All pricing calculations are logged

## 🎯 Best Practices

### For Developers
1. Always use the pricing hooks for client-side integration
2. Handle loading and error states gracefully
3. Cache pricing results appropriately
4. Test edge cases thoroughly

### For Business Users
1. Focus on emotional value in rule messaging
2. Test pricing changes with preview mode
3. Monitor conversion impact of rule changes
4. Keep messaging consistent with brand voice

## 🚀 Future Enhancements

- **Machine Learning**: Dynamic pricing based on demand patterns
- **A/B Testing**: Built-in experimentation framework
- **Personalization**: User-specific pricing optimization
- **International**: Multi-currency and regional pricing
- **Real-time**: WebSocket updates for live pricing changes

---

**Ottiq Pricing Engine** — Where transparency meets emotion, and every price tells a story of value. ✨
