import { test, expect } from '@playwright/test';

test('homepage loads correctly', async ({ page }) => {
  await page.goto('/');

  // Check if the main heading is visible
  await expect(page.getByRole('heading', { name: 'Ottiq' })).toBeVisible();
  
  // Check if the tagline is present
  await expect(page.getByText('Wear Your Imagination')).toBeVisible();
  
  // Check if the CTA button is present
  await expect(page.getByRole('button', { name: 'Start Creating' })).toBeVisible();
  
  // Check if the feature highlights are present
  await expect(page.getByText('AI-Powered Design')).toBeVisible();
  await expect(page.getByText('Unlimited Customization')).toBeVisible();
  await expect(page.getByText('Express Yourself')).toBeVisible();
});

test('mobile responsiveness', async ({ page }) => {
  // Set mobile viewport
  await page.setViewportSize({ width: 375, height: 667 });
  await page.goto('/');

  // Check if content is still visible on mobile
  await expect(page.getByRole('heading', { name: 'Ottiq' })).toBeVisible();
  await expect(page.getByText('Wear Your Imagination')).toBeVisible();
});
