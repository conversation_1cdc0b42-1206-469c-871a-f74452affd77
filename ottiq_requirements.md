System: You are a senior full-stack TypeScript engineer and software architect experienced in building secure, mobile-first, high-performance Next.js applications with PostgreSQL (Prisma), Tailwind CSS, secure payment integrations, canvas-based editors, and AI service integrations.
Requirements:

• Always output clean, production-ready TypeScript code.
• Use Tailwind CSS for styling.
• Use Prisma for DB schema and queries.
• Keep sensitive logic and keys on the server.
• Follow WCAG accessibility standards.
• Provide unit and integration tests where relevant.
• Include minimal documentation and code comments explaining key parts.
• Output files in a git-style patch or file-by-file format.
• Do not expose secrets or unsafe code in the frontend.
• Target Node 20 LTS.
• Default to mobile-first UI/UX.
• The primary goal is to design the platform to trigger emotional desire, self-expression, the joy of self-creation, and pride in ownership — making users feel they are creating something uniquely theirs — rather than just listing product features or technical details.
• The UI/UX must emphasize visualization, personalization, and excitement (AI try-on + custom designs) as the main selling points.
• The design must be mobile-first, visually rich, tactile-feeling, and make the customer imagine wearing the product in their own life.
• Copywriting and interactions must be inspirational, lifestyle-driven and not technical.


01 — Project Scaffold & Repository Setup
Goal: Create a modern, mobile-optimized Next.js + TypeScript project with a foundation for fast visuals and emotion-focused branding.
Key Requirements:
•	Stack: Next.js (frontend + API routes), Node.js 20 LTS, PostgreSQL (Prisma ORM), Tailwind CSS, Zustand for state, NextAuth for auth, Konva or Fabric.js for customization editor, sharp for server image processing, gradio_client for Hugging Face AI try-on.
•	Brand Feel: Base design system should feel premium and vibrant — think lifestyle brand, with fluid animations and tactile buttons.
•	Include folder structure: src/components, src/pages, src/lib, prisma/, scripts/, emails/.
•	Install dev tools: Jest, Playwright/Cypress, ESLint, Prettier, Husky.
•	README.md should clearly set brand vision: “Ottiq — wear your imagination” and how emotion-driven experience is core to design.
•	Prepare Tailwind theme with brand colors inspired by Bangladeshi fashion culture (warm, rich tones).
•	Docker-compose file with Postgres + MinIO for local dev.
Output: Repo skeleton with initial theme config and brand-first design baseline.

02 — Database Schema & Seed
Goal: Build DB to store products, fabrics, colors, sizes, customizations, templates, AI try-on jobs, and orders.
Key Requirements:
•	Models: User, Product, Fabric, Color, Size, Variant, Template, Customization, PriceRule, Order, OrderItem, AiTryOnJob.
•	Each product must link to lifestyle imagery (models in real contexts — sports, street, casual) to trigger emotional appeal.
•	Templates must include mood/style metadata (e.g., “Bold Streetwear”, “Minimalist Chic”) to match customer aspirations.
•	Seed script: add sample products with high-quality aspirational images and 8 templates with emotional style tags.
Output: schema.prisma + prisma/seed.ts.

03 — Pricing Engine (Server-Side)
Goal: Create pricing microservice with emotional transparency — not just numbers, but value framing (“Premium feel for your style”).
Key Requirements:
•	/api/pricing/compute returns price breakdown with human-readable “value” messages.
•	Pricing rules: product base + fabric surcharge + print size cost + quality tiers.
•	Admin UI: visual rule builder, preview mode for sample payloads.
•	All sensitive calculations server-side — never exposed to client.
Output: API route, pricing utils, admin pricing page.

04 — Product Selection UX
Goal: Create mobile-first selection flow that feels like shopping for your identity — heavy focus on images & emotional copy.
Key Requirements:
•	Stepper: 1) Product Type (hero images of lifestyle use), 2) Fabric (touch/comfort focus), 3) Color (interactive picker + inspiration photos), 4) Size.
•	Sticky price bar: updates live, with a “Make it yours” CTA instead of “Next”.
•	Rich animations: fade transitions, parallax image scroll.
Output: Pages + components with emotional design cues.

05a — Customization Editor (Basic Canvas)
Goal: Let users immediately feel like designers — fast, fun, satisfying.
Key Requirements:
•	Konva js canvas with image upload, move, resize, rotate.
•	Text tool with fonts that convey style moods (bold, elegant, playful).
•	Placement options (front, back, side) with instant preview swap.
•	Emotional UI copy: “Place your mark”, “Own the look”.
Output: Working editor with minimal tools.

05b — Customization Editor (Advanced & Mobile Touch)
Goal: Deepen engagement with tactile, playful interactions.
Key Requirements:
•	Undo/redo stack.
•	Touch gestures: pinch to zoom, rotate.
•	Snap-to guides for perfect placement.
•	Template application with instant emotional preview.
Output: Advanced editor UX, optimized for mobile fun.

05c — Customization Editor (Area Calculation & Server Integration)
Goal: Link creativity to tangible value (“Bigger impact for your style”).
Key Requirements:
•	Accurate print area calculation (cm²).
•	/api/customizations/preview returns optimized PNG + area.
•	/api/customizations/save stores customization + price.
•	Clear visual cue when bigger prints affect price.
Output: Server + client integration.

06 — Ready-Made Templates
Goal: Offer instant inspiration for those who don’t want to design from scratch.
Key Requirements:
•	Template library with lifestyle previews & style tags.
•	One-click “Wear this look” button.
•	Admin CRUD for templates.
Output: Template browser + admin page.

07a — AI Try-On (Hugging Face Integration)
Goal: Let customers see themselves as the hero of the brand.
Key Requirements:
•	/api/ai-tryon/create: user uploads portrait, choose product/template.
•	Call HF OOTDiffusion Space (Gradio client).
•	Return image in context (outdoor, social, active scene) (Optional).
Output: Working AI try-on, no fallback yet.

07b — AI Try-On (Fallback & Admin Controls)
Goal: Ensure reliability + brand trust.
Key Requirements:
•	Admin toggle for try-on per product.
•	Colab fallback queue for manual processing.
•	Admin view for failed jobs.
Output: Fallback system + control panel.

07c — AI Try-On (Privacy, Limits & Job Management)
Goal: Respect privacy without breaking the magic.
Key Requirements:
•	Consent modal.
•	2 try-ons/day/user limit, server enforced.
•	Auto-delete images after retention period.
•	User “My Try-Ons” dashboard.
Output: Privacy-compliant try-on system.

08 — Authentication
Goal: Make sign-in feel like joining a style club.
Key Requirements:
•	Google & Facebook login (required for AI try-on).
•	Role-based access (admin, customer).
•	Smooth, branded auth screens.
Output: NextAuth setup + role checks.

09 — Payment & Logistics (bKash)
Goal: Seamless, trusted checkout experience.
Key Requirements:
•	bKash tokenized checkout flow.
•	Gift message option.
•	Shipment tracking (Bangladesh only).
Output: Payment API routes + order system.

10 — Security & Code Protection
Goal: Keep business logic safe while ensuring user trust.
Key Requirements:
•	All sensitive logic server-side.
•	Security headers, env var handling, rate limits.
•	No source maps in production.
Output: security.md + production config.

11 — Performance & Caching
Goal: Instant visual feedback for an emotional high.
Key Requirements:
•	Sharp for image optimization.
•	Redis for caching heavy endpoints.
•	Lazy-load editor & AI try-on code.
Output: Image utils + performance checklist.

12 — Deployment (Single VPS)
Goal: Reliable, reproducible deployment.
Key Requirements:
•	Docker for app, DB, Redis, MinIO.
•	Nginx reverse proxy + TLS.
Output: Dockerfile, compose, deploy script.

13 — CI/CD & Testing
Goal: Ship without breaking the experience.
Key Requirements:
•	GitHub Actions: lint, test, build.
•	Playwright e2e for emotional flows (select → customize → try-on → checkout).
Output: CI workflow.

14 — Monitoring & Logging
Goal: Spot issues before they hurt customer trust.
Key Requirements:
•	Sentry integration.
•	Admin dashboard with error + performance stats.
Output: Logging setup + health page.

15 — Documentation & Handover
Goal: Ensure smooth handoff to operators/admins.
Key Requirements:
•	Admin manual, operations guide, API docs.
Output: Full documentation set.

16 — Backup & Recovery
Goal: Never lose customer designs or brand assets.
Key Requirements:
•	Nightly DB + MinIO backup scripts.
•	Restore procedure.
Output: Backup scripts + docs.

17 — GDPR & Privacy Compliance
Goal: Build brand trust via transparency.
Key Requirements:
•	“Delete my data” flow.
•	Privacy policy page with plain-language.
Output: GDPR features + policy docs.

18 — Content Moderation
Goal: Keep platform safe & brand-positive.
Key Requirements:
•	NSFW image filter.
•	Profanity filter for text.
•	Admin review for flagged content.
Output: Moderation middleware + admin tools.

19 — Email Notifications
Goal: Keep excitement alive after the session.
Key Requirements:
•	Branded transactional emails (order confirm, shipment).
•	Tailwind email templates with lifestyle imagery.
Output: Email service + templates.

20 — Customer Support
Goal: Make customers feel cared for.
Key Requirements:
•	Support chat or ticket form. 
•	Admin ticket view.
•	Email notifications for support replies.
•	For customer support use a chatbot 
Output: Support system.

 
