import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting Ottiq database seeding...');

  // Clean existing data (in development)
  if (process.env.NODE_ENV === 'development') {
    console.log('🧹 Cleaning existing data...');
    await prisma.orderItem.deleteMany();
    await prisma.order.deleteMany();
    await prisma.aiTryOnJob.deleteMany();
    await prisma.customization.deleteMany();
    await prisma.template.deleteMany();
    await prisma.productVariant.deleteMany();
    await prisma.product.deleteMany();
    await prisma.fabric.deleteMany();
    await prisma.color.deleteMany();
    await prisma.size.deleteMany();
    await prisma.priceRule.deleteMany();
  }

  // Seed Sizes
  console.log('📏 Seeding sizes...');
  const sizes = await Promise.all([
    prisma.size.create({
      data: {
        name: 'XS',
        displayName: 'Extra Small',
        measurements: {
          chest: '32-34',
          waist: '24-26',
          hip: '34-36',
          length: '25'
        },
        fitType: 'slim',
        fitDescription: 'Perfect for a sleek, tailored silhouette that hugs your curves beautifully',
        sortOrder: 1
      }
    }),
    prisma.size.create({
      data: {
        name: 'S',
        displayName: 'Small',
        measurements: {
          chest: '34-36',
          waist: '26-28',
          hip: '36-38',
          length: '26'
        },
        fitType: 'regular',
        fitDescription: 'Classic fit that moves with you, ideal for everyday confidence',
        sortOrder: 2
      }
    }),
    prisma.size.create({
      data: {
        name: 'M',
        displayName: 'Medium',
        measurements: {
          chest: '36-38',
          waist: '28-30',
          hip: '38-40',
          length: '27'
        },
        fitType: 'regular',
        fitDescription: 'The perfect balance of comfort and style for any occasion',
        sortOrder: 3
      }
    }),
    prisma.size.create({
      data: {
        name: 'L',
        displayName: 'Large',
        measurements: {
          chest: '38-40',
          waist: '30-32',
          hip: '40-42',
          length: '28'
        },
        fitType: 'regular',
        fitDescription: 'Comfortable fit that celebrates your natural shape with confidence',
        sortOrder: 4
      }
    }),
    prisma.size.create({
      data: {
        name: 'XL',
        displayName: 'Extra Large',
        measurements: {
          chest: '40-42',
          waist: '32-34',
          hip: '42-44',
          length: '29'
        },
        fitType: 'relaxed',
        fitDescription: 'Generous fit for ultimate comfort and effortless style',
        sortOrder: 5
      }
    })
  ]);

  // Seed Colors with emotional associations
  console.log('🎨 Seeding colors...');
  const colors = await Promise.all([
    prisma.color.create({
      data: {
        name: 'Midnight Black',
        hexCode: '#000000',
        moodTags: ['sophisticated', 'powerful', 'timeless', 'elegant'],
        seasonTags: ['fall', 'winter'],
        colorFamily: 'neutral',
        isPopular: true
      }
    }),
    prisma.color.create({
      data: {
        name: 'Pure White',
        hexCode: '#FFFFFF',
        moodTags: ['clean', 'fresh', 'minimalist', 'pure'],
        seasonTags: ['spring', 'summer'],
        colorFamily: 'neutral',
        isPopular: true
      }
    }),
    prisma.color.create({
      data: {
        name: 'Sunset Orange',
        hexCode: '#FF6B35',
        moodTags: ['energetic', 'bold', 'creative', 'confident'],
        seasonTags: ['summer', 'fall'],
        colorFamily: 'warm',
        isPopular: true
      }
    }),
    prisma.color.create({
      data: {
        name: 'Ocean Blue',
        hexCode: '#0077BE',
        moodTags: ['calming', 'trustworthy', 'professional', 'serene'],
        seasonTags: ['spring', 'summer'],
        colorFamily: 'cool'
      }
    }),
    prisma.color.create({
      data: {
        name: 'Forest Green',
        hexCode: '#228B22',
        moodTags: ['natural', 'grounding', 'peaceful', 'growth'],
        seasonTags: ['spring', 'fall'],
        colorFamily: 'earth'
      }
    }),
    prisma.color.create({
      data: {
        name: 'Royal Purple',
        hexCode: '#6A0DAD',
        moodTags: ['luxurious', 'creative', 'mysterious', 'regal'],
        seasonTags: ['fall', 'winter'],
        colorFamily: 'cool'
      }
    }),
    prisma.color.create({
      data: {
        name: 'Warm Gray',
        hexCode: '#8B8680',
        moodTags: ['versatile', 'sophisticated', 'balanced', 'modern'],
        seasonTags: ['spring', 'summer', 'fall', 'winter'],
        colorFamily: 'neutral'
      }
    }),
    prisma.color.create({
      data: {
        name: 'Blush Pink',
        hexCode: '#FFB6C1',
        moodTags: ['gentle', 'romantic', 'soft', 'feminine'],
        seasonTags: ['spring', 'summer'],
        colorFamily: 'warm'
      }
    })
  ]);

  // Seed Fabrics with emotional appeal
  console.log('🧵 Seeding fabrics...');
  const fabrics = await Promise.all([
    prisma.fabric.create({
      data: {
        name: 'Organic Cotton Blend',
        description: 'Sustainably sourced organic cotton that feels like a gentle hug against your skin',
        feelTags: ['soft', 'breathable', 'comfortable', 'natural'],
        careTags: ['easy-care', 'machine-washable', 'sustainable'],
        composition: '95% Organic Cotton, 5% Elastane',
        weight: 180,
        stretch: '2-way stretch',
        priceModifier: 0
      }
    }),
    prisma.fabric.create({
      data: {
        name: 'Premium Modal',
        description: 'Luxuriously smooth modal fabric that drapes beautifully and feels incredibly soft',
        feelTags: ['luxurious', 'silky', 'draping', 'premium'],
        careTags: ['wrinkle-resistant', 'color-retention', 'sustainable'],
        composition: '100% Modal',
        weight: 160,
        stretch: 'no stretch',
        priceModifier: 15.00
      }
    }),
    prisma.fabric.create({
      data: {
        name: 'Performance Blend',
        description: 'High-tech fabric that moves with you, perfect for active lifestyles',
        feelTags: ['moisture-wicking', 'stretchy', 'durable', 'athletic'],
        careTags: ['quick-dry', 'odor-resistant', 'machine-washable'],
        composition: '88% Polyester, 12% Spandex',
        weight: 200,
        stretch: '4-way stretch',
        priceModifier: 10.00
      }
    }),
    prisma.fabric.create({
      data: {
        name: 'Bamboo Silk',
        description: 'Eco-friendly bamboo silk that combines sustainability with incredible softness',
        feelTags: ['eco-friendly', 'silky', 'temperature-regulating', 'hypoallergenic'],
        careTags: ['sustainable', 'antibacterial', 'UV-protective'],
        composition: '70% Bamboo, 30% Silk',
        weight: 140,
        stretch: 'slight stretch',
        priceModifier: 25.00
      }
    })
  ]);

  // Seed Products with lifestyle imagery
  console.log('👕 Seeding products...');
  const products = await Promise.all([
    prisma.product.create({
      data: {
        name: 'Essential Crew Tee',
        description: 'The perfect foundation for any wardrobe. This versatile crew neck tee is designed to make you feel confident and comfortable in any setting.',
        category: 't-shirt',
        subcategory: 'crew-neck',
        moodTags: ['confident', 'versatile', 'comfortable', 'timeless'],
        lifestyleTags: ['casual', 'workwear', 'weekend', 'layering'],
        heroImage: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=800',
        lifestyleImages: [
          {
            url: 'https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=800',
            context: 'street',
            mood: 'confident',
            model: 'diverse'
          },
          {
            url: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=800',
            context: 'work',
            mood: 'professional',
            model: 'business'
          }
        ],
        basePrice: 29.99,
        isFeatured: true
      }
    }),
    prisma.product.create({
      data: {
        name: 'Urban Hoodie',
        description: 'Express your street style with this premium hoodie. Designed for those who dare to stand out and make a statement.',
        category: 'hoodie',
        subcategory: 'pullover',
        moodTags: ['bold', 'urban', 'expressive', 'cozy'],
        lifestyleTags: ['streetwear', 'casual', 'athleisure', 'weekend'],
        heroImage: 'https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=800',
        lifestyleImages: [
          {
            url: 'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=800',
            context: 'urban',
            mood: 'edgy',
            model: 'street'
          },
          {
            url: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800',
            context: 'casual',
            mood: 'relaxed',
            model: 'lifestyle'
          }
        ],
        basePrice: 59.99,
        isFeatured: true
      }
    }),
    prisma.product.create({
      data: {
        name: 'Flow Tank Top',
        description: 'Move freely and feel amazing in this breathable tank top. Perfect for workouts, yoga, or just feeling your best.',
        category: 'tank-top',
        subcategory: 'athletic',
        moodTags: ['energetic', 'free', 'strong', 'active'],
        lifestyleTags: ['fitness', 'yoga', 'athleisure', 'summer'],
        heroImage: 'https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=800',
        lifestyleImages: [
          {
            url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800',
            context: 'gym',
            mood: 'powerful',
            model: 'fitness'
          },
          {
            url: 'https://images.unsplash.com/photo-1506629905607-d9c297d3d45f?w=800',
            context: 'outdoor',
            mood: 'free',
            model: 'active'
          }
        ],
        basePrice: 24.99
      }
    }),
    prisma.product.create({
      data: {
        name: 'Elegant Midi Dress',
        description: 'Embrace your feminine power with this flowing midi dress. Designed to make you feel graceful and confident for any occasion.',
        category: 'dress',
        subcategory: 'midi',
        moodTags: ['elegant', 'feminine', 'graceful', 'sophisticated'],
        lifestyleTags: ['formal', 'date-night', 'work', 'special-occasion'],
        heroImage: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=800',
        lifestyleImages: [
          {
            url: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=800',
            context: 'office',
            mood: 'professional',
            model: 'business'
          },
          {
            url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=800',
            context: 'evening',
            mood: 'romantic',
            model: 'elegant'
          }
        ],
        basePrice: 79.99,
        isFeatured: true
      }
    })
  ]);

  // Create Product Variants
  console.log('🔄 Creating product variants...');
  const variants = [];
  for (const product of products) {
    for (const size of sizes) {
      for (const color of colors.slice(0, 4)) { // Use first 4 colors for each product
        const variant = await prisma.productVariant.create({
          data: {
            sku: `${product.id.slice(-6)}-${size.name}-${color.name.replace(/\s+/g, '').toUpperCase()}`,
            productId: product.id,
            sizeId: size.id,
            colorId: color.id,
            fabricId: fabrics[0].id, // Default to organic cotton
            stock: Math.floor(Math.random() * 50) + 10 // Random stock between 10-60
          }
        });
        variants.push(variant);
      }
    }
  }

  // Seed Templates with emotional styling
  console.log('🎨 Seeding design templates...');
  const templates = await Promise.all([
    prisma.template.create({
      data: {
        name: 'Bold Streetwear',
        description: 'Make a statement with bold graphics and urban edge. Perfect for those who want to stand out and express their rebellious spirit.',
        moodTag: 'Bold Streetwear',
        styleKeywords: ['urban', 'edgy', 'confident', 'rebellious'],
        targetAudience: 'young creatives and urban trendsetters',
        designData: {
          elements: [
            { type: 'text', content: 'STREET', style: 'bold', position: { x: 50, y: 100 } },
            { type: 'shape', shape: 'rectangle', color: '#FF6B35', position: { x: 20, y: 80 } }
          ]
        },
        previewImage: 'https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=400',
        lifestyleContext: ['street', 'casual', 'nightlife', 'creative'],
        productId: products[0].id, // Essential Crew Tee
        isFeatured: true
      }
    }),
    prisma.template.create({
      data: {
        name: 'Minimalist Chic',
        description: 'Less is more. Clean lines and subtle elegance for the sophisticated minimalist who values quality over quantity.',
        moodTag: 'Minimalist Chic',
        styleKeywords: ['clean', 'sophisticated', 'timeless', 'elegant'],
        targetAudience: 'professionals and minimalist enthusiasts',
        designData: {
          elements: [
            { type: 'text', content: 'MINIMAL', style: 'thin', position: { x: 100, y: 150 } },
            { type: 'line', color: '#000000', position: { x: 50, y: 200 } }
          ]
        },
        previewImage: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400',
        lifestyleContext: ['work', 'formal', 'everyday', 'professional'],
        productId: products[0].id,
        isFeatured: true
      }
    }),
    prisma.template.create({
      data: {
        name: 'Vintage Rebel',
        description: 'Channel your inner rebel with vintage-inspired designs that tell a story of freedom and authenticity.',
        moodTag: 'Vintage Rebel',
        styleKeywords: ['vintage', 'rebellious', 'authentic', 'nostalgic'],
        targetAudience: 'free spirits and vintage lovers',
        designData: {
          elements: [
            { type: 'text', content: 'REBEL', style: 'vintage', position: { x: 75, y: 120 } },
            { type: 'image', src: 'vintage-badge', position: { x: 30, y: 50 } }
          ]
        },
        previewImage: 'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=400',
        lifestyleContext: ['casual', 'weekend', 'music', 'artistic'],
        productId: products[1].id // Urban Hoodie
      }
    }),
    prisma.template.create({
      data: {
        name: 'Nature Lover',
        description: 'Connect with nature through earthy designs that celebrate the beauty of the natural world.',
        moodTag: 'Nature Lover',
        styleKeywords: ['natural', 'earthy', 'peaceful', 'organic'],
        targetAudience: 'outdoor enthusiasts and eco-conscious individuals',
        designData: {
          elements: [
            { type: 'text', content: 'WILD & FREE', style: 'organic', position: { x: 60, y: 110 } },
            { type: 'shape', shape: 'leaf', color: '#228B22', position: { x: 40, y: 60 } }
          ]
        },
        previewImage: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400',
        lifestyleContext: ['outdoor', 'casual', 'weekend', 'travel'],
        productId: products[2].id // Flow Tank Top
      }
    }),
    prisma.template.create({
      data: {
        name: 'Fitness Warrior',
        description: 'Unleash your inner warrior with powerful designs that motivate and inspire your fitness journey.',
        moodTag: 'Fitness Warrior',
        styleKeywords: ['powerful', 'motivational', 'strong', 'athletic'],
        targetAudience: 'fitness enthusiasts and athletes',
        designData: {
          elements: [
            { type: 'text', content: 'NO LIMITS', style: 'bold', position: { x: 70, y: 100 } },
            { type: 'shape', shape: 'lightning', color: '#FF6B35', position: { x: 120, y: 80 } }
          ]
        },
        previewImage: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
        lifestyleContext: ['gym', 'sports', 'fitness', 'motivation'],
        productId: products[2].id // Flow Tank Top
      }
    }),
    prisma.template.create({
      data: {
        name: 'Romantic Dreamer',
        description: 'Embrace your feminine side with soft, romantic designs that celebrate love and beauty.',
        moodTag: 'Romantic Dreamer',
        styleKeywords: ['romantic', 'feminine', 'soft', 'dreamy'],
        targetAudience: 'romantic souls and feminine fashion lovers',
        designData: {
          elements: [
            { type: 'text', content: 'DREAM', style: 'script', position: { x: 80, y: 130 } },
            { type: 'shape', shape: 'heart', color: '#FFB6C1', position: { x: 60, y: 70 } }
          ]
        },
        previewImage: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=400',
        lifestyleContext: ['date-night', 'formal', 'romantic', 'special-occasion'],
        productId: products[3].id // Elegant Midi Dress
      }
    }),
    prisma.template.create({
      data: {
        name: 'Tech Innovator',
        description: 'Futuristic designs for the tech-savvy individual who lives on the cutting edge of innovation.',
        moodTag: 'Tech Innovator',
        styleKeywords: ['futuristic', 'innovative', 'tech', 'modern'],
        targetAudience: 'tech professionals and innovation enthusiasts',
        designData: {
          elements: [
            { type: 'text', content: 'INNOVATE', style: 'modern', position: { x: 65, y: 110 } },
            { type: 'shape', shape: 'circuit', color: '#0077BE', position: { x: 40, y: 60 } }
          ]
        },
        previewImage: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400',
        lifestyleContext: ['work', 'tech', 'professional', 'modern'],
        productId: products[0].id // Essential Crew Tee
      }
    }),
    prisma.template.create({
      data: {
        name: 'Artistic Soul',
        description: 'Express your creativity with artistic designs that celebrate the beauty of self-expression.',
        moodTag: 'Artistic Soul',
        styleKeywords: ['creative', 'artistic', 'expressive', 'unique'],
        targetAudience: 'artists and creative professionals',
        designData: {
          elements: [
            { type: 'text', content: 'CREATE', style: 'artistic', position: { x: 75, y: 120 } },
            { type: 'shape', shape: 'brush-stroke', color: '#6A0DAD', position: { x: 50, y: 80 } }
          ]
        },
        previewImage: 'https://images.unsplash.com/photo-1506629905607-d9c297d3d45f?w=400',
        lifestyleContext: ['creative', 'artistic', 'casual', 'expressive'],
        productId: products[1].id // Urban Hoodie
      }
    })
  ]);

  // Seed Price Rules
  console.log('💰 Seeding price rules...');
  const priceRules = await Promise.all([
    prisma.priceRule.create({
      data: {
        name: 'Premium Fabric Surcharge',
        description: 'Additional cost for premium fabrics like Modal and Bamboo Silk',
        conditions: {
          fabricTypes: ['Premium Modal', 'Bamboo Silk']
        },
        modifier: 15.00,
        modifierType: 'fixed',
        priority: 1
      }
    }),
    prisma.priceRule.create({
      data: {
        name: 'Bulk Order Discount',
        description: '10% discount for orders of 5 or more items',
        conditions: {
          minQuantity: 5
        },
        modifier: -10.00,
        modifierType: 'percentage',
        priority: 2
      }
    }),
    prisma.priceRule.create({
      data: {
        name: 'Custom Design Premium',
        description: 'Additional charge for highly customized designs',
        conditions: {
          customizationComplexity: 'high'
        },
        modifier: 20.00,
        modifierType: 'fixed',
        priority: 3
      }
    })
  ]);

  console.log('✅ Database seeded successfully!');
  console.log(`📏 Created ${sizes.length} sizes`);
  console.log(`🎨 Created ${colors.length} colors`);
  console.log(`🧵 Created ${fabrics.length} fabrics`);
  console.log(`👕 Created ${products.length} products`);
  console.log(`🔄 Created ${variants.length} product variants`);
  console.log(`🎨 Created ${templates.length} design templates`);
  console.log(`💰 Created ${priceRules.length} price rules`);
  console.log('🌟 Ottiq database is ready to inspire fashion dreams!');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('❌ Error seeding database:', e);
    await prisma.$disconnect();
    process.exit(1);
  });
