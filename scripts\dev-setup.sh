#!/bin/bash

# Ottiq Development Setup Script
echo "🚀 Setting up Ottiq development environment..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Copy environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. Please update it with your configuration."
fi

# Start Docker services
echo "🐳 Starting Docker services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check if PostgreSQL is ready
echo "🔍 Checking PostgreSQL connection..."
until docker-compose exec postgres pg_isready -U ottiq_user -d ottiq_dev; do
    echo "Waiting for PostgreSQL..."
    sleep 2
done

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npm run db:generate

# Push database schema
echo "📊 Pushing database schema..."
npm run db:push

echo "✅ Development environment setup complete!"
echo ""
echo "🌐 Services available at:"
echo "  - Application: http://localhost:3000"
echo "  - PostgreSQL: localhost:5432"
echo "  - MinIO Console: http://localhost:9001"
echo "  - Redis: localhost:6379"
echo "  - Mailhog: http://localhost:8025"
echo ""
echo "🚀 Run 'npm run dev' to start the development server"
