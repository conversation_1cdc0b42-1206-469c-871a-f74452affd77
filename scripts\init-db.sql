-- Initialize Ottiq Database
-- This script runs when the PostgreSQL container starts for the first time

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create initial schemas if needed
-- (<PERSON><PERSON><PERSON> will handle the actual table creation)

-- Set up initial configuration
DO $$
BEGIN
    -- Log initialization
    RAISE NOTICE 'Ottiq database initialized successfully';
END $$;
