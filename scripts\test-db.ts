import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testDatabase() {
  console.log('🧪 Testing database connection and schema...');

  try {
    // Test basic connection
    await prisma.$connect();
    console.log('✅ Database connection successful');

    // Test if we can query the basic tables
    const sizeCount = await prisma.size.count();
    const colorCount = await prisma.color.count();
    const fabricCount = await prisma.fabric.count();
    const productCount = await prisma.product.count();
    const templateCount = await prisma.template.count();

    console.log('📊 Database Statistics:');
    console.log(`   Sizes: ${sizeCount}`);
    console.log(`   Colors: ${colorCount}`);
    console.log(`   Fabrics: ${fabricCount}`);
    console.log(`   Products: ${productCount}`);
    console.log(`   Templates: ${templateCount}`);

    // Test complex query with relations
    if (productCount > 0) {
      const productWithDetails = await prisma.product.findFirst({
        include: {
          variants: {
            include: {
              size: true,
              color: true,
              fabric: true
            }
          },
          templates: true
        }
      });

      if (productWithDetails) {
        console.log(`🔍 Sample Product: ${productWithDetails.name}`);
        console.log(`   Variants: ${productWithDetails.variants.length}`);
        console.log(`   Templates: ${productWithDetails.templates.length}`);
        console.log(`   Mood Tags: ${productWithDetails.moodTags.join(', ')}`);
      }
    }

    console.log('✅ Database schema test completed successfully!');
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testDatabase()
  .then(() => {
    console.log('🎉 All database tests passed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Database tests failed:', error);
    process.exit(1);
  });
