#!/usr/bin/env tsx

/**
 * Comprehensive Pricing Engine Test Script
 * Tests all pricing scenarios and validates emotional messaging
 */

import { PricingEngine } from '../src/lib/pricing/engine';
import { PricingRequest } from '../src/types/pricing';
import { PricingFormatter } from '../src/lib/pricing/utils';

// Test scenarios with emotional context
const TEST_SCENARIOS: Array<{
  name: string;
  description: string;
  request: PricingRequest;
  expectedBehavior: string;
}> = [
  {
    name: 'Basic T-Shirt Pricing',
    description: 'Standard t-shirt with default options',
    request: {
      productId: 'test-product-1',
      quantity: 1,
      printSize: 'medium',
      qualityTier: 'standard'
    },
    expectedBehavior: 'Should return base price with emotional value messaging'
  },
  {
    name: 'Premium Fabric Upgrade',
    description: 'T-shirt with premium fabric upgrade',
    request: {
      productId: 'test-product-1',
      quantity: 1,
      fabricId: 'premium-modal',
      printSize: 'medium',
      qualityTier: 'premium'
    },
    expectedBehavior: 'Should include fabric surcharge with luxury messaging'
  },
  {
    name: 'Large Print Bold Statement',
    description: 'Large print for maximum impact',
    request: {
      productId: 'test-product-1',
      quantity: 1,
      printSize: 'large',
      qualityTier: 'standard'
    },
    expectedBehavior: 'Should include print size cost with confidence messaging'
  },
  {
    name: 'Bulk Order Savings',
    description: 'Multiple items for bulk discount',
    request: {
      productId: 'test-product-1',
      quantity: 6,
      printSize: 'medium',
      qualityTier: 'standard'
    },
    expectedBehavior: 'Should apply quantity discount with savings messaging'
  },
  {
    name: 'Luxury Full Experience',
    description: 'Premium everything - luxury tier with full coverage',
    request: {
      productId: 'test-product-1',
      quantity: 1,
      fabricId: 'bamboo-silk',
      printSize: 'full_coverage',
      qualityTier: 'luxury'
    },
    expectedBehavior: 'Should reflect luxury pricing with premium value messaging'
  },
  {
    name: 'Edge Case - Maximum Quantity',
    description: 'Testing maximum allowed quantity',
    request: {
      productId: 'test-product-1',
      quantity: 100,
      printSize: 'small',
      qualityTier: 'standard'
    },
    expectedBehavior: 'Should handle large quantities without errors'
  }
];

// Mock product data for testing
const MOCK_PRODUCTS = {
  'test-product-1': {
    id: 'test-product-1',
    name: 'Essential Crew Tee',
    basePrice: 29.99,
    variants: [
      {
        fabricId: 'premium-modal',
        fabric: {
          name: 'Premium Modal',
          priceModifier: 15.00,
          feelTags: ['luxurious', 'silky', 'draping'],
          careTags: ['wrinkle-resistant', 'color-retention']
        }
      },
      {
        fabricId: 'bamboo-silk',
        fabric: {
          name: 'Bamboo Silk',
          priceModifier: 25.00,
          feelTags: ['eco-friendly', 'silky', 'temperature-regulating'],
          careTags: ['sustainable', 'antibacterial']
        }
      }
    ]
  }
};

class PricingTester {
  private pricingEngine: PricingEngine;
  private results: Array<{
    scenario: string;
    success: boolean;
    result?: any;
    error?: string;
    emotionalScore: number;
    transparencyScore: number;
  }> = [];

  constructor() {
    this.pricingEngine = PricingEngine.getInstance();
  }

  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Pricing Engine Tests...\n');

    for (const scenario of TEST_SCENARIOS) {
      await this.runScenarioTest(scenario);
    }

    this.generateReport();
  }

  private async runScenarioTest(scenario: typeof TEST_SCENARIOS[0]): Promise<void> {
    console.log(`📋 Testing: ${scenario.name}`);
    console.log(`   ${scenario.description}`);

    try {
      // Mock the product data (in real implementation, this would come from database)
      this.mockProductData(scenario.request.productId);

      const result = await this.pricingEngine.calculatePrice(scenario.request);

      if (result.success && result.data) {
        const emotionalScore = this.evaluateEmotionalMessaging(result.data);
        const transparencyScore = this.evaluateTransparency(result.data);

        this.results.push({
          scenario: scenario.name,
          success: true,
          result: result.data,
          emotionalScore,
          transparencyScore
        });

        console.log(`   ✅ Success - Total: ${PricingFormatter.formatCurrency(result.data.totalPrice)}`);
        console.log(`   💝 Emotional Score: ${emotionalScore}/10`);
        console.log(`   🔍 Transparency Score: ${transparencyScore}/10`);
        console.log(`   💬 Value Message: "${result.data.valueMessage}"`);
        
        if (result.data.savingsMessage) {
          console.log(`   💰 Savings: "${result.data.savingsMessage}"`);
        }
      } else {
        this.results.push({
          scenario: scenario.name,
          success: false,
          error: result.error,
          emotionalScore: 0,
          transparencyScore: 0
        });

        console.log(`   ❌ Failed: ${result.error}`);
      }
    } catch (error) {
      this.results.push({
        scenario: scenario.name,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        emotionalScore: 0,
        transparencyScore: 0
      });

      console.log(`   💥 Error: ${error}`);
    }

    console.log(''); // Empty line for readability
  }

  private mockProductData(productId: string): void {
    // In a real implementation, this would be handled by the database
    // For testing, we'll assume the pricing engine can access this mock data
    const product = MOCK_PRODUCTS[productId as keyof typeof MOCK_PRODUCTS];
    if (!product) {
      throw new Error(`Mock product ${productId} not found`);
    }
  }

  private evaluateEmotionalMessaging(data: any): number {
    let score = 0;

    // Check for emotional value message (0-3 points)
    if (data.valueMessage) {
      if (data.valueMessage.includes('style') || data.valueMessage.includes('experience')) score += 1;
      if (data.valueMessage.includes('exceptional') || data.valueMessage.includes('premium')) score += 1;
      if (data.valueMessage.length > 20) score += 1; // Detailed message
    }

    // Check for quality promise (0-2 points)
    if (data.qualityPromise) {
      if (data.qualityPromise.includes('craftsmanship') || data.qualityPromise.includes('quality')) score += 1;
      if (data.qualityPromise.length > 30) score += 1; // Detailed promise
    }

    // Check breakdown messaging (0-3 points)
    if (data.breakdown && data.breakdown.length > 0) {
      const hasEmotionalMessages = data.breakdown.some((item: any) => 
        item.valueMessage && item.valueMessage.length > 10
      );
      if (hasEmotionalMessages) score += 2;
      
      const hasIcons = data.breakdown.some((item: any) => item.icon);
      if (hasIcons) score += 1;
    }

    // Check for savings messaging (0-2 points)
    if (data.savingsMessage) {
      score += 2;
    }

    return Math.min(score, 10);
  }

  private evaluateTransparency(data: any): number {
    let score = 0;

    // Check for detailed breakdown (0-4 points)
    if (data.breakdown && data.breakdown.length > 0) {
      score += 2; // Has breakdown
      if (data.breakdown.length > 1) score += 1; // Multiple components
      if (data.breakdown.every((item: any) => item.description)) score += 1; // All have descriptions
    }

    // Check for price justification (0-3 points)
    if (data.priceJustification && data.priceJustification.length > 0) {
      score += 1;
      if (data.priceJustification.length >= 3) score += 1; // Multiple justifications
      if (data.priceJustification.some((j: string) => j.includes('sustainable') || j.includes('ethical'))) score += 1;
    }

    // Check for metadata (0-2 points)
    if (data.calculatedAt && data.validUntil) score += 1;
    if (data.priceId) score += 1;

    // Check for currency specification (0-1 point)
    if (data.currency) score += 1;

    return Math.min(score, 10);
  }

  private generateReport(): void {
    console.log('📊 PRICING ENGINE TEST REPORT');
    console.log('================================\n');

    const successfulTests = this.results.filter(r => r.success);
    const failedTests = this.results.filter(r => !r.success);

    console.log(`✅ Successful Tests: ${successfulTests.length}/${this.results.length}`);
    console.log(`❌ Failed Tests: ${failedTests.length}/${this.results.length}`);

    if (successfulTests.length > 0) {
      const avgEmotionalScore = successfulTests.reduce((sum, r) => sum + r.emotionalScore, 0) / successfulTests.length;
      const avgTransparencyScore = successfulTests.reduce((sum, r) => sum + r.transparencyScore, 0) / successfulTests.length;

      console.log(`\n💝 Average Emotional Score: ${avgEmotionalScore.toFixed(1)}/10`);
      console.log(`🔍 Average Transparency Score: ${avgTransparencyScore.toFixed(1)}/10`);

      // Recommendations
      console.log('\n🎯 RECOMMENDATIONS:');
      if (avgEmotionalScore < 7) {
        console.log('   • Enhance emotional messaging in value propositions');
        console.log('   • Add more lifestyle-focused language');
      }
      if (avgTransparencyScore < 8) {
        console.log('   • Improve price breakdown detail');
        console.log('   • Add more justification points');
      }
    }

    if (failedTests.length > 0) {
      console.log('\n❌ FAILED TESTS:');
      failedTests.forEach(test => {
        console.log(`   • ${test.scenario}: ${test.error}`);
      });
    }

    console.log('\n🎉 Test completed!');
  }
}

// Run the tests
async function main() {
  const tester = new PricingTester();
  await tester.runAllTests();
}

if (require.main === module) {
  main().catch(console.error);
}
