// Schema validation script - validates Prisma schema without database connection
import fs from 'fs';
import path from 'path';

function validateSchema() {
  console.log('🔍 Validating Prisma schema...');

  const schemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma');
  
  if (!fs.existsSync(schemaPath)) {
    throw new Error('❌ Prisma schema file not found');
  }

  const schemaContent = fs.readFileSync(schemaPath, 'utf-8');
  
  // Check for required models
  const requiredModels = [
    'User', 'Product', 'ProductVariant', 'Fabric', 'Color', 'Size',
    'Template', 'Customization', 'PriceRule', 'Order', 'OrderItem', 'AiTryOnJob'
  ];

  const missingModels = [];
  
  for (const model of requiredModels) {
    if (!schemaContent.includes(`model ${model}`)) {
      missingModels.push(model);
    }
  }

  if (missingModels.length > 0) {
    throw new Error(`❌ Missing required models: ${missingModels.join(', ')}`);
  }

  // Check for emotional appeal fields
  const emotionalFields = [
    'moodTags', 'lifestyleTags', 'lifestyleImages', 'styleKeywords', 'targetAudience'
  ];

  const foundEmotionalFields = [];
  for (const field of emotionalFields) {
    if (schemaContent.includes(field)) {
      foundEmotionalFields.push(field);
    }
  }

  console.log('✅ Schema validation passed!');
  console.log(`📋 Found ${requiredModels.length} required models`);
  console.log(`💝 Found ${foundEmotionalFields.length} emotional appeal fields: ${foundEmotionalFields.join(', ')}`);
  
  // Check seed file exists
  const seedPath = path.join(process.cwd(), 'prisma', 'seed.ts');
  if (fs.existsSync(seedPath)) {
    console.log('✅ Seed file found');
    
    const seedContent = fs.readFileSync(seedPath, 'utf-8');
    
    // Check for emotional templates
    const templateMoods = [
      'Bold Streetwear', 'Minimalist Chic', 'Vintage Rebel', 'Nature Lover',
      'Fitness Warrior', 'Romantic Dreamer', 'Tech Innovator', 'Artistic Soul'
    ];
    
    const foundMoods = [];
    for (const mood of templateMoods) {
      if (seedContent.includes(mood)) {
        foundMoods.push(mood);
      }
    }
    
    console.log(`🎨 Found ${foundMoods.length} emotional template moods: ${foundMoods.join(', ')}`);
  } else {
    console.log('⚠️  Seed file not found');
  }

  console.log('🎉 Schema and seed validation completed successfully!');
}

// Run validation
try {
  validateSchema();
  process.exit(0);
} catch (error) {
  console.error('💥 Validation failed:', error);
  process.exit(1);
}
