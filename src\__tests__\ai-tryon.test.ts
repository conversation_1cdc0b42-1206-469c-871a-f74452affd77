/**
 * AI Try-On Tests
 * 
 * Unit and integration tests for AI try-on functionality
 */

import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { NextRequest } from 'next/server';
import { POST, GET } from '@/app/api/ai-tryon/create/route';
import { huggingFaceService } from '@/lib/services/huggingface';
import { parseBase64Image, validatePortraitImage } from '@/lib/utils/imageUpload';

// Mock dependencies
jest.mock('@/lib/services/huggingface');
jest.mock('@/lib/utils/imageUpload');
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    user: {
      findUnique: jest.fn(),
    },
    customization: {
      findUnique: jest.fn(),
    },
    aiTryOnJob: {
      create: jest.fn(),
      update: jest.fn(),
      findUnique: jest.fn(),
    },
  })),
}));

const mockHuggingFaceService = huggingFaceService as jest.Mocked<typeof huggingFaceService>;
const mockParseBase64Image = parseBase64Image as jest.MockedFunction<typeof parseBase64Image>;
const mockValidatePortraitImage = validatePortraitImage as jest.MockedFunction<typeof validatePortraitImage>;

// Test data
const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
  name: 'Test User',
};

const mockCustomization = {
  id: 'custom-123',
  name: 'Test Design',
  userId: 'user-123',
  previewImage: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
  product: {
    id: 'product-123',
    name: 'Test T-Shirt',
    category: 't-shirt',
    subcategory: 'fitted',
  },
};

const mockBase64Image = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=';

describe('AI Try-On API', () => {
  let mockPrisma: any;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Setup Prisma mock
    const { PrismaClient } = require('@prisma/client');
    mockPrisma = new PrismaClient();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('POST /api/ai-tryon/create', () => {
    it('should create AI try-on job successfully with JSON body', async () => {
      // Setup mocks
      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.customization.findUnique.mockResolvedValue(mockCustomization);
      mockPrisma.aiTryOnJob.create.mockResolvedValue({
        id: 'job-123',
        status: 'PENDING',
        userPhotoUrl: mockBase64Image,
        customizationId: 'custom-123',
        userId: 'user-123',
      });
      mockPrisma.aiTryOnJob.update.mockResolvedValue({
        id: 'job-123',
        status: 'COMPLETED',
        resultImageUrl: mockBase64Image,
        confidence: 0.85,
        processingTime: 30,
      });

      mockParseBase64Image.mockResolvedValue({
        success: true,
        data: {
          originalBuffer: Buffer.from('test'),
          processedBuffer: Buffer.from('test'),
          metadata: {
            width: 512,
            height: 512,
            format: 'jpeg',
            size: 1000,
            processedSize: 800,
          },
          base64DataUrl: mockBase64Image,
          mimeType: 'image/jpeg',
        },
      });

      mockHuggingFaceService.generateTryOnWithRetry.mockResolvedValue({
        success: true,
        result_image: mockBase64Image,
        processing_time: 30,
      });

      // Create request
      const requestBody = {
        userPhotoUrl: mockBase64Image,
        customizationId: 'custom-123',
        userId: 'user-123',
        contextScene: 'outdoor',
      };

      const request = new NextRequest('http://localhost/api/ai-tryon/create', {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      // Execute
      const response = await POST(request);
      const data = await response.json();

      // Assertions
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.jobId).toBe('job-123');
      expect(data.data.status).toBe('COMPLETED');
      expect(data.data.resultImageUrl).toBe(mockBase64Image);
      expect(data.data.confidence).toBe(0.85);

      // Verify database calls
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { id: 'user-123' },
      });
      expect(mockPrisma.customization.findUnique).toHaveBeenCalledWith({
        where: { id: 'custom-123' },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              category: true,
              subcategory: true,
            },
          },
        },
      });
      expect(mockPrisma.aiTryOnJob.create).toHaveBeenCalled();
      expect(mockPrisma.aiTryOnJob.update).toHaveBeenCalledTimes(2); // PROCESSING and COMPLETED
    });

    it('should return error for invalid user', async () => {
      // Setup mocks
      mockPrisma.user.findUnique.mockResolvedValue(null);
      mockParseBase64Image.mockResolvedValue({
        success: true,
        data: {
          originalBuffer: Buffer.from('test'),
          processedBuffer: Buffer.from('test'),
          metadata: {
            width: 512,
            height: 512,
            format: 'jpeg',
            size: 1000,
            processedSize: 800,
          },
          base64DataUrl: mockBase64Image,
          mimeType: 'image/jpeg',
        },
      });

      const requestBody = {
        userPhotoUrl: mockBase64Image,
        customizationId: 'custom-123',
        userId: 'invalid-user',
      };

      const request = new NextRequest('http://localhost/api/ai-tryon/create', {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      // Execute
      const response = await POST(request);
      const data = await response.json();

      // Assertions
      expect(response.status).toBe(404);
      expect(data.success).toBe(false);
      expect(data.error).toBe('User not found');
    });

    it('should return error for invalid customization', async () => {
      // Setup mocks
      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.customization.findUnique.mockResolvedValue(null);
      mockParseBase64Image.mockResolvedValue({
        success: true,
        data: {
          originalBuffer: Buffer.from('test'),
          processedBuffer: Buffer.from('test'),
          metadata: {
            width: 512,
            height: 512,
            format: 'jpeg',
            size: 1000,
            processedSize: 800,
          },
          base64DataUrl: mockBase64Image,
          mimeType: 'image/jpeg',
        },
      });

      const requestBody = {
        userPhotoUrl: mockBase64Image,
        customizationId: 'invalid-custom',
        userId: 'user-123',
      };

      const request = new NextRequest('http://localhost/api/ai-tryon/create', {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      // Execute
      const response = await POST(request);
      const data = await response.json();

      // Assertions
      expect(response.status).toBe(404);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Customization not found');
    });

    it('should handle Hugging Face service failure', async () => {
      // Setup mocks
      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.customization.findUnique.mockResolvedValue(mockCustomization);
      mockPrisma.aiTryOnJob.create.mockResolvedValue({
        id: 'job-123',
        status: 'PENDING',
        userPhotoUrl: mockBase64Image,
        customizationId: 'custom-123',
        userId: 'user-123',
      });
      mockPrisma.aiTryOnJob.update.mockResolvedValue({
        id: 'job-123',
        status: 'FAILED',
        errorMessage: 'AI service error',
      });

      mockParseBase64Image.mockResolvedValue({
        success: true,
        data: {
          originalBuffer: Buffer.from('test'),
          processedBuffer: Buffer.from('test'),
          metadata: {
            width: 512,
            height: 512,
            format: 'jpeg',
            size: 1000,
            processedSize: 800,
          },
          base64DataUrl: mockBase64Image,
          mimeType: 'image/jpeg',
        },
      });

      mockHuggingFaceService.generateTryOnWithRetry.mockResolvedValue({
        success: false,
        error: 'AI service error',
        processing_time: 5,
      });

      const requestBody = {
        userPhotoUrl: mockBase64Image,
        customizationId: 'custom-123',
        userId: 'user-123',
      };

      const request = new NextRequest('http://localhost/api/ai-tryon/create', {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      // Execute
      const response = await POST(request);
      const data = await response.json();

      // Assertions
      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('AI try-on generation failed');
      expect(data.details).toBe('AI service error');
    });
  });

  describe('GET /api/ai-tryon/create', () => {
    it('should return job status successfully', async () => {
      const mockJob = {
        id: 'job-123',
        status: 'COMPLETED',
        resultImageUrl: mockBase64Image,
        confidence: 0.85,
        customization: {
          id: 'custom-123',
          name: 'Test Design',
          product: {
            name: 'Test T-Shirt',
            category: 't-shirt',
          },
        },
        user: {
          id: 'user-123',
          name: 'Test User',
        },
      };

      mockPrisma.aiTryOnJob.findUnique.mockResolvedValue(mockJob);

      const request = new NextRequest('http://localhost/api/ai-tryon/create?jobId=job-123');

      // Execute
      const response = await GET(request);
      const data = await response.json();

      // Assertions
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.jobId).toBe('job-123');
      expect(data.data.status).toBe('COMPLETED');
      expect(data.data.resultImageUrl).toBe(mockBase64Image);
      expect(data.data.confidence).toBe(0.85);
    });

    it('should return error for missing job ID', async () => {
      const request = new NextRequest('http://localhost/api/ai-tryon/create');

      // Execute
      const response = await GET(request);
      const data = await response.json();

      // Assertions
      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Job ID is required');
    });

    it('should return error for non-existent job', async () => {
      mockPrisma.aiTryOnJob.findUnique.mockResolvedValue(null);

      const request = new NextRequest('http://localhost/api/ai-tryon/create?jobId=invalid-job');

      // Execute
      const response = await GET(request);
      const data = await response.json();

      // Assertions
      expect(response.status).toBe(404);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Job not found');
    });
  });
});

describe('Hugging Face Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generateTryOn', () => {
    it('should generate try-on successfully', async () => {
      const mockClient = {
        predict: jest.fn().mockResolvedValue({
          data: [mockBase64Image],
        }),
      };

      // Mock the Client.connect method
      const { Client } = require('@gradio/client');
      Client.connect = jest.fn().mockResolvedValue(mockClient);

      const service = new (require('@/lib/services/huggingface').HuggingFaceOOTDService)();

      const request = {
        person_image: mockBase64Image,
        garment_image: mockBase64Image,
        category: 'upper_body' as const,
        num_inference_steps: 20,
        guidance_scale: 2.0,
        seed: -1,
      };

      const result = await service.generateTryOn(request);

      expect(result.success).toBe(true);
      expect(result.result_image).toBe(mockBase64Image);
      expect(result.processing_time).toBeGreaterThan(0);
      expect(mockClient.predict).toHaveBeenCalledWith('/generate', request);
    });

    it('should handle validation errors', async () => {
      const service = new (require('@/lib/services/huggingface').HuggingFaceOOTDService)();

      const invalidRequest = {
        person_image: '',
        garment_image: mockBase64Image,
        category: 'upper_body' as const,
      };

      const result = await service.generateTryOn(invalidRequest);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Person image is required');
    });

    it('should handle service errors', async () => {
      const mockClient = {
        predict: jest.fn().mockRejectedValue(new Error('Service unavailable')),
      };

      const { Client } = require('@gradio/client');
      Client.connect = jest.fn().mockResolvedValue(mockClient);

      const service = new (require('@/lib/services/huggingface').HuggingFaceOOTDService)();

      const request = {
        person_image: mockBase64Image,
        garment_image: mockBase64Image,
        category: 'upper_body' as const,
      };

      const result = await service.generateTryOn(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Service unavailable');
    });
  });

  describe('generateTryOnWithRetry', () => {
    it('should retry on failure and succeed', async () => {
      const mockClient = {
        predict: jest.fn()
          .mockRejectedValueOnce(new Error('Temporary error'))
          .mockResolvedValueOnce({
            data: [mockBase64Image],
          }),
      };

      const { Client } = require('@gradio/client');
      Client.connect = jest.fn().mockResolvedValue(mockClient);

      const service = new (require('@/lib/services/huggingface').HuggingFaceOOTDService)();

      const request = {
        person_image: mockBase64Image,
        garment_image: mockBase64Image,
        category: 'upper_body' as const,
      };

      const result = await service.generateTryOnWithRetry(request, 2);

      expect(result.success).toBe(true);
      expect(result.result_image).toBe(mockBase64Image);
      expect(mockClient.predict).toHaveBeenCalledTimes(2);
    });

    it('should fail after max retries', async () => {
      const mockClient = {
        predict: jest.fn().mockRejectedValue(new Error('Persistent error')),
      };

      const { Client } = require('@gradio/client');
      Client.connect = jest.fn().mockResolvedValue(mockClient);

      const service = new (require('@/lib/services/huggingface').HuggingFaceOOTDService)();

      const request = {
        person_image: mockBase64Image,
        garment_image: mockBase64Image,
        category: 'upper_body' as const,
      };

      const result = await service.generateTryOnWithRetry(request, 2);

      expect(result.success).toBe(false);
      expect(result.error).toContain('All retry attempts failed');
      expect(mockClient.predict).toHaveBeenCalledTimes(2);
    });
  });
});
