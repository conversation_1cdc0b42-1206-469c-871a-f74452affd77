import { NextRequest } from 'next/server';
import { POST as previewPOST, GET as previewGET } from '@/app/api/customizations/preview/route';
import { POST as savePOST, GET as saveGET } from '@/app/api/customizations/save/route';
import { DesignCanvas } from '@/types';

// Mock dependencies
jest.mock('@/lib/utils/imageProcessing', () => ({
  generatePreviewImage: jest.fn().mockResolvedValue({
    buffer: Buffer.from('mock-image-data'),
    metadata: {
      width: 800,
      height: 600,
      format: 'png',
      size: 1024,
    },
  }),
  generateImageVariants: jest.fn().mockResolvedValue({
    preview: Buffer.from('mock-preview'),
    thumbnail: Buffer.from('mock-thumbnail'),
    optimized: Buffer.from('mock-optimized'),
    metadata: {
      preview: { width: 800, height: 600, format: 'png', size: 1024 },
      thumbnail: { width: 200, height: 200, format: 'jpeg', size: 256 },
      optimized: { width: 800, height: 600, format: 'webp', size: 512 },
    },
  }),
}));

jest.mock('@/lib/utils/areaCalculation', () => ({
  calculateCanvasPrintArea: jest.fn().mockReturnValue({
    totalArea: 50.5,
    elementAreas: [
      { id: 'text-1', area: 25.2, type: 'text' },
      { id: 'image-1', area: 25.3, type: 'image' },
    ],
    overlappingArea: 0,
    printSizeCategory: 'medium',
    isWithinLimits: true,
    recommendations: [],
  }),
  PRINT_SIZE_CATEGORIES: {
    small: { maxArea: 25, label: 'Small Print', description: 'Perfect for subtle details' },
    medium: { maxArea: 100, label: 'Medium Print', description: 'Great for statement pieces' },
    large: { maxArea: 250, label: 'Large Print', description: 'Bold and eye-catching' },
    full_coverage: { maxArea: 500, label: 'Full Coverage', description: 'Maximum impact design' },
  },
}));

jest.mock('@/lib/pricing/engine', () => ({
  PricingEngine: {
    getInstance: jest.fn().mockReturnValue({
      calculatePrice: jest.fn().mockResolvedValue({
        success: true,
        data: {
          totalPrice: 29.99,
          breakdown: [
            { name: 'Base Price', amount: 19.99 },
            { name: 'Print Cost', amount: 10.00 },
          ],
        },
      }),
    }),
  },
}));

jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    user: {
      findUnique: jest.fn().mockResolvedValue({
        id: 'user-1',
        name: 'Test User',
        email: '<EMAIL>',
      }),
    },
    product: {
      findUnique: jest.fn().mockResolvedValue({
        id: 'product-1',
        name: 'Test Product',
        category: 'T-Shirt',
        basePrice: 19.99,
      }),
    },
    template: {
      findUnique: jest.fn().mockResolvedValue({
        id: 'template-1',
        name: 'Test Template',
        moodTag: 'Bold',
      }),
      update: jest.fn().mockResolvedValue({}),
    },
    customization: {
      create: jest.fn().mockResolvedValue({
        id: 'customization-1',
        name: 'Test Design',
        description: 'Test Description',
        status: 'DRAFT',
        previewImage: 'data:image/png;base64,mock-data',
        createdAt: new Date(),
        updatedAt: new Date(),
        user: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
        },
        product: {
          id: 'product-1',
          name: 'Test Product',
          category: 'T-Shirt',
          basePrice: 19.99,
        },
        template: {
          id: 'template-1',
          name: 'Test Template',
          moodTag: 'Bold',
        },
      }),
      findMany: jest.fn().mockResolvedValue([]),
      count: jest.fn().mockResolvedValue(0),
    },
  })),
}));

describe('/api/customizations/preview', () => {
  const mockCanvas: DesignCanvas = {
    width: 800,
    height: 600,
    backgroundColor: '#ffffff',
    elements: [
      {
        id: 'text-1',
        type: 'text',
        x: 100,
        y: 100,
        width: 200,
        height: 50,
        rotation: 0,
        opacity: 1,
        visible: true,
        locked: false,
        data: { text: 'Test Text', fontSize: 16, fontFamily: 'Arial', color: '#000000' },
      },
    ],
    layers: ['text-1'],
  };

  describe('POST /api/customizations/preview', () => {
    test('generates preview successfully with valid canvas', async () => {
      const request = new NextRequest('http://localhost/api/customizations/preview', {
        method: 'POST',
        body: JSON.stringify({
          canvas: mockCanvas,
          options: {
            format: 'png',
            quality: 85,
            includePricing: true,
          },
        }),
      });

      const response = await previewPOST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toBeDefined();
      expect(data.data.canvas).toBeDefined();
      expect(data.data.area).toBeDefined();
      expect(data.data.images).toBeDefined();
      expect(data.data.pricing).toBeDefined();
    });

    test('returns error for empty canvas', async () => {
      const emptyCanvas = { ...mockCanvas, elements: [] };
      const request = new NextRequest('http://localhost/api/customizations/preview', {
        method: 'POST',
        body: JSON.stringify({ canvas: emptyCanvas }),
      });

      const response = await previewPOST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toContain('at least one design element');
    });

    test('validates canvas dimensions', async () => {
      const invalidCanvas = { ...mockCanvas, width: 50 }; // Too small
      const request = new NextRequest('http://localhost/api/customizations/preview', {
        method: 'POST',
        body: JSON.stringify({ canvas: invalidCanvas }),
      });

      const response = await previewPOST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Invalid request data');
    });

    test('handles image generation variants', async () => {
      const request = new NextRequest('http://localhost/api/customizations/preview', {
        method: 'POST',
        body: JSON.stringify({
          canvas: mockCanvas,
          options: {
            includeVariants: true,
          },
        }),
      });

      const response = await previewPOST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.data.images.preview).toBeDefined();
      expect(data.data.images.thumbnail).toBeDefined();
      expect(data.data.images.optimized).toBeDefined();
    });
  });

  describe('GET /api/customizations/preview', () => {
    test('returns print size configuration', async () => {
      const request = new NextRequest('http://localhost/api/customizations/preview?config=print-sizes');

      const response = await previewGET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.categories).toBeDefined();
      expect(data.data.limits).toBeDefined();
    });

    test('returns format configuration', async () => {
      const request = new NextRequest('http://localhost/api/customizations/preview?config=formats');

      const response = await previewGET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.data.supportedFormats).toContain('png');
      expect(data.data.defaultFormat).toBe('png');
    });
  });
});

describe('/api/customizations/save', () => {
  const mockSaveData = {
    name: 'My Test Design',
    description: 'A beautiful test design',
    canvas: {
      width: 800,
      height: 600,
      backgroundColor: '#ffffff',
      elements: [
        {
          id: 'text-1',
          type: 'text',
          x: 100,
          y: 100,
          width: 200,
          height: 50,
          rotation: 0,
          opacity: 1,
          visible: true,
          locked: false,
          data: { text: 'Test Text', fontSize: 16, fontFamily: 'Arial', color: '#000000' },
        },
      ],
      layers: ['text-1'],
    },
    productId: 'product-1',
    userId: 'user-1',
    placement: 'front' as const,
  };

  describe('POST /api/customizations/save', () => {
    test('saves customization successfully', async () => {
      const request = new NextRequest('http://localhost/api/customizations/save', {
        method: 'POST',
        body: JSON.stringify(mockSaveData),
      });

      const response = await savePOST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.customization).toBeDefined();
      expect(data.data.customization.name).toBe('My Test Design');
      expect(data.data.area).toBeDefined();
      expect(data.data.pricing).toBeDefined();
    });

    test('validates required fields', async () => {
      const invalidData = { ...mockSaveData, name: '' };
      const request = new NextRequest('http://localhost/api/customizations/save', {
        method: 'POST',
        body: JSON.stringify(invalidData),
      });

      const response = await savePOST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Invalid request data');
    });

    test('validates user exists', async () => {
      // Mock user not found
      const { PrismaClient } = require('@prisma/client');
      const mockPrisma = new PrismaClient();
      mockPrisma.user.findUnique.mockResolvedValueOnce(null);

      const request = new NextRequest('http://localhost/api/customizations/save', {
        method: 'POST',
        body: JSON.stringify(mockSaveData),
      });

      const response = await savePOST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('User not found');
    });

    test('validates product exists', async () => {
      // Mock product not found
      const { PrismaClient } = require('@prisma/client');
      const mockPrisma = new PrismaClient();
      mockPrisma.product.findUnique.mockResolvedValueOnce(null);

      const request = new NextRequest('http://localhost/api/customizations/save', {
        method: 'POST',
        body: JSON.stringify(mockSaveData),
      });

      const response = await savePOST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('Product not found');
    });

    test('handles empty canvas', async () => {
      const emptyCanvasData = {
        ...mockSaveData,
        canvas: { ...mockSaveData.canvas, elements: [] },
      };

      const request = new NextRequest('http://localhost/api/customizations/save', {
        method: 'POST',
        body: JSON.stringify(emptyCanvasData),
      });

      const response = await savePOST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('at least one design element');
    });
  });

  describe('GET /api/customizations/save', () => {
    test('fetches user customizations', async () => {
      const request = new NextRequest('http://localhost/api/customizations/save?userId=user-1');

      const response = await saveGET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.customizations).toBeDefined();
      expect(data.data.pagination).toBeDefined();
    });

    test('requires userId parameter', async () => {
      const request = new NextRequest('http://localhost/api/customizations/save');

      const response = await saveGET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('User ID is required');
    });

    test('supports filtering by product', async () => {
      const request = new NextRequest('http://localhost/api/customizations/save?userId=user-1&productId=product-1');

      const response = await saveGET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
    });

    test('supports pagination', async () => {
      const request = new NextRequest('http://localhost/api/customizations/save?userId=user-1&limit=5&offset=10');

      const response = await saveGET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.data.pagination.limit).toBe(5);
      expect(data.data.pagination.offset).toBe(10);
    });
  });
});

describe('Error Handling', () => {
  test('handles malformed JSON in requests', async () => {
    const request = new NextRequest('http://localhost/api/customizations/preview', {
      method: 'POST',
      body: 'invalid json',
    });

    const response = await previewPOST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.success).toBe(false);
  });

  test('handles database connection errors', async () => {
    // Mock database error
    const { PrismaClient } = require('@prisma/client');
    const mockPrisma = new PrismaClient();
    mockPrisma.user.findUnique.mockRejectedValueOnce(new Error('Database connection failed'));

    const request = new NextRequest('http://localhost/api/customizations/save', {
      method: 'POST',
      body: JSON.stringify({
        name: 'Test',
        canvas: { width: 800, height: 600, backgroundColor: '#fff', elements: [{}], layers: [] },
        productId: 'product-1',
        userId: 'user-1',
      }),
    });

    const response = await savePOST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.success).toBe(false);
  });
});
