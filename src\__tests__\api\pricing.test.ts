import { POST, GET } from '@/app/api/pricing/compute/route';
import { NextRequest } from 'next/server';

// Mock the pricing engine
jest.mock('@/lib/pricing/engine', () => ({
  PricingEngine: {
    getInstance: jest.fn(() => ({
      calculatePrice: jest.fn(),
      getPrintSizeConfig: jest.fn(() => ({
        small: { size: 'small', name: 'Small', valueMessage: 'Subtle elegance' },
        medium: { size: 'medium', name: 'Medium', valueMessage: 'Perfect balance' },
      })),
      getQualityTierConfig: jest.fn(() => ({
        standard: { tier: 'standard', name: 'Standard', qualityPromise: 'Reliable quality' },
        premium: { tier: 'premium', name: 'Premium', qualityPromise: 'Premium experience' },
      })),
    })),
  },
}));

describe('/api/pricing/compute', () => {
  let mockCalculatePrice: jest.Mock;

  beforeEach(() => {
    const { PricingEngine } = require('@/lib/pricing/engine');
    const mockInstance = PricingEngine.getInstance();
    mockCalculatePrice = mockInstance.calculatePrice;
    jest.clearAllMocks();
  });

  describe('POST', () => {
    it('should validate required fields', async () => {
      const request = new NextRequest('http://localhost/api/pricing/compute', {
        method: 'POST',
        body: JSON.stringify({}),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Invalid request data');
      expect(data.details).toBeDefined();
    });

    it('should validate quantity limits', async () => {
      const request = new NextRequest('http://localhost/api/pricing/compute', {
        method: 'POST',
        body: JSON.stringify({
          productId: 'test-product',
          quantity: 150, // Exceeds max
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.details.some((d: any) => d.message.includes('cannot exceed 100'))).toBe(true);
    });

    it('should handle successful pricing calculation', async () => {
      const mockResult = {
        success: true,
        data: {
          basePrice: 29.99,
          totalPrice: 59.98,
          currency: 'USD',
          breakdown: [
            {
              id: 'base_price',
              name: 'Base Price',
              amount: 59.98,
              type: 'base',
              valueMessage: 'Premium design included',
            },
          ],
          valueMessage: 'Exceptional value for your style',
          qualityPromise: 'Built to last',
          priceJustification: ['Ethically sourced', 'Expert craftsmanship'],
          calculatedAt: new Date().toISOString(),
          validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          priceId: 'test-price-id',
        },
      };

      mockCalculatePrice.mockResolvedValue(mockResult);

      const request = new NextRequest('http://localhost/api/pricing/compute', {
        method: 'POST',
        body: JSON.stringify({
          productId: 'test-product',
          quantity: 2,
          printSize: 'medium',
          qualityTier: 'standard',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toBeDefined();
      expect(data.data.totalPrice).toBe(59.98);
      expect(data.data.valueMessage).toBe('Exceptional value for your style');
      expect(mockCalculatePrice).toHaveBeenCalledWith({
        productId: 'test-product',
        quantity: 2,
        printSize: 'medium',
        qualityTier: 'standard',
      });
    });

    it('should handle pricing engine errors', async () => {
      mockCalculatePrice.mockResolvedValue({
        success: false,
        error: 'Product not found',
      });

      const request = new NextRequest('http://localhost/api/pricing/compute', {
        method: 'POST',
        body: JSON.stringify({
          productId: 'non-existent-product',
          quantity: 1,
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Product not found');
    });

    it('should handle internal server errors', async () => {
      mockCalculatePrice.mockRejectedValue(new Error('Database connection failed'));

      const request = new NextRequest('http://localhost/api/pricing/compute', {
        method: 'POST',
        body: JSON.stringify({
          productId: 'test-product',
          quantity: 1,
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Internal server error');
    });

    it('should validate enum values', async () => {
      const request = new NextRequest('http://localhost/api/pricing/compute', {
        method: 'POST',
        body: JSON.stringify({
          productId: 'test-product',
          quantity: 1,
          printSize: 'invalid-size',
          qualityTier: 'invalid-tier',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.details.some((d: any) => d.field === 'printSize')).toBe(true);
      expect(data.details.some((d: any) => d.field === 'qualityTier')).toBe(true);
    });
  });

  describe('GET', () => {
    it('should return all configuration by default', async () => {
      const request = new NextRequest('http://localhost/api/pricing/compute');

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.printSizes).toBeDefined();
      expect(data.data.qualityTiers).toBeDefined();
    });

    it('should return specific configuration when requested', async () => {
      const request = new NextRequest('http://localhost/api/pricing/compute?config=print-sizes');

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.small).toBeDefined();
      expect(data.data.medium).toBeDefined();
    });

    it('should return quality tiers configuration', async () => {
      const request = new NextRequest('http://localhost/api/pricing/compute?config=quality-tiers');

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.standard).toBeDefined();
      expect(data.data.premium).toBeDefined();
    });

    it('should handle configuration errors', async () => {
      const { PricingEngine } = require('@/lib/pricing/engine');
      const mockInstance = PricingEngine.getInstance();
      mockInstance.getPrintSizeConfig.mockImplementation(() => {
        throw new Error('Configuration error');
      });

      const request = new NextRequest('http://localhost/api/pricing/compute?config=print-sizes');

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Unable to fetch pricing configuration');
    });
  });
});
