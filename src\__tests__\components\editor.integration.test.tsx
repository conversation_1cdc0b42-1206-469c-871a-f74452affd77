import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AreaCalculationDisplay } from '@/components/editor/AreaCalculationDisplay';
import { PricingImpactIndicator } from '@/components/editor/PricingImpactIndicator';
import { SavePreviewPanel } from '@/components/editor/SavePreviewPanel';
import { useEditorStore } from '@/stores/editorStore';
import { calculateCanvasPrintArea } from '@/lib/utils/areaCalculation';

// Mock the editor store
jest.mock('@/stores/editorStore');
const mockUseEditorStore = useEditorStore as jest.MockedFunction<typeof useEditorStore>;

// Mock the area calculation
jest.mock('@/lib/utils/areaCalculation');
const mockCalculateCanvasPrintArea = calculateCanvasPrintArea as jest.MockedFunction<typeof calculateCanvasPrintArea>;

// Mock the API hook
jest.mock('@/hooks/useCustomizationAPI', () => ({
  useCustomizationAPI: () => ({
    isGeneratingPreview: false,
    isSaving: false,
    previewData: null,
    saveData: null,
    error: null,
    lastPreviewUrl: null,
    generatePreview: jest.fn().mockResolvedValue({
      canvas: { width: 800, height: 600, elementCount: 2 },
      area: { totalArea: 50.5, printSizeCategory: 'medium' },
      images: { preview: { buffer: 'mock-base64', metadata: {} } },
    }),
    saveCustomization: jest.fn().mockResolvedValue({
      customization: { id: 'test-id', name: 'Test Design' },
    }),
    clearError: jest.fn(),
    hasPreview: false,
    hasSaved: false,
    isLoading: false,
  }),
}));

// Mock fetch for API calls
global.fetch = jest.fn();

describe('Editor Integration Tests', () => {
  const mockCanvas = {
    width: 800,
    height: 600,
    backgroundColor: '#ffffff',
    elements: [
      {
        id: 'text-1',
        type: 'text' as const,
        x: 100,
        y: 100,
        width: 200,
        height: 50,
        rotation: 0,
        opacity: 1,
        visible: true,
        locked: false,
        data: { text: 'Test Text', fontSize: 16, fontFamily: 'Arial', color: '#000000' },
      },
      {
        id: 'image-1',
        type: 'image' as const,
        x: 200,
        y: 200,
        width: 150,
        height: 150,
        rotation: 0,
        opacity: 1,
        visible: true,
        locked: false,
        data: { src: 'test.jpg', originalWidth: 150, originalHeight: 150 },
      },
    ],
    layers: ['text-1', 'image-1'],
  };

  const mockAreaData = {
    totalArea: 50.5,
    elementAreas: [
      { id: 'text-1', area: 25.2, type: 'text' },
      { id: 'image-1', area: 25.3, type: 'image' },
    ],
    overlappingArea: 0,
    printSizeCategory: 'medium' as const,
    isWithinLimits: true,
    recommendations: [],
  };

  beforeEach(() => {
    mockUseEditorStore.mockReturnValue({
      canvas: mockCanvas,
      placement: 'front',
      // Add other required store properties as needed
    } as any);

    mockCalculateCanvasPrintArea.mockReturnValue(mockAreaData);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('AreaCalculationDisplay', () => {
    test('displays area calculation for canvas with elements', () => {
      render(<AreaCalculationDisplay />);

      expect(screen.getByText('Print Area')).toBeInTheDocument();
      expect(screen.getByText('50.5 cm²')).toBeInTheDocument();
      expect(screen.getByText('Medium Print')).toBeInTheDocument();
    });

    test('shows empty state when no elements', () => {
      mockUseEditorStore.mockReturnValue({
        canvas: { ...mockCanvas, elements: [] },
        placement: 'front',
      } as any);

      render(<AreaCalculationDisplay />);

      expect(screen.getByText('Add design elements to see print area')).toBeInTheDocument();
    });

    test('shows detailed information when showDetails is true', () => {
      render(<AreaCalculationDisplay showDetails={true} />);

      expect(screen.getByText('Element Breakdown')).toBeInTheDocument();
      expect(screen.getByText('text 1')).toBeInTheDocument();
      expect(screen.getByText('image 2')).toBeInTheDocument();
      expect(screen.getByText('25.2 cm²')).toBeInTheDocument();
      expect(screen.getByText('25.3 cm²')).toBeInTheDocument();
    });

    test('calls onPricingImpact when area changes significantly', async () => {
      const onPricingImpact = jest.fn();
      const { rerender } = render(
        <AreaCalculationDisplay onPricingImpact={onPricingImpact} />
      );

      // Simulate area change by updating the mock
      mockCalculateCanvasPrintArea.mockReturnValue({
        ...mockAreaData,
        totalArea: 150.5, // Changed from medium to large category
        printSizeCategory: 'large',
      });

      rerender(<AreaCalculationDisplay onPricingImpact={onPricingImpact} />);

      await waitFor(() => {
        expect(onPricingImpact).toHaveBeenCalled();
      });
    });

    test('shows recommendations when available', () => {
      mockCalculateCanvasPrintArea.mockReturnValue({
        ...mockAreaData,
        recommendations: ['Consider spacing elements out for better visibility'],
      });

      render(<AreaCalculationDisplay showDetails={true} />);

      expect(screen.getByText('Suggestions')).toBeInTheDocument();
      expect(screen.getByText(/Consider spacing elements out/)).toBeInTheDocument();
    });
  });

  describe('PricingImpactIndicator', () => {
    test('shows impact indicator when area increases significantly', () => {
      render(
        <PricingImpactIndicator
          currentArea={150}
          previousArea={50}
          currentCategory="large"
        />
      );

      expect(screen.getByText('Bigger Impact!')).toBeInTheDocument();
      expect(screen.getByText(/Upgraded to Large Print/)).toBeInTheDocument();
    });

    test('shows different message when area decreases', () => {
      render(
        <PricingImpactIndicator
          currentArea={50}
          previousArea={150}
          currentCategory="medium"
        />
      );

      expect(screen.getByText('Smart Sizing!')).toBeInTheDocument();
      expect(screen.getByText(/Reduced to Medium Print/)).toBeInTheDocument();
    });

    test('does not render when no significant impact', () => {
      render(
        <PricingImpactIndicator
          currentArea={52}
          previousArea={50}
          currentCategory="medium"
        />
      );

      // Should not render anything for small changes within same category
      expect(screen.queryByText('Bigger Impact!')).not.toBeInTheDocument();
      expect(screen.queryByText('Smart Sizing!')).not.toBeInTheDocument();
    });

    test('can be dismissed', async () => {
      const onDismiss = jest.fn();
      render(
        <PricingImpactIndicator
          currentArea={150}
          previousArea={50}
          currentCategory="large"
          onDismiss={onDismiss}
        />
      );

      const dismissButton = screen.getByRole('button', { name: /close/i });
      await userEvent.click(dismissButton);

      expect(onDismiss).toHaveBeenCalled();
    });

    test('shows detailed information when expanded', async () => {
      render(
        <PricingImpactIndicator
          currentArea={150}
          previousArea={50}
          currentCategory="large"
        />
      );

      const detailsButton = screen.getByText(/Show Details/);
      await userEvent.click(detailsButton);

      expect(screen.getByText('Previous Size')).toBeInTheDocument();
      expect(screen.getByText('50.0 cm²')).toBeInTheDocument();
      expect(screen.getByText('Change')).toBeInTheDocument();
      expect(screen.getByText('+100.0 cm²')).toBeInTheDocument();
    });
  });

  describe('SavePreviewPanel', () => {
    test('renders save and preview buttons', () => {
      render(<SavePreviewPanel />);

      expect(screen.getByText('Generate Preview')).toBeInTheDocument();
      expect(screen.getByText('Save Design')).toBeInTheDocument();
    });

    test('disables buttons when no elements', () => {
      mockUseEditorStore.mockReturnValue({
        canvas: { ...mockCanvas, elements: [] },
        placement: 'front',
      } as any);

      render(<SavePreviewPanel />);

      expect(screen.getByText('Generate Preview')).toBeDisabled();
      expect(screen.getByText('Save Design')).toBeDisabled();
    });

    test('opens save form when save button clicked', async () => {
      render(<SavePreviewPanel />);

      const saveButton = screen.getByText('Save Design');
      await userEvent.click(saveButton);

      expect(screen.getByText('Save Your Creation')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('My Amazing Design')).toBeInTheDocument();
    });

    test('validates design name in save form', async () => {
      render(<SavePreviewPanel />);

      // Open save form
      const saveButton = screen.getByText('Save Design');
      await userEvent.click(saveButton);

      // Try to save without name
      const saveFormButton = screen.getByRole('button', { name: /Save Design/ });
      expect(saveFormButton).toBeDisabled();

      // Add name and check it's enabled
      const nameInput = screen.getByPlaceholderText('My Amazing Design');
      await userEvent.type(nameInput, 'My Test Design');

      expect(saveFormButton).not.toBeDisabled();
    });
  });
});
