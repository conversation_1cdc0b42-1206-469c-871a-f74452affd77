import { renderHook, act } from '@testing-library/react';
import { useEditorStore } from '@/stores/editorStore';

describe('EditorStore', () => {
  beforeEach(() => {
    // Reset store before each test
    const { result } = renderHook(() => useEditorStore());
    act(() => {
      result.current.clearCanvas();
    });
  });

  it('should initialize with default state', () => {
    const { result } = renderHook(() => useEditorStore());
    
    expect(result.current.canvas.width).toBe(400);
    expect(result.current.canvas.height).toBe(500);
    expect(result.current.canvas.elements).toHaveLength(0);
    expect(result.current.selectedElementId).toBeNull();
    expect(result.current.activeTool).toBe('select');
    expect(result.current.placement).toBe('front');
    expect(result.current.zoom).toBe(1);
  });

  it('should add text element', () => {
    const { result } = renderHook(() => useEditorStore());
    
    act(() => {
      result.current.addText('Hello World', 100, 100);
    });

    expect(result.current.canvas.elements).toHaveLength(1);
    expect(result.current.canvas.elements[0].type).toBe('text');
    expect(result.current.canvas.elements[0].data.text).toBe('Hello World');
    expect(result.current.canvas.elements[0].x).toBe(100);
    expect(result.current.canvas.elements[0].y).toBe(100);
    expect(result.current.selectedElementId).toBe(result.current.canvas.elements[0].id);
  });

  it('should add image element', () => {
    const { result } = renderHook(() => useEditorStore());
    const testImageSrc = 'data:image/png;base64,test';
    
    act(() => {
      result.current.addImage(testImageSrc, 50, 50);
    });

    expect(result.current.canvas.elements).toHaveLength(1);
    expect(result.current.canvas.elements[0].type).toBe('image');
    expect(result.current.canvas.elements[0].data.src).toBe(testImageSrc);
    expect(result.current.canvas.elements[0].x).toBe(50);
    expect(result.current.canvas.elements[0].y).toBe(50);
  });

  it('should update element properties', () => {
    const { result } = renderHook(() => useEditorStore());
    
    act(() => {
      result.current.addText('Test', 0, 0);
    });

    const elementId = result.current.canvas.elements[0].id;

    act(() => {
      result.current.updateElement(elementId, { x: 200, y: 150 });
    });

    expect(result.current.canvas.elements[0].x).toBe(200);
    expect(result.current.canvas.elements[0].y).toBe(150);
  });

  it('should delete element', () => {
    const { result } = renderHook(() => useEditorStore());
    
    act(() => {
      result.current.addText('Test', 0, 0);
    });

    const elementId = result.current.canvas.elements[0].id;

    act(() => {
      result.current.deleteElement(elementId);
    });

    expect(result.current.canvas.elements).toHaveLength(0);
    expect(result.current.selectedElementId).toBeNull();
  });

  it('should handle undo/redo', () => {
    const { result } = renderHook(() => useEditorStore());
    
    // Add element
    act(() => {
      result.current.addText('Test', 0, 0);
    });

    expect(result.current.canvas.elements).toHaveLength(1);

    // Undo
    act(() => {
      result.current.undo();
    });

    expect(result.current.canvas.elements).toHaveLength(0);

    // Redo
    act(() => {
      result.current.redo();
    });

    expect(result.current.canvas.elements).toHaveLength(1);
  });

  it('should change placement', () => {
    const { result } = renderHook(() => useEditorStore());
    
    act(() => {
      result.current.setPlacement('back');
    });

    expect(result.current.placement).toBe('back');
  });

  it('should change zoom level', () => {
    const { result } = renderHook(() => useEditorStore());
    
    act(() => {
      result.current.setZoom(1.5);
    });

    expect(result.current.zoom).toBe(1.5);

    // Test zoom limits
    act(() => {
      result.current.setZoom(5); // Should be clamped to 3
    });

    expect(result.current.zoom).toBe(3);

    act(() => {
      result.current.setZoom(-1); // Should be clamped to 0.1
    });

    expect(result.current.zoom).toBe(0.1);
  });

  it('should duplicate element', () => {
    const { result } = renderHook(() => useEditorStore());
    
    act(() => {
      result.current.addText('Original', 100, 100);
    });

    const originalId = result.current.canvas.elements[0].id;

    act(() => {
      result.current.duplicateElement(originalId);
    });

    expect(result.current.canvas.elements).toHaveLength(2);
    expect(result.current.canvas.elements[1].data.text).toBe('Original');
    expect(result.current.canvas.elements[1].x).toBe(120); // Offset by 20
    expect(result.current.canvas.elements[1].y).toBe(120); // Offset by 20
  });
});
