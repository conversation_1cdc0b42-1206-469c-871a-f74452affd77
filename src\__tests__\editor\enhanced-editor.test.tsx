import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

import { CustomizationEditor } from '@/components/editor/CustomizationEditor';
import { TemplateGallery } from '@/components/editor/TemplateGallery';
import { MobileCanvasControls } from '@/components/editor/MobileCanvasControls';
import { UndoRedoToolbar } from '@/components/editor/UndoRedoToolbar';
import { AlignmentTools } from '@/components/editor/AlignmentTools';
import { useEditorStore } from '@/stores/editorStore';

// Mock the editor store
jest.mock('@/stores/editorStore');
const mockUseEditorStore = useEditorStore as jest.MockedFunction<typeof useEditorStore>;

// Mock touch gesture utilities
jest.mock('@/utils/touchGestures', () => ({
  triggerHapticFeedback: jest.fn(),
  recognizeGesture: jest.fn(() => 'tap'),
  getTouchCenter: jest.fn(() => ({ x: 0, y: 0 })),
  getTouchDistance: jest.fn(() => 100),
  getTouchAngle: jest.fn(() => 0),
  calculateVelocity: jest.fn(() => ({ x: 0, y: 0 })),
  constrainScale: jest.fn((scale) => scale),
  constrainRotation: jest.fn((rotation) => rotation),
  DEFAULT_GESTURE_CONFIG: {
    enablePinch: true,
    enableRotation: true,
    enablePan: true,
    minScale: 0.1,
    maxScale: 5.0,
    snapToGrid: true,
    snapThreshold: 10,
    hapticFeedback: true,
  },
  createInitialGestureState: jest.fn(() => ({
    isActive: false,
    type: 'tap',
    startTime: 0,
    scale: 1,
    rotation: 0,
    translation: { x: 0, y: 0 },
    velocity: { x: 0, y: 0 },
    center: { x: 0, y: 0 },
    fingers: 0,
  })),
}));

// Mock snap guides utilities
jest.mock('@/utils/snapGuides', () => ({
  generateSnapGuides: jest.fn(() => []),
  calculateSnapResult: jest.fn(() => ({
    snapped: false,
    guides: [],
    adjustedPosition: { x: 0, y: 0 },
    feedback: 'visual',
  })),
  applySnapFeedback: jest.fn(),
  snapToGrid: jest.fn(() => ({ x: 0, y: 0, snapped: false })),
  detectAlignment: jest.fn(() => ({ horizontal: [], vertical: [] })),
}));

// Mock responsive hook
jest.mock('@/components/ui/ResponsiveContainer', () => ({
  ResponsiveContainer: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useResponsive: () => ({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    isTouchDevice: false,
    prefersReducedMotion: false,
    screenWidth: 1024,
  }),
}));

describe('Enhanced Customization Editor', () => {
  const mockStore = {
    canvas: {
      width: 400,
      height: 500,
      backgroundColor: '#ffffff',
      elements: [],
      layers: [],
    },
    selectedElementId: null,
    activeTool: 'select',
    placement: 'front',
    zoom: 1,
    isLoading: false,
    history: [],
    historyIndex: 0,
    gestureState: {
      isActive: false,
      type: 'tap',
      startTime: 0,
      scale: 1,
      rotation: 0,
      translation: { x: 0, y: 0 },
      velocity: { x: 0, y: 0 },
      center: { x: 0, y: 0 },
      fingers: 0,
    },
    gestureConfig: {
      enablePinch: true,
      enableRotation: true,
      enablePan: true,
      minScale: 0.1,
      maxScale: 5.0,
      snapToGrid: true,
      snapThreshold: 10,
      hapticFeedback: true,
    },
    snapGuides: [],
    snapEnabled: true,
    snapThreshold: 10,
    gridEnabled: true,
    gridSize: 20,
    availableTemplates: [],
    selectedTemplate: null,
    templatePreviews: [],
    isMobileMode: false,
    touchOptimized: true,
    hapticEnabled: true,
    setActiveTool: jest.fn(),
    updateElement: jest.fn(),
    addElement: jest.fn(),
    deleteElement: jest.fn(),
    selectElement: jest.fn(),
    setZoom: jest.fn(),
    undo: jest.fn(),
    redo: jest.fn(),
    saveToHistory: jest.fn(),
    updateGestureState: jest.fn(),
    updateSnapGuides: jest.fn(),
    setSnapEnabled: jest.fn(),
    setMobileMode: jest.fn(),
    loadTemplate: jest.fn(),
  };

  beforeEach(() => {
    mockUseEditorStore.mockReturnValue(mockStore as any);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('CustomizationEditor', () => {
    it('renders all main components', () => {
      render(<CustomizationEditor />);
      
      expect(screen.getByText('✨ Every design tells a story. What\'s yours?')).toBeInTheDocument();
      expect(screen.getByText('Text')).toBeInTheDocument();
      expect(screen.getByText('Images')).toBeInTheDocument();
      expect(screen.getByText('Templates')).toBeInTheDocument();
      expect(screen.getByText('Placement')).toBeInTheDocument();
    });

    it('switches between tabs correctly', async () => {
      const user = userEvent.setup();
      render(<CustomizationEditor />);
      
      const templatesTab = screen.getByText('Templates');
      await user.click(templatesTab);
      
      expect(screen.getByText('Ready-Made Templates')).toBeInTheDocument();
    });

    it('updates mobile mode when responsive state changes', () => {
      render(<CustomizationEditor />);
      
      expect(mockStore.setMobileMode).toHaveBeenCalledWith(false);
    });
  });

  describe('UndoRedoToolbar', () => {
    it('renders undo and redo buttons', () => {
      render(<UndoRedoToolbar />);
      
      expect(screen.getByTitle('Nothing to undo')).toBeInTheDocument();
      expect(screen.getByTitle('Nothing to redo')).toBeInTheDocument();
    });

    it('shows history indicator', () => {
      render(<UndoRedoToolbar />);
      
      expect(screen.getByText('1/0')).toBeInTheDocument();
    });

    it('triggers haptic feedback on actions', async () => {
      const user = userEvent.setup();
      const mockStoreWithHistory = {
        ...mockStore,
        historyIndex: 1,
        history: [{}, {}],
      };
      mockUseEditorStore.mockReturnValue(mockStoreWithHistory as any);
      
      render(<UndoRedoToolbar />);
      
      const undoButton = screen.getByTitle('Undo last action');
      await user.click(undoButton);
      
      expect(mockStore.undo).toHaveBeenCalled();
    });
  });

  describe('AlignmentTools', () => {
    it('renders alignment buttons', () => {
      render(<AlignmentTools />);
      
      expect(screen.getByTitle('Align Left')).toBeInTheDocument();
      expect(screen.getByTitle('Align Center')).toBeInTheDocument();
      expect(screen.getByTitle('Align Right')).toBeInTheDocument();
    });

    it('disables buttons when no element is selected', () => {
      render(<AlignmentTools />);
      
      const alignLeftButton = screen.getByTitle('Align Left');
      expect(alignLeftButton).toBeDisabled();
    });

    it('enables buttons when element is selected', () => {
      const mockStoreWithSelection = {
        ...mockStore,
        selectedElementId: 'element-1',
        canvas: {
          ...mockStore.canvas,
          elements: [
            {
              id: 'element-1',
              type: 'text',
              x: 100,
              y: 100,
              width: 200,
              height: 50,
              rotation: 0,
              opacity: 1,
              visible: true,
              locked: false,
              data: { text: 'Test' },
            },
          ],
        },
      };
      mockUseEditorStore.mockReturnValue(mockStoreWithSelection as any);
      
      render(<AlignmentTools />);
      
      const alignLeftButton = screen.getByTitle('Align Left');
      expect(alignLeftButton).not.toBeDisabled();
    });
  });

  describe('Touch Gestures', () => {
    it('handles touch events on canvas', () => {
      const { container } = render(<CustomizationEditor />);
      const canvas = container.querySelector('.touch-none');
      
      expect(canvas).toBeInTheDocument();
      expect(canvas).toHaveClass('touch-none');
    });
  });

  describe('Snap Guides', () => {
    it('updates snap guides when canvas changes', () => {
      render(<CustomizationEditor />);
      
      expect(mockStore.updateSnapGuides).toHaveBeenCalled();
    });
  });

  describe('Template System', () => {
    it('renders template gallery', () => {
      render(<TemplateGallery />);
      
      expect(screen.getByText('✨ Ready-Made Templates')).toBeInTheDocument();
      expect(screen.getByText('Start with inspiration, make it uniquely yours')).toBeInTheDocument();
    });

    it('filters templates by search', async () => {
      const user = userEvent.setup();
      render(<TemplateGallery />);
      
      const searchInput = screen.getByPlaceholderText('Search templates...');
      await user.type(searchInput, 'bold');
      
      expect(searchInput).toHaveValue('bold');
    });
  });

  describe('Performance', () => {
    it('renders without performance issues', () => {
      const startTime = performance.now();
      render(<CustomizationEditor />);
      const endTime = performance.now();
      
      // Should render in less than 100ms
      expect(endTime - startTime).toBeLessThan(100);
    });

    it('handles rapid state updates', async () => {
      const user = userEvent.setup();
      render(<CustomizationEditor />);
      
      // Simulate rapid tab switching
      const textTab = screen.getByText('Text');
      const imageTab = screen.getByText('Images');
      
      await user.click(textTab);
      await user.click(imageTab);
      await user.click(textTab);
      
      // Should not throw errors
      expect(screen.getByText('Text')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles missing store gracefully', () => {
      mockUseEditorStore.mockReturnValue(null as any);
      
      expect(() => render(<CustomizationEditor />)).not.toThrow();
    });

    it('handles invalid gesture data', () => {
      const mockStoreWithInvalidGesture = {
        ...mockStore,
        gestureState: null,
      };
      mockUseEditorStore.mockReturnValue(mockStoreWithInvalidGesture as any);
      
      expect(() => render(<CustomizationEditor />)).not.toThrow();
    });
  });
});
