import { 
  recognizeGesture, 
  getTouchCenter, 
  getTouchDistance, 
  calculateVelocity,
  throttle,
  debounce,
  measurePerformance
} from '@/utils/touchGestures';

import { 
  generateSnapGuides, 
  calculateSnapResult, 
  detectAlignment 
} from '@/utils/snapGuides';

import { HistoryManager } from '@/utils/historyManager';
import { DesignCanvas, DesignElement } from '@/types';

describe('Performance Tests', () => {
  describe('Touch Gesture Performance', () => {
    it('recognizes gestures quickly', () => {
      const mockTouches = {
        length: 2,
        0: { clientX: 100, clientY: 100 } as Touch,
        1: { clientX: 200, clientY: 200 } as Touch,
      } as TouchList;

      const startTime = performance.now();
      for (let i = 0; i < 1000; i++) {
        recognizeGesture(mockTouches);
      }
      const endTime = performance.now();

      // Should process 1000 gesture recognitions in less than 10ms
      expect(endTime - startTime).toBeLessThan(10);
    });

    it('calculates touch center efficiently', () => {
      const mockTouches = {
        length: 3,
        0: { clientX: 0, clientY: 0 } as Touch,
        1: { clientX: 100, clientY: 100 } as Touch,
        2: { clientX: 200, clientY: 200 } as Touch,
      } as TouchList;

      const startTime = performance.now();
      for (let i = 0; i < 1000; i++) {
        getTouchCenter(mockTouches);
      }
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(5);
    });

    it('calculates touch distance efficiently', () => {
      const touch1 = { clientX: 0, clientY: 0 } as Touch;
      const touch2 = { clientX: 100, clientY: 100 } as Touch;

      const startTime = performance.now();
      for (let i = 0; i < 1000; i++) {
        getTouchDistance(touch1, touch2);
      }
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(5);
    });

    it('throttles function calls correctly', (done) => {
      let callCount = 0;
      const throttledFn = throttle(() => {
        callCount++;
      }, 100);

      // Call function rapidly
      for (let i = 0; i < 10; i++) {
        throttledFn();
      }

      // Should only be called once immediately
      expect(callCount).toBe(1);

      setTimeout(() => {
        // Call again after throttle period
        throttledFn();
        expect(callCount).toBe(2);
        done();
      }, 150);
    });

    it('debounces function calls correctly', (done) => {
      let callCount = 0;
      const debouncedFn = debounce(() => {
        callCount++;
      }, 100);

      // Call function rapidly
      for (let i = 0; i < 10; i++) {
        debouncedFn();
      }

      // Should not be called yet
      expect(callCount).toBe(0);

      setTimeout(() => {
        // Should be called once after debounce period
        expect(callCount).toBe(1);
        done();
      }, 150);
    });
  });

  describe('Snap Guides Performance', () => {
    const createMockCanvas = (): DesignCanvas => ({
      width: 400,
      height: 500,
      backgroundColor: '#ffffff',
      elements: [],
      layers: [],
    });

    const createMockElements = (count: number): DesignElement[] => {
      return Array.from({ length: count }, (_, i) => ({
        id: `element-${i}`,
        type: 'text',
        x: Math.random() * 400,
        y: Math.random() * 500,
        width: 100,
        height: 50,
        rotation: 0,
        opacity: 1,
        visible: true,
        locked: false,
        data: { text: `Element ${i}` },
      }));
    };

    it('generates snap guides efficiently with many elements', () => {
      const canvas = createMockCanvas();
      const elements = createMockElements(100);

      const startTime = performance.now();
      generateSnapGuides(canvas, elements);
      const endTime = performance.now();

      // Should generate guides for 100 elements in less than 50ms
      expect(endTime - startTime).toBeLessThan(50);
    });

    it('calculates snap results quickly', () => {
      const canvas = createMockCanvas();
      const elements = createMockElements(50);
      const guides = generateSnapGuides(canvas, elements);

      const startTime = performance.now();
      for (let i = 0; i < 100; i++) {
        calculateSnapResult(
          { x: Math.random() * 400, y: Math.random() * 500 },
          { width: 100, height: 50 },
          guides
        );
      }
      const endTime = performance.now();

      // Should calculate 100 snap results in less than 20ms
      expect(endTime - startTime).toBeLessThan(20);
    });

    it('detects alignments efficiently', () => {
      const elements = createMockElements(50);

      const startTime = performance.now();
      detectAlignment(elements);
      const endTime = performance.now();

      // Should detect alignments for 50 elements in less than 10ms
      expect(endTime - startTime).toBeLessThan(10);
    });
  });

  describe('History Manager Performance', () => {
    const createMockCanvas = (): DesignCanvas => ({
      width: 400,
      height: 500,
      backgroundColor: '#ffffff',
      elements: [],
      layers: [],
    });

    it('handles large history efficiently', () => {
      const historyManager = new HistoryManager(createMockCanvas());
      
      const startTime = performance.now();
      
      // Add 100 history entries
      for (let i = 0; i < 100; i++) {
        const canvas = createMockCanvas();
        canvas.elements = [{
          id: `element-${i}`,
          type: 'text',
          x: i * 10,
          y: i * 10,
          width: 100,
          height: 50,
          rotation: 0,
          opacity: 1,
          visible: true,
          locked: false,
          data: { text: `Element ${i}` },
        }];
        historyManager.saveToHistory(canvas);
      }
      
      const endTime = performance.now();

      // Should handle 100 history entries in less than 50ms
      expect(endTime - startTime).toBeLessThan(50);
    });

    it('performs undo/redo operations quickly', () => {
      const historyManager = new HistoryManager(createMockCanvas());
      
      // Build up some history
      for (let i = 0; i < 10; i++) {
        const canvas = createMockCanvas();
        historyManager.saveToHistory(canvas);
      }

      const startTime = performance.now();
      
      // Perform 20 undo/redo operations
      for (let i = 0; i < 10; i++) {
        historyManager.undo();
        historyManager.redo();
      }
      
      const endTime = performance.now();

      // Should perform 20 operations in less than 10ms
      expect(endTime - startTime).toBeLessThan(10);
    });
  });

  describe('Memory Usage', () => {
    it('does not leak memory with repeated operations', () => {
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
      
      // Perform many operations that could potentially leak memory
      for (let i = 0; i < 1000; i++) {
        const mockTouches = {
          length: 2,
          0: { clientX: i, clientY: i } as Touch,
          1: { clientX: i + 100, clientY: i + 100 } as Touch,
        } as TouchList;
        
        recognizeGesture(mockTouches);
        getTouchCenter(mockTouches);
        calculateVelocity({ x: i, y: i }, { x: i - 1, y: i - 1 }, 16);
      }
      
      // Force garbage collection if available
      if ((global as any).gc) {
        (global as any).gc();
      }
      
      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
      
      // Memory usage should not increase significantly (allow 1MB increase)
      if (initialMemory > 0) {
        expect(finalMemory - initialMemory).toBeLessThan(1024 * 1024);
      }
    });
  });

  describe('Performance Monitoring', () => {
    it('measures performance correctly', () => {
      let measured = false;
      const originalConsoleLog = console.log;
      console.log = jest.fn((message) => {
        if (message.includes('took') && message.includes('milliseconds')) {
          measured = true;
        }
      });

      measurePerformance('test operation', () => {
        // Simulate some work
        for (let i = 0; i < 1000; i++) {
          Math.sqrt(i);
        }
      });

      console.log = originalConsoleLog;
      expect(measured).toBe(true);
    });
  });

  describe('Real-world Scenarios', () => {
    it('handles rapid touch events without performance degradation', () => {
      const startTime = performance.now();
      
      // Simulate rapid touch events like a user drawing
      for (let i = 0; i < 100; i++) {
        const mockTouches = {
          length: 1,
          0: { clientX: i * 2, clientY: i * 2 } as Touch,
        } as TouchList;
        
        recognizeGesture(mockTouches);
        getTouchCenter(mockTouches);
        
        if (i > 0) {
          calculateVelocity(
            { x: i * 2, y: i * 2 },
            { x: (i - 1) * 2, y: (i - 1) * 2 },
            16
          );
        }
      }
      
      const endTime = performance.now();

      // Should handle 100 rapid touch events in less than 50ms
      expect(endTime - startTime).toBeLessThan(50);
    });

    it('maintains performance with complex canvas state', () => {
      const canvas = {
        width: 800,
        height: 600,
        backgroundColor: '#ffffff',
        elements: Array.from({ length: 50 }, (_, i) => ({
          id: `element-${i}`,
          type: 'text' as const,
          x: Math.random() * 800,
          y: Math.random() * 600,
          width: 100 + Math.random() * 200,
          height: 50 + Math.random() * 100,
          rotation: Math.random() * 360,
          opacity: 0.5 + Math.random() * 0.5,
          visible: true,
          locked: false,
          data: { text: `Complex Element ${i}` },
        })),
        layers: [],
      };

      const startTime = performance.now();
      
      // Perform operations that would be common in a complex editor
      generateSnapGuides(canvas, canvas.elements);
      detectAlignment(canvas.elements);
      
      for (let i = 0; i < 10; i++) {
        calculateSnapResult(
          { x: Math.random() * 800, y: Math.random() * 600 },
          { width: 100, height: 50 },
          generateSnapGuides(canvas, canvas.elements)
        );
      }
      
      const endTime = performance.now();

      // Should handle complex operations in less than 100ms
      expect(endTime - startTime).toBeLessThan(100);
    });
  });
});
