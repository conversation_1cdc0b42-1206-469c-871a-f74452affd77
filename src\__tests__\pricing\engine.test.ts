import { PricingEngine } from '@/lib/pricing/engine';
import { PricingRequest } from '@/types/pricing';

// Mock Prisma
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    product: {
      findUnique: jest.fn(),
    },
    priceRule: {
      findMany: jest.fn(),
    },
  })),
}));

describe('PricingEngine', () => {
  let pricingEngine: PricingEngine;
  let mockPrisma: any;

  beforeEach(() => {
    pricingEngine = PricingEngine.getInstance();
    // Reset mocks
    jest.clearAllMocks();
  });

  describe('calculatePrice', () => {
    it('should validate request parameters', async () => {
      const invalidRequest: PricingRequest = {
        productId: '',
        quantity: 0,
      };

      const result = await pricingEngine.calculatePrice(invalidRequest);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Product ID is required');
    });

    it('should reject excessive quantities', async () => {
      const invalidRequest: PricingRequest = {
        productId: 'test-product',
        quantity: 150,
      };

      const result = await pricingEngine.calculatePrice(invalidRequest);

      expect(result.success).toBe(false);
      expect(result.error).toContain('exceeds maximum limit');
    });

    it('should calculate basic pricing correctly', async () => {
      // Mock product data
      const mockProduct = {
        id: 'test-product',
        name: 'Test T-Shirt',
        basePrice: 29.99,
        variants: [],
      };

      // Mock Prisma response
      const { PrismaClient } = require('@prisma/client');
      const mockPrismaInstance = new PrismaClient();
      mockPrismaInstance.product.findUnique.mockResolvedValue(mockProduct);

      const request: PricingRequest = {
        productId: 'test-product',
        quantity: 2,
      };

      const result = await pricingEngine.calculatePrice(request);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.basePrice).toBe(29.99);
      expect(result.data!.totalPrice).toBe(59.98); // 29.99 * 2
      expect(result.data!.breakdown).toHaveLength(1); // Base price component
    });

    it('should apply fabric surcharge correctly', async () => {
      const mockProduct = {
        id: 'test-product',
        name: 'Test T-Shirt',
        basePrice: 29.99,
        variants: [
          {
            fabricId: 'premium-fabric',
            fabric: {
              name: 'Premium Modal',
              priceModifier: 15.00,
              feelTags: ['luxurious', 'soft'],
            },
          },
        ],
      };

      const { PrismaClient } = require('@prisma/client');
      const mockPrismaInstance = new PrismaClient();
      mockPrismaInstance.product.findUnique.mockResolvedValue(mockProduct);

      const request: PricingRequest = {
        productId: 'test-product',
        quantity: 1,
        fabricId: 'premium-fabric',
      };

      const result = await pricingEngine.calculatePrice(request);

      expect(result.success).toBe(true);
      expect(result.data!.totalPrice).toBe(44.99); // 29.99 + 15.00
      expect(result.data!.breakdown).toHaveLength(2); // Base + fabric surcharge
      
      const fabricComponent = result.data!.breakdown.find(c => c.id === 'fabric_surcharge');
      expect(fabricComponent).toBeDefined();
      expect(fabricComponent!.amount).toBe(15.00);
      expect(fabricComponent!.valueMessage).toContain('luxurious');
    });

    it('should generate emotional value messaging', async () => {
      const mockProduct = {
        id: 'test-product',
        name: 'Test T-Shirt',
        basePrice: 29.99,
        variants: [],
      };

      const { PrismaClient } = require('@prisma/client');
      const mockPrismaInstance = new PrismaClient();
      mockPrismaInstance.product.findUnique.mockResolvedValue(mockProduct);

      const request: PricingRequest = {
        productId: 'test-product',
        quantity: 1,
        qualityTier: 'premium',
      };

      const result = await pricingEngine.calculatePrice(request);

      expect(result.success).toBe(true);
      expect(result.data!.valueMessage).toBeDefined();
      expect(result.data!.qualityPromise).toBeDefined();
      expect(result.data!.priceJustification).toBeDefined();
      expect(result.data!.priceJustification.length).toBeGreaterThan(0);
    });

    it('should handle product not found', async () => {
      const { PrismaClient } = require('@prisma/client');
      const mockPrismaInstance = new PrismaClient();
      mockPrismaInstance.product.findUnique.mockResolvedValue(null);

      const request: PricingRequest = {
        productId: 'non-existent-product',
        quantity: 1,
      };

      const result = await pricingEngine.calculatePrice(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Product not found');
    });

    it('should ensure non-negative pricing', async () => {
      const mockProduct = {
        id: 'test-product',
        name: 'Test T-Shirt',
        basePrice: 10.00,
        variants: [],
      };

      const { PrismaClient } = require('@prisma/client');
      const mockPrismaInstance = new PrismaClient();
      mockPrismaInstance.product.findUnique.mockResolvedValue(mockProduct);

      // Mock a large discount that would make price negative
      const mockRules = [
        {
          id: 'huge-discount',
          name: 'Huge Discount',
          type: 'quantity_discount',
          modifier: -50,
          modifierType: 'fixed',
          customerMessage: 'Huge savings',
          valueFraming: 'Amazing deal',
          priority: 1,
          conditions: [],
        },
      ];

      // We would need to mock the getApplicableRules method
      // For now, we'll test that totalPrice is never negative
      const request: PricingRequest = {
        productId: 'test-product',
        quantity: 1,
      };

      const result = await pricingEngine.calculatePrice(request);

      expect(result.success).toBe(true);
      expect(result.data!.totalPrice).toBeGreaterThanOrEqual(0);
    });
  });

  describe('previewPricing', () => {
    it('should handle multiple pricing scenarios', async () => {
      const mockProduct = {
        id: 'test-product',
        name: 'Test T-Shirt',
        basePrice: 29.99,
        variants: [],
      };

      const { PrismaClient } = require('@prisma/client');
      const mockPrismaInstance = new PrismaClient();
      mockPrismaInstance.product.findUnique.mockResolvedValue(mockProduct);

      const requests: PricingRequest[] = [
        { productId: 'test-product', quantity: 1 },
        { productId: 'test-product', quantity: 5 },
        { productId: 'test-product', quantity: 1, qualityTier: 'premium' },
      ];

      const results = await pricingEngine.previewPricing(requests);

      expect(results).toHaveLength(3);
      expect(results.every(r => r.success)).toBe(true);
    });
  });

  describe('configuration methods', () => {
    it('should return print size configuration', () => {
      const config = pricingEngine.getPrintSizeConfig();

      expect(config).toBeDefined();
      expect(config.small).toBeDefined();
      expect(config.medium).toBeDefined();
      expect(config.large).toBeDefined();
      expect(config.full_coverage).toBeDefined();

      // Check emotional messaging
      expect(config.small.valueMessage).toContain('simplicity');
      expect(config.large.valueMessage).toContain('attention');
    });

    it('should return quality tier configuration', () => {
      const config = pricingEngine.getQualityTierConfig();

      expect(config).toBeDefined();
      expect(config.standard).toBeDefined();
      expect(config.premium).toBeDefined();
      expect(config.luxury).toBeDefined();

      // Check emotional promises
      expect(config.premium.qualityPromise).toContain('premium');
      expect(config.luxury.qualityPromise).toContain('luxury');
    });
  });
});
