/**
 * AI Try-On Hook Tests
 * 
 * Tests for the useAiTryOn React hook
 */

import { renderHook, act, waitFor } from '@testing-library/react';
import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { useAiTryOn } from '@/hooks/useAiTryOn';

// Mock fetch
global.fetch = jest.fn();
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

// Mock FileReader
global.FileReader = jest.fn().mockImplementation(() => ({
  readAsDataURL: jest.fn(),
  onload: null,
  onerror: null,
  result: null,
}));

// Mock file
const createMockFile = (name: string, type: string, size: number): File => {
  const file = new File(['test content'], name, { type });
  Object.defineProperty(file, 'size', { value: size });
  return file;
};

const mockBase64Image = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=';

describe('useAiTryOn', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('selectImage', () => {
    it('should select and validate image successfully', async () => {
      const { result } = renderHook(() => useAiTryOn());
      const validFile = createMockFile('test.jpg', 'image/jpeg', 1024 * 1024); // 1MB

      // Mock FileReader
      const mockFileReader = {
        readAsDataURL: jest.fn(),
        onload: null,
        onerror: null,
        result: mockBase64Image,
      };
      (global.FileReader as jest.Mock).mockImplementation(() => mockFileReader);

      await act(async () => {
        await result.current.selectImage(validFile);
        
        // Simulate FileReader onload
        if (mockFileReader.onload) {
          mockFileReader.onload({ target: { result: mockBase64Image } } as any);
        }
      });

      expect(result.current.selectedImage).toBe(validFile);
      expect(result.current.imagePreview).toBe(mockBase64Image);
      expect(result.current.error).toBeNull();
      expect(result.current.isUploading).toBe(false);
    });

    it('should reject invalid file type', async () => {
      const { result } = renderHook(() => useAiTryOn());
      const invalidFile = createMockFile('test.txt', 'text/plain', 1024);

      await act(async () => {
        await result.current.selectImage(invalidFile);
      });

      expect(result.current.selectedImage).toBeNull();
      expect(result.current.error).toContain('Please select a valid image file');
      expect(result.current.isUploading).toBe(false);
    });

    it('should reject oversized file', async () => {
      const { result } = renderHook(() => useAiTryOn());
      const oversizedFile = createMockFile('test.jpg', 'image/jpeg', 10 * 1024 * 1024); // 10MB

      await act(async () => {
        await result.current.selectImage(oversizedFile);
      });

      expect(result.current.selectedImage).toBeNull();
      expect(result.current.error).toContain('Image file size must be less than 5MB');
      expect(result.current.isUploading).toBe(false);
    });
  });

  describe('clearImage', () => {
    it('should clear selected image and preview', async () => {
      const { result } = renderHook(() => useAiTryOn());
      const validFile = createMockFile('test.jpg', 'image/jpeg', 1024 * 1024);

      // First select an image
      const mockFileReader = {
        readAsDataURL: jest.fn(),
        onload: null,
        onerror: null,
        result: mockBase64Image,
      };
      (global.FileReader as jest.Mock).mockImplementation(() => mockFileReader);

      await act(async () => {
        await result.current.selectImage(validFile);
        if (mockFileReader.onload) {
          mockFileReader.onload({ target: { result: mockBase64Image } } as any);
        }
      });

      // Then clear it
      act(() => {
        result.current.clearImage();
      });

      expect(result.current.selectedImage).toBeNull();
      expect(result.current.imagePreview).toBeNull();
      expect(result.current.error).toBeNull();
    });
  });

  describe('generateTryOn', () => {
    it('should generate try-on successfully', async () => {
      const { result } = renderHook(() => useAiTryOn());
      const validFile = createMockFile('test.jpg', 'image/jpeg', 1024 * 1024);

      // Setup image first
      const mockFileReader = {
        readAsDataURL: jest.fn(),
        onload: null,
        onerror: null,
        result: mockBase64Image,
      };
      (global.FileReader as jest.Mock).mockImplementation(() => mockFileReader);

      await act(async () => {
        await result.current.selectImage(validFile);
        if (mockFileReader.onload) {
          mockFileReader.onload({ target: { result: mockBase64Image } } as any);
        }
      });

      // Mock successful API response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            jobId: 'job-123',
            status: 'COMPLETED',
            resultImageUrl: mockBase64Image,
            confidence: 0.85,
          },
        }),
      } as Response);

      await act(async () => {
        await result.current.generateTryOn('custom-123', 'user-123', 'outdoor');
      });

      expect(result.current.jobId).toBe('job-123');
      expect(result.current.status).toBe('COMPLETED');
      expect(result.current.resultImageUrl).toBe(mockBase64Image);
      expect(result.current.confidence).toBe(0.85);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isProcessing).toBe(false);
      expect(result.current.progress).toBe(100);
      expect(result.current.error).toBeNull();
    });

    it('should handle processing status with polling', async () => {
      const { result } = renderHook(() => useAiTryOn());
      const validFile = createMockFile('test.jpg', 'image/jpeg', 1024 * 1024);

      // Setup image first
      const mockFileReader = {
        readAsDataURL: jest.fn(),
        onload: null,
        onerror: null,
        result: mockBase64Image,
      };
      (global.FileReader as jest.Mock).mockImplementation(() => mockFileReader);

      await act(async () => {
        await result.current.selectImage(validFile);
        if (mockFileReader.onload) {
          mockFileReader.onload({ target: { result: mockBase64Image } } as any);
        }
      });

      // Mock initial processing response
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            success: true,
            data: {
              jobId: 'job-123',
              status: 'PROCESSING',
            },
          }),
        } as Response)
        // Mock polling response - completed
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            success: true,
            data: {
              jobId: 'job-123',
              status: 'COMPLETED',
              resultImageUrl: mockBase64Image,
              confidence: 0.85,
            },
          }),
        } as Response);

      await act(async () => {
        await result.current.generateTryOn('custom-123', 'user-123');
      });

      expect(result.current.jobId).toBe('job-123');
      expect(result.current.status).toBe('PROCESSING');
      expect(result.current.isProcessing).toBe(true);

      // Fast-forward polling interval
      await act(async () => {
        jest.advanceTimersByTime(2000);
        await waitFor(() => {
          expect(result.current.status).toBe('COMPLETED');
        });
      });

      expect(result.current.resultImageUrl).toBe(mockBase64Image);
      expect(result.current.confidence).toBe(0.85);
      expect(result.current.isProcessing).toBe(false);
    });

    it('should handle API errors', async () => {
      const { result } = renderHook(() => useAiTryOn());
      const validFile = createMockFile('test.jpg', 'image/jpeg', 1024 * 1024);

      // Setup image first
      const mockFileReader = {
        readAsDataURL: jest.fn(),
        onload: null,
        onerror: null,
        result: mockBase64Image,
      };
      (global.FileReader as jest.Mock).mockImplementation(() => mockFileReader);

      await act(async () => {
        await result.current.selectImage(validFile);
        if (mockFileReader.onload) {
          mockFileReader.onload({ target: { result: mockBase64Image } } as any);
        }
      });

      // Mock error response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: false,
          error: 'User not found',
        }),
      } as Response);

      await act(async () => {
        await result.current.generateTryOn('custom-123', 'invalid-user');
      });

      expect(result.current.error).toBe('User not found');
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isProcessing).toBe(false);
      expect(result.current.progress).toBe(0);
    });

    it('should require image selection', async () => {
      const { result } = renderHook(() => useAiTryOn());

      await act(async () => {
        await result.current.generateTryOn('custom-123', 'user-123');
      });

      expect(result.current.error).toBe('Please select an image first');
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('checkJobStatus', () => {
    it('should check job status successfully', async () => {
      const { result } = renderHook(() => useAiTryOn());

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            jobId: 'job-123',
            status: 'COMPLETED',
            resultImageUrl: mockBase64Image,
            confidence: 0.85,
          },
        }),
      } as Response);

      await act(async () => {
        await result.current.checkJobStatus('job-123');
      });

      expect(result.current.jobId).toBe('job-123');
      expect(result.current.status).toBe('COMPLETED');
      expect(result.current.resultImageUrl).toBe(mockBase64Image);
      expect(result.current.confidence).toBe(0.85);
    });
  });

  describe('cancelJob', () => {
    it('should cancel processing job', async () => {
      const { result } = renderHook(() => useAiTryOn());

      // Set processing state
      act(() => {
        result.current.generateTryOn('custom-123', 'user-123');
      });

      act(() => {
        result.current.cancelJob();
      });

      expect(result.current.isLoading).toBe(false);
      expect(result.current.isProcessing).toBe(false);
      expect(result.current.progress).toBe(0);
      expect(result.current.estimatedTimeRemaining).toBeNull();
    });
  });

  describe('reset', () => {
    it('should reset all state', async () => {
      const { result } = renderHook(() => useAiTryOn());
      const validFile = createMockFile('test.jpg', 'image/jpeg', 1024 * 1024);

      // Setup some state
      const mockFileReader = {
        readAsDataURL: jest.fn(),
        onload: null,
        onerror: null,
        result: mockBase64Image,
      };
      (global.FileReader as jest.Mock).mockImplementation(() => mockFileReader);

      await act(async () => {
        await result.current.selectImage(validFile);
        if (mockFileReader.onload) {
          mockFileReader.onload({ target: { result: mockBase64Image } } as any);
        }
      });

      // Reset
      act(() => {
        result.current.reset();
      });

      expect(result.current.jobId).toBeNull();
      expect(result.current.status).toBe('PENDING');
      expect(result.current.resultImageUrl).toBeNull();
      expect(result.current.confidence).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isUploading).toBe(false);
      expect(result.current.isProcessing).toBe(false);
      expect(result.current.error).toBeNull();
      expect(result.current.selectedImage).toBeNull();
      expect(result.current.imagePreview).toBeNull();
      expect(result.current.progress).toBe(0);
      expect(result.current.estimatedTimeRemaining).toBeNull();
    });
  });

  describe('clearError', () => {
    it('should clear error state', () => {
      const { result } = renderHook(() => useAiTryOn());

      // Set error state
      act(() => {
        result.current.selectImage(createMockFile('test.txt', 'text/plain', 1024));
      });

      expect(result.current.error).toBeTruthy();

      // Clear error
      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBeNull();
    });
  });
});
