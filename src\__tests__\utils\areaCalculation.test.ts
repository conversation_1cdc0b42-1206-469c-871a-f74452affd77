import {
  pixelsToCm,
  cmToPixels,
  getRotatedBoundingBox,
  calculateElementArea,
  rectanglesIntersect,
  calculateIntersectionArea,
  calculateTotalPrintArea,
  getPrintSizeCategory,
  isAreaWithinLimits,
  getPricingImpactMessage,
  calculateCanvasPrintArea,
  PRINT_CONFIG,
  PRINT_SIZE_CATEGORIES,
} from '@/lib/utils/areaCalculation';
import { DesignElement, DesignCanvas } from '@/types';

describe('Area Calculation Utilities', () => {
  // Mock design elements for testing
  const mockTextElement: DesignElement = {
    id: 'text-1',
    type: 'text',
    x: 100,
    y: 100,
    width: 200,
    height: 50,
    rotation: 0,
    opacity: 1,
    visible: true,
    locked: false,
    data: {
      text: 'Test Text',
      fontSize: 16,
      fontFamily: 'Arial',
      color: '#000000',
    },
  };

  const mockImageElement: DesignElement = {
    id: 'image-1',
    type: 'image',
    x: 200,
    y: 200,
    width: 150,
    height: 150,
    rotation: 45,
    opacity: 1,
    visible: true,
    locked: false,
    data: {
      src: 'test-image.jpg',
      originalWidth: 150,
      originalHeight: 150,
    },
  };

  const mockCanvas: DesignCanvas = {
    width: 800,
    height: 600,
    backgroundColor: '#ffffff',
    elements: [mockTextElement, mockImageElement],
    layers: ['text-1', 'image-1'],
  };

  describe('Unit Conversions', () => {
    test('pixelsToCm converts pixels to centimeters correctly', () => {
      // Test width conversion
      const widthCm = pixelsToCm(400, true); // Half canvas width
      expect(widthCm).toBeCloseTo(12.5); // Half of 25cm

      // Test height conversion
      const heightCm = pixelsToCm(300, false); // Half canvas height
      expect(heightCm).toBeCloseTo(10); // Half of 20cm
    });

    test('cmToPixels converts centimeters to pixels correctly', () => {
      // Test width conversion
      const widthPixels = cmToPixels(12.5, true);
      expect(widthPixels).toBeCloseTo(400);

      // Test height conversion
      const heightPixels = cmToPixels(10, false);
      expect(heightPixels).toBeCloseTo(300);
    });

    test('conversions are reversible', () => {
      const originalPixels = 320;
      const cm = pixelsToCm(originalPixels, true);
      const backToPixels = cmToPixels(cm, true);
      expect(backToPixels).toBeCloseTo(originalPixels);
    });
  });

  describe('Bounding Box Calculations', () => {
    test('getRotatedBoundingBox returns correct box for non-rotated element', () => {
      const bbox = getRotatedBoundingBox(mockTextElement);
      expect(bbox).toEqual({
        x: 100,
        y: 100,
        width: 200,
        height: 50,
      });
    });

    test('getRotatedBoundingBox calculates correct box for rotated element', () => {
      const bbox = getRotatedBoundingBox(mockImageElement);
      
      // For a 45-degree rotation, the bounding box should be larger
      expect(bbox.width).toBeGreaterThan(150);
      expect(bbox.height).toBeGreaterThan(150);
      
      // The center should remain the same
      const centerX = bbox.x + bbox.width / 2;
      const centerY = bbox.y + bbox.height / 2;
      const originalCenterX = mockImageElement.x + mockImageElement.width / 2;
      const originalCenterY = mockImageElement.y + mockImageElement.height / 2;
      
      expect(centerX).toBeCloseTo(originalCenterX, 1);
      expect(centerY).toBeCloseTo(originalCenterY, 1);
    });
  });

  describe('Element Area Calculations', () => {
    test('calculateElementArea returns correct area for visible element', () => {
      const area = calculateElementArea(mockTextElement);
      
      // Convert expected pixel area to cm²
      const expectedWidthCm = pixelsToCm(200, true);
      const expectedHeightCm = pixelsToCm(50, false);
      const expectedArea = expectedWidthCm * expectedHeightCm;
      
      expect(area).toBeCloseTo(expectedArea);
    });

    test('calculateElementArea returns 0 for invisible element', () => {
      const invisibleElement = { ...mockTextElement, visible: false };
      const area = calculateElementArea(invisibleElement);
      expect(area).toBe(0);
    });

    test('calculateElementArea returns 0 for transparent element', () => {
      const transparentElement = { ...mockTextElement, opacity: 0 };
      const area = calculateElementArea(transparentElement);
      expect(area).toBe(0);
    });
  });

  describe('Rectangle Intersection', () => {
    test('rectanglesIntersect detects overlapping rectangles', () => {
      const rect1 = { x: 0, y: 0, width: 100, height: 100 };
      const rect2 = { x: 50, y: 50, width: 100, height: 100 };
      
      expect(rectanglesIntersect(rect1, rect2)).toBe(true);
    });

    test('rectanglesIntersect detects non-overlapping rectangles', () => {
      const rect1 = { x: 0, y: 0, width: 50, height: 50 };
      const rect2 = { x: 100, y: 100, width: 50, height: 50 };
      
      expect(rectanglesIntersect(rect1, rect2)).toBe(false);
    });

    test('calculateIntersectionArea returns correct area', () => {
      const rect1 = { x: 0, y: 0, width: 100, height: 100 };
      const rect2 = { x: 50, y: 50, width: 100, height: 100 };
      
      const intersectionArea = calculateIntersectionArea(rect1, rect2);
      
      // Intersection should be 50x50 pixels
      const expectedWidthCm = pixelsToCm(50, true);
      const expectedHeightCm = pixelsToCm(50, false);
      const expectedArea = expectedWidthCm * expectedHeightCm;
      
      expect(intersectionArea).toBeCloseTo(expectedArea);
    });
  });

  describe('Print Size Categories', () => {
    test('getPrintSizeCategory returns correct categories', () => {
      expect(getPrintSizeCategory(10)).toBe('small');
      expect(getPrintSizeCategory(50)).toBe('medium');
      expect(getPrintSizeCategory(150)).toBe('large');
      expect(getPrintSizeCategory(400)).toBe('full_coverage');
    });

    test('isAreaWithinLimits validates area correctly', () => {
      expect(isAreaWithinLimits(0.5)).toBe(false); // Too small
      expect(isAreaWithinLimits(10)).toBe(true); // Valid
      expect(isAreaWithinLimits(600)).toBe(false); // Too large
    });
  });

  describe('Pricing Impact Messages', () => {
    test('getPricingImpactMessage detects category upgrades', () => {
      const impact = getPricingImpactMessage(20, 80); // small to medium
      
      expect(impact.hasImpact).toBe(true);
      expect(impact.type).toBe('positive');
      expect(impact.message).toContain('Medium Print');
      expect(impact.emotionalMessage).toContain('Bigger impact');
    });

    test('getPricingImpactMessage detects category downgrades', () => {
      const impact = getPricingImpactMessage(200, 80); // large to medium
      
      expect(impact.hasImpact).toBe(true);
      expect(impact.type).toBe('negative');
      expect(impact.message).toContain('Medium Print');
      expect(impact.emotionalMessage).toContain('Smaller print');
    });

    test('getPricingImpactMessage detects no change', () => {
      const impact = getPricingImpactMessage(20, 22); // both small
      
      expect(impact.hasImpact).toBe(false);
      expect(impact.type).toBe('neutral');
    });
  });

  describe('Canvas Area Calculation', () => {
    test('calculateCanvasPrintArea returns comprehensive data', () => {
      const result = calculateCanvasPrintArea(mockCanvas);
      
      expect(result.totalArea).toBeGreaterThan(0);
      expect(result.elementAreas).toHaveLength(2);
      expect(result.elementAreas[0].id).toBe('text-1');
      expect(result.elementAreas[1].id).toBe('image-1');
      expect(result.printSizeCategory).toBeOneOf(['small', 'medium', 'large', 'full_coverage']);
      expect(result.isWithinLimits).toBe(true);
    });

    test('calculateCanvasPrintArea handles empty canvas', () => {
      const emptyCanvas = { ...mockCanvas, elements: [] };
      const result = calculateCanvasPrintArea(emptyCanvas);
      
      expect(result.totalArea).toBe(0);
      expect(result.elementAreas).toHaveLength(0);
      expect(result.printSizeCategory).toBe('small');
      expect(result.isWithinLimits).toBe(true);
    });

    test('calculateCanvasPrintArea includes recommendations for large overlaps', () => {
      // Create overlapping elements
      const overlappingElement = { ...mockTextElement, x: 150, y: 120 };
      const canvasWithOverlap = {
        ...mockCanvas,
        elements: [mockTextElement, overlappingElement],
      };
      
      const result = calculateCanvasPrintArea(canvasWithOverlap);
      
      if (result.overlappingArea > result.totalArea * 0.3) {
        expect(result.recommendations).toBeDefined();
        expect(result.recommendations).toContain(
          expect.stringContaining('overlapping')
        );
      }
    });
  });

  describe('Edge Cases', () => {
    test('handles elements with zero dimensions', () => {
      const zeroElement = { ...mockTextElement, width: 0, height: 0 };
      const area = calculateElementArea(zeroElement);
      expect(area).toBe(0);
    });

    test('handles elements outside canvas bounds', () => {
      const outsideElement = { ...mockTextElement, x: -100, y: -100 };
      const area = calculateElementArea(outsideElement);
      expect(area).toBeGreaterThan(0); // Should still calculate area
    });

    test('handles very large rotations', () => {
      const largeRotationElement = { ...mockImageElement, rotation: 720 }; // 2 full rotations
      const bbox = getRotatedBoundingBox(largeRotationElement);
      
      // Should be equivalent to 0 degrees rotation
      const normalBbox = getRotatedBoundingBox({ ...mockImageElement, rotation: 0 });
      expect(bbox.width).toBeCloseTo(normalBbox.width, 1);
      expect(bbox.height).toBeCloseTo(normalBbox.height, 1);
    });
  });

  describe('Performance', () => {
    test('calculateCanvasPrintArea performs well with many elements', () => {
      // Create canvas with 100 elements
      const manyElements = Array.from({ length: 100 }, (_, i) => ({
        ...mockTextElement,
        id: `element-${i}`,
        x: (i % 10) * 80,
        y: Math.floor(i / 10) * 60,
      }));
      
      const largeCanvas = { ...mockCanvas, elements: manyElements };
      
      const startTime = performance.now();
      const result = calculateCanvasPrintArea(largeCanvas);
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(100); // Should complete in under 100ms
      expect(result.elementAreas).toHaveLength(100);
    });
  });
});

// Custom Jest matchers
expect.extend({
  toBeOneOf(received: any, expected: any[]) {
    const pass = expected.includes(received);
    if (pass) {
      return {
        message: () => `expected ${received} not to be one of ${expected}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be one of ${expected}`,
        pass: false,
      };
    }
  },
});
