'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Container, Section } from '@/components/layout';
import { Button } from '@/components/ui';
import { AiTryOnProductsTab } from '@/components/admin/AiTryOnProductsTab';
import { AiTryOnFallbackQueueTab } from '@/components/admin/AiTryOnFallbackQueueTab';
import { AiTryOnAnalyticsTab } from '@/components/admin/AiTryOnAnalyticsTab';
import { AiTryOnSystemStatus } from '@/components/admin/AiTryOnSystemStatus';

type TabType = 'products' | 'queue' | 'analytics' | 'settings';

interface SystemStats {
  totalProducts: number;
  aiTryOnEnabledProducts: number;
  totalJobs: number;
  successRate: number;
  queueLength: number;
  averageProcessingTime: number;
}

export default function AdminAiTryOnPage() {
  const [activeTab, setActiveTab] = useState<TabType>('products');
  const [systemStats, setSystemStats] = useState<SystemStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    fetchSystemStats();
  }, [refreshKey]);

  const fetchSystemStats = async () => {
    try {
      const response = await fetch('/api/admin/ai-tryon/stats');
      const data = await response.json();
      if (data.success) {
        setSystemStats(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch system stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  const tabs = [
    { id: 'products', label: 'Product Settings', icon: '🎨' },
    { id: 'queue', label: 'Fallback Queue', icon: '⚡' },
    { id: 'analytics', label: 'Analytics', icon: '📊' },
    { id: 'settings', label: 'System Settings', icon: '⚙️' },
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-warm-50 to-amber-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-warm-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading AI Try-On Dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-warm-50 to-amber-50">
      <Section variant="primary" padding="lg">
        <Container>
          {/* Header */}
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6 mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                AI Try-On Management
              </h1>
              <p className="text-gray-600">
                Manage AI try-on settings, monitor system health, and handle fallback processing
              </p>
            </div>
            
            <div className="flex space-x-3">
              <Button
                onClick={handleRefresh}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <span>🔄</span>
                <span>Refresh</span>
              </Button>
              
              <Button
                onClick={() => window.open('/api/admin/ai-tryon/export', '_blank')}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <span>📊</span>
                <span>Export Data</span>
              </Button>
            </div>
          </div>

          {/* System Status Overview */}
          {systemStats && (
            <AiTryOnSystemStatus 
              stats={systemStats} 
              onRefresh={handleRefresh}
              className="mb-8"
            />
          )}

          {/* Tab Navigation */}
          <div className="border-b border-gray-200 mb-8">
            <nav className="-mb-px flex space-x-8 overflow-x-auto">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as TabType)}
                  className={`
                    whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2
                    ${activeTab === tab.id
                      ? 'border-warm-500 text-warm-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  <span>{tab.icon}</span>
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="space-y-6">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
              >
                {activeTab === 'products' && (
                  <AiTryOnProductsTab onRefresh={handleRefresh} />
                )}
                {activeTab === 'queue' && (
                  <AiTryOnFallbackQueueTab onRefresh={handleRefresh} />
                )}
                {activeTab === 'analytics' && (
                  <AiTryOnAnalyticsTab />
                )}
                {activeTab === 'settings' && (
                  <AiTryOnSystemSettingsTab onRefresh={handleRefresh} />
                )}
              </motion.div>
            </AnimatePresence>
          </div>
        </Container>
      </Section>
    </div>
  );
}

// System Settings Tab Component
function AiTryOnSystemSettingsTab({ onRefresh }: { onRefresh: () => void }) {
  const [settings, setSettings] = useState({
    maxConcurrentJobs: 5,
    fallbackThreshold: 3,
    autoRetryEnabled: true,
    notificationsEnabled: true,
    maintenanceMode: false,
  });

  const [saving, setSaving] = useState(false);

  const handleSaveSettings = async () => {
    setSaving(true);
    try {
      const response = await fetch('/api/admin/ai-tryon/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings)
      });

      if (response.ok) {
        onRefresh();
        // Show success message
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">System Configuration</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Max Concurrent Jobs
            </label>
            <input
              type="number"
              min="1"
              max="20"
              value={settings.maxConcurrentJobs}
              onChange={(e) => setSettings(prev => ({ 
                ...prev, 
                maxConcurrentJobs: parseInt(e.target.value) 
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-warm-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Fallback Threshold (retries)
            </label>
            <input
              type="number"
              min="1"
              max="10"
              value={settings.fallbackThreshold}
              onChange={(e) => setSettings(prev => ({ 
                ...prev, 
                fallbackThreshold: parseInt(e.target.value) 
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-warm-500"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="autoRetry"
              checked={settings.autoRetryEnabled}
              onChange={(e) => setSettings(prev => ({ 
                ...prev, 
                autoRetryEnabled: e.target.checked 
              }))}
              className="h-4 w-4 text-warm-600 focus:ring-warm-500 border-gray-300 rounded"
            />
            <label htmlFor="autoRetry" className="ml-2 block text-sm text-gray-900">
              Enable Auto Retry
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="notifications"
              checked={settings.notificationsEnabled}
              onChange={(e) => setSettings(prev => ({ 
                ...prev, 
                notificationsEnabled: e.target.checked 
              }))}
              className="h-4 w-4 text-warm-600 focus:ring-warm-500 border-gray-300 rounded"
            />
            <label htmlFor="notifications" className="ml-2 block text-sm text-gray-900">
              Enable Admin Notifications
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="maintenance"
              checked={settings.maintenanceMode}
              onChange={(e) => setSettings(prev => ({ 
                ...prev, 
                maintenanceMode: e.target.checked 
              }))}
              className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
            />
            <label htmlFor="maintenance" className="ml-2 block text-sm text-gray-900">
              Maintenance Mode (Disable AI Try-On)
            </label>
          </div>
        </div>

        <div className="mt-6 flex justify-end">
          <Button
            onClick={handleSaveSettings}
            disabled={saving}
            className="flex items-center space-x-2"
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Saving...</span>
              </>
            ) : (
              <>
                <span>💾</span>
                <span>Save Settings</span>
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
