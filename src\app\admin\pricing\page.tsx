'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Container } from '@/components/layout/Container';
import { Section } from '@/components/layout/Section';
import { PricingRule, PricingRequest, PricingResponse } from '@/types/pricing';
import { PricingFormatter } from '@/lib/pricing/utils';

export default function AdminPricingPage() {
  const [rules, setRules] = useState<PricingRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'rules' | 'preview' | 'analytics'>('rules');

  useEffect(() => {
    fetchPricingRules();
  }, []);

  const fetchPricingRules = async () => {
    try {
      const response = await fetch('/api/pricing/rules');
      const data = await response.json();
      if (data.success) {
        setRules(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch pricing rules:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Section variant="default" padding="lg">
        <Container>
          <div className="space-y-8">
            {/* Header */}
            <div className="text-center space-y-4">
              <h1 className="text-4xl font-bold text-gray-900">
                Pricing Management
              </h1>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Create transparent, emotion-driven pricing that builds trust and communicates value
              </p>
            </div>

            {/* Navigation Tabs */}
            <div className="flex justify-center">
              <div className="bg-white rounded-lg p-1 shadow-sm border">
                {[
                  { key: 'rules', label: 'Pricing Rules', icon: '⚙️' },
                  { key: 'preview', label: 'Preview & Test', icon: '👁️' },
                  { key: 'analytics', label: 'Analytics', icon: '📊' }
                ].map((tab) => (
                  <button
                    key={tab.key}
                    onClick={() => setActiveTab(tab.key as any)}
                    className={`px-6 py-3 rounded-md font-medium transition-all ${
                      activeTab === tab.key
                        ? 'bg-primary-500 text-white shadow-sm'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <span className="mr-2">{tab.icon}</span>
                    {tab.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Content */}
            <div className="space-y-6">
              {activeTab === 'rules' && <PricingRulesTab rules={rules} onRulesChange={fetchPricingRules} />}
              {activeTab === 'preview' && <PricingPreviewTab />}
              {activeTab === 'analytics' && <PricingAnalyticsTab />}
            </div>
          </div>
        </Container>
      </Section>
    </div>
  );
}

// Pricing Rules Tab Component
function PricingRulesTab({ rules, onRulesChange }: { rules: PricingRule[]; onRulesChange: () => void }) {
  const [showRuleBuilder, setShowRuleBuilder] = useState(false);

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-gray-900">Active Pricing Rules</h2>
        <Button onClick={() => setShowRuleBuilder(true)}>
          ✨ Create New Rule
        </Button>
      </div>

      {/* Rules List */}
      <div className="grid gap-4">
        {rules.map((rule) => (
          <Card key={rule.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{rule.name}</CardTitle>
                  <p className="text-gray-600 mt-1">{rule.description}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    rule.isActive 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {rule.isActive ? 'Active' : 'Inactive'}
                  </span>
                  <span className="text-sm text-gray-500">
                    Priority: {rule.priority}
                  </span>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Rule Details</h4>
                  <div className="space-y-1 text-sm text-gray-600">
                    <p><strong>Type:</strong> {rule.type.replace('_', ' ')}</p>
                    <p><strong>Modifier:</strong> {
                      rule.modifierType === 'percentage' 
                        ? PricingFormatter.formatPercentage(rule.modifier)
                        : PricingFormatter.formatCurrency(rule.modifier)
                    }</p>
                    <p><strong>Conditions:</strong> {rule.conditions?.length || 0} rules</p>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Customer Messaging</h4>
                  <div className="space-y-1 text-sm">
                    <p className="text-gray-800">"{rule.customerMessage}"</p>
                    <p className="text-gray-600 italic">{rule.valueFraming}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {rules.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">⚙️</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Pricing Rules</h3>
            <p className="text-gray-600 mb-4">Create your first pricing rule to get started</p>
            <Button onClick={() => setShowRuleBuilder(true)}>
              Create First Rule
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Rule Builder Modal would go here */}
      {showRuleBuilder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-semibold">Create Pricing Rule</h3>
                <button 
                  onClick={() => setShowRuleBuilder(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
              <p className="text-gray-600 mb-6">
                Build rules that transparently communicate value to customers
              </p>
              {/* Rule builder form would go here */}
              <div className="text-center py-8 text-gray-500">
                Rule builder form coming soon...
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Pricing Preview Tab Component
function PricingPreviewTab() {
  const [previewRequest, setPreviewRequest] = useState<Partial<PricingRequest>>({
    productId: '',
    quantity: 1,
    printSize: 'medium',
    qualityTier: 'standard'
  });
  const [previewResult, setPreviewResult] = useState<PricingResponse | null>(null);
  const [loading, setLoading] = useState(false);

  const handlePreview = async () => {
    if (!previewRequest.productId) return;
    
    setLoading(true);
    try {
      const response = await fetch('/api/pricing/compute', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(previewRequest)
      });
      const result = await response.json();
      setPreviewResult(result);
    } catch (error) {
      console.error('Preview failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="grid lg:grid-cols-2 gap-8">
      {/* Preview Form */}
      <Card>
        <CardHeader>
          <CardTitle>Test Pricing Scenario</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Input
            label="Product ID"
            value={previewRequest.productId || ''}
            onChange={(e) => setPreviewRequest(prev => ({ ...prev, productId: e.target.value }))}
            placeholder="Enter product ID to test"
          />
          
          <Input
            label="Quantity"
            type="number"
            value={previewRequest.quantity || 1}
            onChange={(e) => setPreviewRequest(prev => ({ ...prev, quantity: parseInt(e.target.value) }))}
            min="1"
            max="100"
          />

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Print Size</label>
              <select 
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                value={previewRequest.printSize || 'medium'}
                onChange={(e) => setPreviewRequest(prev => ({ ...prev, printSize: e.target.value as any }))}
              >
                <option value="small">Small</option>
                <option value="medium">Medium</option>
                <option value="large">Large</option>
                <option value="full_coverage">Full Coverage</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Quality Tier</label>
              <select 
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                value={previewRequest.qualityTier || 'standard'}
                onChange={(e) => setPreviewRequest(prev => ({ ...prev, qualityTier: e.target.value as any }))}
              >
                <option value="standard">Standard</option>
                <option value="premium">Premium</option>
                <option value="luxury">Luxury</option>
              </select>
            </div>
          </div>

          <Button 
            onClick={handlePreview} 
            disabled={!previewRequest.productId || loading}
            fullWidth
          >
            {loading ? 'Calculating...' : '🧮 Calculate Pricing'}
          </Button>
        </CardContent>
      </Card>

      {/* Preview Results */}
      <Card>
        <CardHeader>
          <CardTitle>Pricing Preview</CardTitle>
        </CardHeader>
        <CardContent>
          {previewResult ? (
            previewResult.success ? (
              <div className="space-y-4">
                <div className="text-center p-4 bg-primary-50 rounded-lg">
                  <div className="text-3xl font-bold text-primary-600">
                    {PricingFormatter.formatCurrency(previewResult.data!.totalPrice)}
                  </div>
                  <p className="text-primary-700 mt-1">{previewResult.data!.valueMessage}</p>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Price Breakdown</h4>
                  {previewResult.data!.breakdown.map((component, index) => (
                    <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100">
                      <div>
                        <span className="font-medium">{component.name}</span>
                        <p className="text-sm text-gray-600">{component.valueMessage}</p>
                      </div>
                      <span className={`font-medium ${
                        component.type === 'discount' ? 'text-green-600' : 'text-gray-900'
                      }`}>
                        {component.type === 'discount' ? '-' : ''}
                        {PricingFormatter.formatCurrency(Math.abs(component.amount))}
                      </span>
                    </div>
                  ))}
                </div>

                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>Quality Promise:</strong> {previewResult.data!.qualityPromise}
                  </p>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-red-600">
                <div className="text-4xl mb-2">❌</div>
                <p>{previewResult.error}</p>
              </div>
            )
          ) : (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">🧮</div>
              <p>Enter product details to see pricing preview</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Pricing Analytics Tab Component  
function PricingAnalyticsTab() {
  return (
    <div className="grid lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Pricing Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <div className="text-4xl mb-2">📊</div>
            <p>Analytics dashboard coming soon...</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Revenue Impact</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <div className="text-4xl mb-2">💰</div>
            <p>Revenue analytics coming soon...</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
