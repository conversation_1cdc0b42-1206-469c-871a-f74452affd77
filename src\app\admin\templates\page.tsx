'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Container, Section } from '@/components/layout';
import { Button } from '@/components/ui';
import { TemplateAdminCard } from '@/components/admin/TemplateAdminCard';
import { TemplateCreateModal } from '@/components/admin/TemplateCreateModal';
import { TemplateEditModal } from '@/components/admin/TemplateEditModal';
import { TemplateAnalytics } from '@/components/admin/TemplateAnalytics';

interface AdminTemplate {
  id: string;
  name: string;
  description: string;
  moodTag: string;
  styleKeywords: string[];
  targetAudience: string;
  designData: any;
  previewImage: string;
  lifestyleContext: string[];
  usageCount: number;
  isFeatured: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  product: {
    id: string;
    name: string;
    category: string;
    subcategory?: string;
    basePrice: number;
    heroImage: string;
  };
}

export default function AdminTemplatesPage() {
  const [templates, setTemplates] = useState<AdminTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<AdminTemplate | null>(null);
  const [activeTab, setActiveTab] = useState<'all' | 'active' | 'featured' | 'inactive' | 'analytics'>('all');

  // Fetch templates
  const fetchTemplates = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        limit: '50',
        sortBy: 'recent',
        sortOrder: 'desc',
      });

      const response = await fetch(`/api/templates?${params}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch templates');
      }

      if (data.success) {
        setTemplates(data.data.templates);
      } else {
        throw new Error(data.error || 'Failed to fetch templates');
      }
    } catch (err) {
      console.error('Error fetching templates:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch templates');
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchTemplates();
  }, []);

  // Handle template deletion
  const handleDeleteTemplate = async (templateId: string) => {
    if (!confirm('Are you sure you want to delete this template?')) {
      return;
    }

    try {
      const response = await fetch(`/api/templates/${templateId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete template');
      }

      // Refresh templates list
      await fetchTemplates();
    } catch (error) {
      console.error('Error deleting template:', error);
      alert('Failed to delete template. Please try again.');
    }
  };

  // Handle template toggle (active/inactive)
  const handleToggleTemplate = async (templateId: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/templates/${templateId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isActive: !isActive,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update template');
      }

      // Refresh templates list
      await fetchTemplates();
    } catch (error) {
      console.error('Error updating template:', error);
      alert('Failed to update template. Please try again.');
    }
  };

  // Handle template feature toggle
  const handleToggleFeatured = async (templateId: string, isFeatured: boolean) => {
    try {
      const response = await fetch(`/api/templates/${templateId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isFeatured: !isFeatured,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update template');
      }

      // Refresh templates list
      await fetchTemplates();
    } catch (error) {
      console.error('Error updating template:', error);
      alert('Failed to update template. Please try again.');
    }
  };

  // Filter templates based on active tab
  const filteredTemplates = templates.filter(template => {
    switch (activeTab) {
      case 'active':
        return template.isActive;
      case 'featured':
        return template.isFeatured;
      case 'inactive':
        return !template.isActive;
      case 'analytics':
        return true; // Not used for analytics tab
      default:
        return true;
    }
  });

  // Calculate stats
  const stats = {
    total: templates.length,
    active: templates.filter(t => t.isActive).length,
    featured: templates.filter(t => t.isFeatured).length,
    inactive: templates.filter(t => !t.isActive).length,
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-warm-50 to-amber-50">
      <Section variant="primary" padding="lg">
        <Container>
          {/* Header */}
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6 mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Template Management
              </h1>
              <p className="text-gray-600">
                Manage your ready-made template collection
              </p>
            </div>
            
            <Button
              onClick={() => setShowCreateModal(true)}
              className="w-full lg:w-auto"
            >
              ✨ Create New Template
            </Button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            {[
              { label: 'Total Templates', value: stats.total, color: 'bg-blue-500' },
              { label: 'Active', value: stats.active, color: 'bg-green-500' },
              { label: 'Featured', value: stats.featured, color: 'bg-yellow-500' },
              { label: 'Inactive', value: stats.inactive, color: 'bg-gray-500' },
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-lg p-4 shadow-sm border border-gray-200"
              >
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${stat.color}`}></div>
                  <div>
                    <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                    <div className="text-sm text-gray-600">{stat.label}</div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Tabs */}
          <div className="flex justify-center mb-8">
            <div className="bg-white rounded-lg p-1 shadow-sm border border-gray-200">
              {[
                { key: 'all', label: 'All Templates', count: stats.total },
                { key: 'active', label: 'Active', count: stats.active },
                { key: 'featured', label: 'Featured', count: stats.featured },
                { key: 'inactive', label: 'Inactive', count: stats.inactive },
                { key: 'analytics', label: 'Analytics', count: null },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`px-4 py-2 rounded-md font-medium transition-all ${
                    activeTab === tab.key
                      ? 'bg-primary-500 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  {tab.label} {tab.count !== null && `(${tab.count})`}
                </button>
              ))}
            </div>
          </div>

          {/* Content */}
          {loading ? (
            <div className="flex items-center justify-center py-16">
              <motion.div
                className="w-12 h-12 border-4 border-primary-200 border-t-primary-500 rounded-full"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
              />
              <span className="ml-3 text-gray-600">Loading templates...</span>
            </div>
          ) : error ? (
            <div className="text-center py-16">
              <div className="text-red-600 mb-4">⚠️ {error}</div>
              <Button onClick={fetchTemplates}>
                Try Again
              </Button>
            </div>
          ) : activeTab === 'analytics' ? (
            <TemplateAnalytics />
          ) : filteredTemplates.length === 0 ? (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">📝</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                No templates found
              </h3>
              <p className="text-gray-600 mb-6">
                {activeTab === 'all'
                  ? 'Get started by creating your first template'
                  : `No ${activeTab} templates found`
                }
              </p>
              {activeTab === 'all' && (
                <Button onClick={() => setShowCreateModal(true)}>
                  Create First Template
                </Button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <AnimatePresence>
                {filteredTemplates.map((template, index) => (
                  <motion.div
                    key={template.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.1 }}
                    layout
                  >
                    <TemplateAdminCard
                      template={template}
                      onEdit={() => setEditingTemplate(template)}
                      onDelete={() => handleDeleteTemplate(template.id)}
                      onToggleActive={() => handleToggleTemplate(template.id, template.isActive)}
                      onToggleFeatured={() => handleToggleFeatured(template.id, template.isFeatured)}
                    />
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          )}
        </Container>
      </Section>

      {/* Create Modal */}
      <TemplateCreateModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={() => {
          setShowCreateModal(false);
          fetchTemplates();
        }}
      />

      {/* Edit Modal */}
      <TemplateEditModal
        template={editingTemplate}
        isOpen={!!editingTemplate}
        onClose={() => setEditingTemplate(null)}
        onSuccess={() => {
          setEditingTemplate(null);
          fetchTemplates();
        }}
      />
    </div>
  );
}
