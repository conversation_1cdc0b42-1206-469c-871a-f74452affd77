import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const prisma = new PrismaClient();

// GET /api/admin/ai-tryon/analytics - Get detailed analytics
export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const range = searchParams.get('range') || '30d';

    // Calculate date range
    const now = new Date();
    let startDate: Date;
    
    switch (range) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default: // 30d
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // Get overview statistics
    const [totalJobs, successfulJobs, failedJobs] = await Promise.all([
      prisma.aiTryOnJob.count({
        where: { createdAt: { gte: startDate } }
      }),
      prisma.aiTryOnJob.count({
        where: {
          createdAt: { gte: startDate },
          status: { in: ['COMPLETED', 'FALLBACK_COMPLETED'] }
        }
      }),
      prisma.aiTryOnJob.count({
        where: {
          createdAt: { gte: startDate },
          status: { in: ['FAILED', 'QUEUED_FOR_FALLBACK'] }
        }
      })
    ]);

    // Get processing time and confidence averages
    const processingStats = await prisma.aiTryOnJob.aggregate({
      _avg: {
        processingTime: true,
        confidence: true
      },
      where: {
        createdAt: { gte: startDate },
        status: { in: ['COMPLETED', 'FALLBACK_COMPLETED'] },
        processingTime: { not: null },
        confidence: { not: null }
      }
    });

    // Get unique users count
    const uniqueUsers = await prisma.aiTryOnJob.findMany({
      where: { createdAt: { gte: startDate } },
      select: { userId: true },
      distinct: ['userId']
    });

    // Get active products count
    const activeProducts = await prisma.product.count({
      where: {
        aiTryOnEnabled: true,
        customizations: {
          some: {
            aiTryOnJobs: {
              some: {
                createdAt: { gte: startDate }
              }
            }
          }
        }
      }
    });

    // Generate daily trends
    const dailyJobs = [];
    const days = range === '7d' ? 7 : range === '90d' ? 90 : 30;
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const nextDate = new Date(date.getTime() + 24 * 60 * 60 * 1000);
      
      const [total, successful, failed] = await Promise.all([
        prisma.aiTryOnJob.count({
          where: {
            createdAt: { gte: date, lt: nextDate }
          }
        }),
        prisma.aiTryOnJob.count({
          where: {
            createdAt: { gte: date, lt: nextDate },
            status: { in: ['COMPLETED', 'FALLBACK_COMPLETED'] }
          }
        }),
        prisma.aiTryOnJob.count({
          where: {
            createdAt: { gte: date, lt: nextDate },
            status: { in: ['FAILED', 'QUEUED_FOR_FALLBACK'] }
          }
        })
      ]);

      dailyJobs.push({
        date: date.toISOString().split('T')[0],
        total,
        successful,
        failed
      });
    }

    // Get category performance
    const categoryPerformance = await prisma.$queryRaw`
      SELECT 
        p.category,
        COUNT(atj.id) as jobs,
        AVG(CASE WHEN atj.status IN ('COMPLETED', 'FALLBACK_COMPLETED') THEN 1.0 ELSE 0.0 END) as success_rate,
        AVG(atj.confidence) as avg_confidence
      FROM "ai_try_on_jobs" atj
      JOIN "customizations" c ON atj."customizationId" = c.id
      JOIN "products" p ON c."productId" = p.id
      WHERE atj."createdAt" >= ${startDate}
      GROUP BY p.category
      ORDER BY jobs DESC
    ` as Array<{
      category: string;
      jobs: bigint;
      success_rate: number;
      avg_confidence: number;
    }>;

    // Get user engagement trends
    const userEngagement = [];
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const nextDate = new Date(date.getTime() + 24 * 60 * 60 * 1000);
      
      const dayUsers = await prisma.aiTryOnJob.findMany({
        where: {
          createdAt: { gte: date, lt: nextDate }
        },
        select: { userId: true },
        distinct: ['userId']
      });

      // Check for repeat users (users who had jobs before this day)
      const repeatUsers = await prisma.aiTryOnJob.findMany({
        where: {
          createdAt: { gte: date, lt: nextDate },
          userId: {
            in: await prisma.aiTryOnJob.findMany({
              where: {
                createdAt: { lt: date }
              },
              select: { userId: true },
              distinct: ['userId']
            }).then(users => users.map(u => u.userId))
          }
        },
        select: { userId: true },
        distinct: ['userId']
      });

      userEngagement.push({
        date: date.toISOString().split('T')[0],
        uniqueUsers: dayUsers.length,
        repeatUsers: repeatUsers.length
      });
    }

    // Get top performing products
    const topProducts = await prisma.$queryRaw`
      SELECT 
        p.id,
        p.name,
        p.category,
        p."heroImage",
        COUNT(atj.id) as total_jobs,
        AVG(CASE WHEN atj.status IN ('COMPLETED', 'FALLBACK_COMPLETED') THEN 1.0 ELSE 0.0 END) as success_rate,
        AVG(atj.confidence) as avg_confidence
      FROM "products" p
      JOIN "customizations" c ON p.id = c."productId"
      JOIN "ai_try_on_jobs" atj ON c.id = atj."customizationId"
      WHERE atj."createdAt" >= ${startDate}
      GROUP BY p.id, p.name, p.category, p."heroImage"
      ORDER BY total_jobs DESC
      LIMIT 10
    ` as Array<{
      id: string;
      name: string;
      category: string;
      heroImage: string;
      total_jobs: bigint;
      success_rate: number;
      avg_confidence: number;
    }>;

    // Get recent failures
    const recentFailures = await prisma.aiTryOnJob.findMany({
      where: {
        createdAt: { gte: startDate },
        status: { in: ['FAILED', 'QUEUED_FOR_FALLBACK'] }
      },
      select: {
        id: true,
        errorMessage: true,
        retryCount: true,
        createdAt: true,
        customization: {
          select: {
            product: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 20
    });

    // Compile analytics data
    const analyticsData = {
      overview: {
        totalJobs,
        successfulJobs,
        failedJobs,
        averageProcessingTime: Math.round(processingStats._avg.processingTime || 0),
        averageConfidence: processingStats._avg.confidence || 0,
        totalUsers: uniqueUsers.length,
        activeProducts
      },
      trends: {
        dailyJobs,
        categoryPerformance: categoryPerformance.map(cat => ({
          category: cat.category,
          jobs: Number(cat.jobs),
          successRate: cat.success_rate || 0,
          avgConfidence: cat.avg_confidence || 0
        })),
        userEngagement
      },
      topProducts: topProducts.map(product => ({
        id: product.id,
        name: product.name,
        category: product.category,
        heroImage: product.heroImage,
        totalJobs: Number(product.total_jobs),
        successRate: product.success_rate || 0,
        avgConfidence: product.avg_confidence || 0
      })),
      recentFailures: recentFailures.map(failure => ({
        id: failure.id,
        productName: failure.customization.product.name,
        errorMessage: failure.errorMessage || 'Unknown error',
        retryCount: failure.retryCount,
        createdAt: failure.createdAt.toISOString()
      }))
    };

    return NextResponse.json({
      success: true,
      data: analyticsData
    });

  } catch (error) {
    console.error('Error fetching AI try-on analytics:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch analytics data' },
      { status: 500 }
    );
  }
}
