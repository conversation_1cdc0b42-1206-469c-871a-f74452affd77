import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const prisma = new PrismaClient();

// Validation schemas
const ClaimJobSchema = z.object({
  queueId: z.string().min(1, 'Queue ID is required'),
  estimatedCompletion: z.string().datetime().optional(),
});

const UpdateJobSchema = z.object({
  queueId: z.string().min(1, 'Queue ID is required'),
  status: z.enum(['PENDING', 'CLAIMED', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED']),
  processingNotes: z.string().optional(),
  colabNotebookUrl: z.string().url().optional(),
  estimatedCompletion: z.string().datetime().optional(),
});

const CompleteJobSchema = z.object({
  queueId: z.string().min(1, 'Queue ID is required'),
  resultImageUrl: z.string().min(1, 'Result image URL is required'),
  processingNotes: z.string().optional(),
  confidence: z.number().min(0).max(1).optional(),
});

// GET /api/admin/ai-tryon/fallback-queue - Get fallback queue items
export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status') || '';
    const assignedTo = searchParams.get('assignedTo') || '';
    const priority = searchParams.get('priority') || '';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (status) {
      where.status = status;
    }
    
    if (assignedTo) {
      where.assignedTo = assignedTo;
    }
    
    if (priority) {
      where.priority = parseInt(priority);
    }

    // Get fallback queue items
    const [queueItems, total] = await Promise.all([
      prisma.aiTryOnFallbackQueue.findMany({
        where,
        skip,
        take: limit,
        include: {
          aiTryOnJob: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  image: true
                }
              },
              customization: {
                include: {
                  product: {
                    select: {
                      id: true,
                      name: true,
                      category: true,
                      heroImage: true
                    }
                  }
                }
              }
            }
          }
        },
        orderBy: [
          { priority: 'asc' },
          { createdAt: 'asc' }
        ]
      }),
      prisma.aiTryOnFallbackQueue.count({ where })
    ]);

    // Get queue statistics
    const stats = await prisma.aiTryOnFallbackQueue.groupBy({
      by: ['status'],
      _count: {
        id: true
      }
    });

    const queueStats = {
      total,
      pending: stats.find(s => s.status === 'PENDING')?._count.id || 0,
      claimed: stats.find(s => s.status === 'CLAIMED')?._count.id || 0,
      processing: stats.find(s => s.status === 'PROCESSING')?._count.id || 0,
      completed: stats.find(s => s.status === 'COMPLETED')?._count.id || 0,
      failed: stats.find(s => s.status === 'FAILED')?._count.id || 0,
      cancelled: stats.find(s => s.status === 'CANCELLED')?._count.id || 0,
    };

    return NextResponse.json({
      success: true,
      data: {
        queueItems,
        stats: queueStats,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Error fetching fallback queue:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch fallback queue' },
      { status: 500 }
    );
  }
}

// POST /api/admin/ai-tryon/fallback-queue - Claim a job from the queue
export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = ClaimJobSchema.parse(body);

    // Claim the job
    const updatedQueueItem = await prisma.aiTryOnFallbackQueue.update({
      where: { 
        id: validatedData.queueId,
        status: 'PENDING' // Only allow claiming pending jobs
      },
      data: {
        status: 'CLAIMED',
        assignedTo: session.user.id,
        estimatedCompletion: validatedData.estimatedCompletion 
          ? new Date(validatedData.estimatedCompletion) 
          : undefined
      },
      include: {
        aiTryOnJob: {
          include: {
            customization: {
              include: {
                product: {
                  select: {
                    name: true,
                    category: true
                  }
                }
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedQueueItem,
      message: 'Job claimed successfully'
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error claiming fallback job:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to claim job' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/ai-tryon/fallback-queue - Update job status/details
export async function PUT(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = UpdateJobSchema.parse(body);

    // Update the job
    const updateData: any = {
      status: validatedData.status,
    };

    if (validatedData.processingNotes) {
      updateData.processingNotes = validatedData.processingNotes;
    }

    if (validatedData.colabNotebookUrl) {
      updateData.colabNotebookUrl = validatedData.colabNotebookUrl;
    }

    if (validatedData.estimatedCompletion) {
      updateData.estimatedCompletion = new Date(validatedData.estimatedCompletion);
    }

    const updatedQueueItem = await prisma.aiTryOnFallbackQueue.update({
      where: { 
        id: validatedData.queueId,
        assignedTo: session.user.id // Only allow updating own jobs
      },
      data: updateData,
      include: {
        aiTryOnJob: {
          include: {
            customization: {
              include: {
                product: {
                  select: {
                    name: true,
                    category: true
                  }
                }
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedQueueItem,
      message: 'Job updated successfully'
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating fallback job:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update job' },
      { status: 500 }
    );
  }
}
