import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const prisma = new PrismaClient();

// Validation schemas
const UpdateProductAiTryOnSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  aiTryOnEnabled: z.boolean(),
  aiTryOnPriority: z.number().min(1).max(5).optional(),
});

const BulkUpdateSchema = z.object({
  productIds: z.array(z.string()),
  aiTryOnEnabled: z.boolean(),
  aiTryOnPriority: z.number().min(1).max(5).optional(),
});

// GET /api/admin/ai-tryon/products - Get all products with AI try-on settings
export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';
    const aiTryOnEnabled = searchParams.get('aiTryOnEnabled');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    if (category) {
      where.category = category;
    }
    
    if (aiTryOnEnabled !== null) {
      where.aiTryOnEnabled = aiTryOnEnabled === 'true';
    }

    // Get products with AI try-on settings
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        skip,
        take: limit,
        select: {
          id: true,
          name: true,
          description: true,
          category: true,
          subcategory: true,
          heroImage: true,
          basePrice: true,
          isActive: true,
          aiTryOnEnabled: true,
          aiTryOnPriority: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              customizations: {
                where: {
                  aiTryOnJobs: {
                    some: {}
                  }
                }
              }
            }
          }
        },
        orderBy: [
          { aiTryOnPriority: 'asc' },
          { name: 'asc' }
        ]
      }),
      prisma.product.count({ where })
    ]);

    // Get AI try-on statistics for each product
    const productIds = products.map(p => p.id);
    const aiTryOnStats = await prisma.aiTryOnJob.groupBy({
      by: ['customization'],
      where: {
        customization: {
          productId: { in: productIds }
        }
      },
      _count: {
        id: true
      },
      _avg: {
        confidence: true
      }
    });

    // Combine data
    const productsWithStats = products.map(product => {
      const stats = aiTryOnStats.find(stat => 
        stat.customization === product.id
      );
      
      return {
        ...product,
        aiTryOnStats: {
          totalJobs: stats?._count.id || 0,
          averageConfidence: stats?._avg.confidence || 0,
          customizationsWithTryOn: product._count.customizations
        }
      };
    });

    return NextResponse.json({
      success: true,
      data: {
        products: productsWithStats,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Error fetching products AI try-on settings:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/ai-tryon/products - Update AI try-on settings for a product
export async function PUT(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = UpdateProductAiTryOnSchema.parse(body);

    // Update product AI try-on settings
    const updatedProduct = await prisma.product.update({
      where: { id: validatedData.productId },
      data: {
        aiTryOnEnabled: validatedData.aiTryOnEnabled,
        ...(validatedData.aiTryOnPriority && {
          aiTryOnPriority: validatedData.aiTryOnPriority
        })
      },
      select: {
        id: true,
        name: true,
        aiTryOnEnabled: true,
        aiTryOnPriority: true,
        updatedAt: true
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedProduct,
      message: `AI try-on settings updated for ${updatedProduct.name}`
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating product AI try-on settings:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update product settings' },
      { status: 500 }
    );
  }
}

// POST /api/admin/ai-tryon/products/bulk - Bulk update AI try-on settings
export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = BulkUpdateSchema.parse(body);

    // Bulk update products
    const updateData: any = {
      aiTryOnEnabled: validatedData.aiTryOnEnabled
    };
    
    if (validatedData.aiTryOnPriority) {
      updateData.aiTryOnPriority = validatedData.aiTryOnPriority;
    }

    const result = await prisma.product.updateMany({
      where: {
        id: { in: validatedData.productIds }
      },
      data: updateData
    });

    return NextResponse.json({
      success: true,
      data: {
        updatedCount: result.count,
        productIds: validatedData.productIds
      },
      message: `Updated AI try-on settings for ${result.count} products`
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error bulk updating product AI try-on settings:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to bulk update product settings' },
      { status: 500 }
    );
  }
}
