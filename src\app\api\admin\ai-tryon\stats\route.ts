import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const prisma = new PrismaClient();

// GET /api/admin/ai-tryon/stats - Get system statistics
export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    // Get current date for time-based queries
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Get basic product statistics
    const [totalProducts, aiTryOnEnabledProducts] = await Promise.all([
      prisma.product.count(),
      prisma.product.count({ where: { aiTryOnEnabled: true } })
    ]);

    // Get AI try-on job statistics
    const [totalJobs, successfulJobs, failedJobs, recentFailures] = await Promise.all([
      prisma.aiTryOnJob.count(),
      prisma.aiTryOnJob.count({ 
        where: { 
          status: { in: ['COMPLETED', 'FALLBACK_COMPLETED'] } 
        } 
      }),
      prisma.aiTryOnJob.count({ 
        where: { 
          status: { in: ['FAILED', 'QUEUED_FOR_FALLBACK'] } 
        } 
      }),
      prisma.aiTryOnJob.count({
        where: {
          status: { in: ['FAILED', 'QUEUED_FOR_FALLBACK'] },
          createdAt: { gte: last24Hours }
        }
      })
    ]);

    // Get fallback queue statistics
    const queueLength = await prisma.aiTryOnFallbackQueue.count({
      where: {
        status: { in: ['PENDING', 'CLAIMED', 'PROCESSING'] }
      }
    });

    // Calculate success rate
    const successRate = totalJobs > 0 ? successfulJobs / totalJobs : 0;

    // Get average processing time
    const processingTimeResult = await prisma.aiTryOnJob.aggregate({
      _avg: {
        processingTime: true
      },
      where: {
        processingTime: { not: null },
        status: { in: ['COMPLETED', 'FALLBACK_COMPLETED'] }
      }
    });

    const averageProcessingTime = processingTimeResult._avg.processingTime || 0;

    // Determine system health
    let systemHealth: 'healthy' | 'warning' | 'critical' = 'healthy';
    
    if (successRate < 0.7 || queueLength > 10 || recentFailures > 5 || averageProcessingTime > 120) {
      systemHealth = 'critical';
    } else if (successRate < 0.9 || queueLength > 5 || recentFailures > 2 || averageProcessingTime > 60) {
      systemHealth = 'warning';
    }

    // Get recent job trends (last 7 days)
    const recentJobs = await prisma.aiTryOnJob.findMany({
      where: {
        createdAt: { gte: last7Days }
      },
      select: {
        status: true,
        createdAt: true,
        processingTime: true,
        confidence: true
      }
    });

    // Calculate daily statistics
    const dailyStats = new Map();
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dateKey = date.toISOString().split('T')[0];
      dailyStats.set(dateKey, {
        total: 0,
        successful: 0,
        failed: 0,
        avgProcessingTime: 0,
        avgConfidence: 0
      });
    }

    recentJobs.forEach(job => {
      const dateKey = job.createdAt.toISOString().split('T')[0];
      const stats = dailyStats.get(dateKey);
      if (stats) {
        stats.total++;
        if (job.status === 'COMPLETED' || job.status === 'FALLBACK_COMPLETED') {
          stats.successful++;
        } else if (job.status === 'FAILED' || job.status === 'QUEUED_FOR_FALLBACK') {
          stats.failed++;
        }
      }
    });

    // Get top performing products
    const topProducts = await prisma.product.findMany({
      where: {
        aiTryOnEnabled: true,
        customizations: {
          some: {
            aiTryOnJobs: {
              some: {}
            }
          }
        }
      },
      select: {
        id: true,
        name: true,
        category: true,
        heroImage: true,
        _count: {
          select: {
            customizations: {
              where: {
                aiTryOnJobs: {
                  some: {}
                }
              }
            }
          }
        }
      },
      orderBy: {
        customizations: {
          _count: 'desc'
        }
      },
      take: 10
    });

    // Get recent failures for monitoring
    const recentFailureDetails = await prisma.aiTryOnJob.findMany({
      where: {
        status: { in: ['FAILED', 'QUEUED_FOR_FALLBACK'] },
        createdAt: { gte: last24Hours }
      },
      select: {
        id: true,
        errorMessage: true,
        retryCount: true,
        createdAt: true,
        customization: {
          select: {
            product: {
              select: {
                name: true,
                category: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });

    // Compile response data
    const stats = {
      totalProducts,
      aiTryOnEnabledProducts,
      totalJobs,
      successRate,
      queueLength,
      averageProcessingTime: Math.round(averageProcessingTime),
      recentFailures,
      systemHealth,
      lastUpdated: now.toISOString(),
      
      // Additional metrics
      metrics: {
        dailyTrends: Array.from(dailyStats.entries()).map(([date, stats]) => ({
          date,
          ...stats
        })),
        topProducts: topProducts.map(product => ({
          id: product.id,
          name: product.name,
          category: product.category,
          heroImage: product.heroImage,
          totalCustomizations: product._count.customizations
        })),
        recentFailures: recentFailureDetails.map(failure => ({
          id: failure.id,
          productName: failure.customization.product.name,
          category: failure.customization.product.category,
          errorMessage: failure.errorMessage || 'Unknown error',
          retryCount: failure.retryCount,
          createdAt: failure.createdAt.toISOString()
        }))
      }
    };

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Error fetching AI try-on stats:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch system statistics' },
      { status: 500 }
    );
  }
}
