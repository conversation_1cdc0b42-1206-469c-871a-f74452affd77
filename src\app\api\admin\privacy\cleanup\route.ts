import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { PrivacyCleanupService } from '@/lib/services/privacyCleanup';
import { z } from 'zod';

// Validation schema
const CleanupRequestSchema = z.object({
  type: z.enum(['automatic', 'manual', 'user_specific']),
  userId: z.string().optional(),
  force: z.boolean().default(false),
});

/**
 * POST /api/admin/privacy/cleanup - Trigger privacy cleanup
 */
export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = CleanupRequestSchema.parse(body);

    const cleanupService = PrivacyCleanupService.getInstance();
    let result;

    switch (validatedData.type) {
      case 'automatic':
      case 'manual':
        result = await cleanupService.runCleanup();
        break;

      case 'user_specific':
        if (!validatedData.userId) {
          return NextResponse.json(
            { success: false, error: 'User ID is required for user-specific cleanup' },
            { status: 400 }
          );
        }
        result = await cleanupService.cleanupUserData(validatedData.userId, {
          includeDesigns: validatedData.force,
          includeOrders: false, // Never delete orders
        });
        break;

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid cleanup type' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      data: result,
      message: `Cleanup completed: ${result.deletedJobs} jobs and ${result.deletedFiles} files deleted`,
    });

  } catch (error) {
    console.error('Error running privacy cleanup:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/privacy/cleanup - Get cleanup statistics
 */
export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Get cleanup statistics
    const [
      totalJobs,
      activeJobs,
      deletedJobs,
      expiredJobs,
      recentDeletions,
      usersWithConsent,
      totalUsers,
    ] = await Promise.all([
      prisma.aiTryOnJob.count(),
      prisma.aiTryOnJob.count({ where: { isDeleted: false } }),
      prisma.aiTryOnJob.count({ where: { isDeleted: true } }),
      prisma.aiTryOnJob.count({
        where: {
          isDeleted: false,
          OR: [
            { scheduledDeletion: { lte: now } },
            {
              AND: [
                { scheduledDeletion: null },
                { createdAt: { lte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) } },
              ],
            },
          ],
        },
      }),
      prisma.aiTryOnJob.count({
        where: {
          isDeleted: true,
          deletedAt: { gte: last24Hours },
        },
      }),
      prisma.user.count({ where: { aiTryOnConsent: true } }),
      prisma.user.count(),
    ]);

    // Get retention policy distribution
    const retentionDistribution = await prisma.user.groupBy({
      by: ['dataRetentionDays'],
      _count: { id: true },
      where: { aiTryOnConsent: true },
    });

    // Get recent privacy requests
    const recentPrivacyRequests = await prisma.privacyRequest.findMany({
      where: { createdAt: { gte: last7Days } },
      orderBy: { createdAt: 'desc' },
      take: 10,
      select: {
        id: true,
        type: true,
        status: true,
        createdAt: true,
        user: {
          select: {
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        overview: {
          totalJobs,
          activeJobs,
          deletedJobs,
          expiredJobs,
          deletionRate: totalJobs > 0 ? (deletedJobs / totalJobs) * 100 : 0,
        },
        recent: {
          deletionsLast24h: recentDeletions,
          privacyRequestsLast7d: recentPrivacyRequests.length,
        },
        users: {
          totalUsers,
          usersWithConsent,
          consentRate: totalUsers > 0 ? (usersWithConsent / totalUsers) * 100 : 0,
        },
        retentionPolicies: retentionDistribution.map(item => ({
          days: item.dataRetentionDays,
          userCount: item._count.id,
        })),
        recentRequests: recentPrivacyRequests,
      },
    });

  } catch (error) {
    console.error('Error fetching cleanup statistics:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
