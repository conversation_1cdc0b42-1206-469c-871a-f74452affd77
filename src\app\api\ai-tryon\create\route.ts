import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { huggingFaceService } from '@/lib/services/huggingface';
import { parseBase64Image, parseUploadedImage, validatePortraitImage } from '@/lib/utils/imageUpload';
import { AiTryOnRequest, AiTryOnResponse, HuggingFaceOOTDRequest } from '@/types';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const prisma = new PrismaClient();

// Validation schema for AI try-on requests
const AiTryOnRequestSchema = z.object({
  userPhotoUrl: z.string().min(1, 'User photo is required'),
  customizationId: z.string().min(1, 'Customization ID is required'),
  userId: z.string().min(1, 'User ID is required'),
  contextScene: z.enum(['outdoor', 'social', 'active', 'studio']).optional(),
});

// Alternative schema for FormData uploads
const AiTryOnFormDataSchema = z.object({
  customizationId: z.string().min(1, 'Customization ID is required'),
  userId: z.string().min(1, 'User ID is required'),
  contextScene: z.enum(['outdoor', 'social', 'active', 'studio']).optional(),
});

/**
 * POST /api/ai-tryon/create - Create AI try-on job
 * 
 * Accepts either:
 * 1. JSON body with base64 userPhotoUrl
 * 2. FormData with uploaded image file
 */
export async function POST(request: NextRequest) {
  try {
    const contentType = request.headers.get('content-type') || '';
    let requestData: AiTryOnRequest;
    let userPhotoBase64: string;

    // Handle different content types
    if (contentType.includes('multipart/form-data')) {
      // Handle FormData upload
      const formData = await request.formData();
      const customizationId = formData.get('customizationId') as string;
      const userId = formData.get('userId') as string;
      const contextScene = formData.get('contextScene') as string;

      const formValidation = AiTryOnFormDataSchema.safeParse({
        customizationId,
        userId,
        contextScene: contextScene || undefined,
      });

      if (!formValidation.success) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid form data',
            details: formValidation.error.issues,
          },
          { status: 400 }
        );
      }

      // Parse uploaded image
      const imageResult = await parseUploadedImage(request, 'userPhoto');
      if (!imageResult.success) {
        return NextResponse.json(
          {
            success: false,
            error: imageResult.error,
          },
          { status: 400 }
        );
      }

      userPhotoBase64 = imageResult.data!.base64DataUrl;
      requestData = {
        userPhotoUrl: userPhotoBase64,
        customizationId: formValidation.data.customizationId,
        userId: formValidation.data.userId,
        contextScene: formValidation.data.contextScene,
      };

    } else {
      // Handle JSON body
      const body = await request.json();
      const validation = AiTryOnRequestSchema.safeParse(body);

      if (!validation.success) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request data',
            details: validation.error.issues,
          },
          { status: 400 }
        );
      }

      requestData = validation.data;

      // Validate and process base64 image
      const imageResult = await parseBase64Image(requestData.userPhotoUrl, 'portrait');
      if (!imageResult.success) {
        return NextResponse.json(
          {
            success: false,
            error: imageResult.error,
          },
          { status: 400 }
        );
      }

      userPhotoBase64 = imageResult.data!.base64DataUrl;
    }

    // Check user authentication and consent
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.id !== requestData.userId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required or user mismatch',
        },
        { status: 401 }
      );
    }

    // Verify user exists and get privacy settings
    const user = await prisma.user.findUnique({
      where: { id: requestData.userId },
      select: {
        id: true,
        aiTryOnConsent: true,
        aiTryOnConsentDate: true,
        dailyTryOnCount: true,
        lastTryOnDate: true,
        dataRetentionDays: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'User not found',
        },
        { status: 404 }
      );
    }

    // Check AI try-on consent
    if (!user.aiTryOnConsent) {
      return NextResponse.json(
        {
          success: false,
          error: 'AI try-on consent required. Please accept the privacy terms to continue.',
          code: 'CONSENT_REQUIRED',
        },
        { status: 403 }
      );
    }

    // Check daily limits (2 try-ons per day)
    const today = new Date();
    const lastTryOn = user.lastTryOnDate;
    const needsReset = !lastTryOn || lastTryOn.toDateString() !== today.toDateString();
    const currentCount = needsReset ? 0 : user.dailyTryOnCount;

    if (currentCount >= 2) {
      return NextResponse.json(
        {
          success: false,
          error: 'Daily try-on limit reached. You can try on 2 designs per day.',
          code: 'LIMIT_EXCEEDED',
          data: {
            dailyCount: currentCount,
            dailyLimit: 2,
            resetTime: new Date(today.getTime() + 24 * 60 * 60 * 1000),
          },
        },
        { status: 429 }
      );
    }

    // Verify customization exists and get product info
    const customization = await prisma.customization.findUnique({
      where: { id: requestData.customizationId },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            category: true,
            subcategory: true,
          },
        },
      },
    });

    if (!customization) {
      return NextResponse.json(
        {
          success: false,
          error: 'Customization not found',
        },
        { status: 404 }
      );
    }

    // Check if user owns the customization or if it's public
    if (customization.userId !== requestData.userId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Access denied to this customization',
        },
        { status: 403 }
      );
    }

    // Check if AI try-on is enabled for this product (Admin Control)
    if (!customization.product.aiTryOnEnabled) {
      return NextResponse.json(
        {
          success: false,
          error: 'AI try-on is currently disabled for this product',
        },
        { status: 403 }
      );
    }

    // Update user's daily try-on count
    await prisma.user.update({
      where: { id: requestData.userId },
      data: {
        dailyTryOnCount: currentCount + 1,
        lastTryOnDate: today,
        totalTryOnCount: { increment: 1 },
      },
    });

    // Calculate scheduled deletion date based on user's retention preference
    const retentionDays = user.dataRetentionDays || 30;
    const scheduledDeletion = new Date(today.getTime() + retentionDays * 24 * 60 * 60 * 1000);

    // Create hash of user photo for duplicate detection (privacy-friendly)
    const crypto = require('crypto');
    const userPhotoHash = crypto.createHash('sha256').update(userPhotoBase64).digest('hex');

    // Create AI try-on job record with privacy tracking
    const aiTryOnJob = await prisma.aiTryOnJob.create({
      data: {
        status: 'PENDING',
        userPhotoUrl: userPhotoBase64,
        customizationId: requestData.customizationId,
        userId: requestData.userId,
        userPhotoHash,
        scheduledDeletion,
        consentVersion: '1.0', // Track consent version for audit purposes
      },
    });

    // Prepare garment image from customization
    let garmentImageBase64: string;
    if (customization.previewImage) {
      garmentImageBase64 = customization.previewImage;
    } else {
      // Generate preview image from design data if not available
      // This would typically involve rendering the design
      // For now, we'll return an error
      await prisma.aiTryOnJob.update({
        where: { id: aiTryOnJob.id },
        data: {
          status: 'FAILED',
          errorMessage: 'No preview image available for customization',
        },
      });

      return NextResponse.json(
        {
          success: false,
          error: 'Customization preview image not available',
        },
        { status: 400 }
      );
    }

    // Determine garment category based on product
    let category: 'upper_body' | 'lower_body' | 'dresses';
    const productCategory = customization.product.category.toLowerCase();
    
    if (productCategory.includes('dress')) {
      category = 'dresses';
    } else if (productCategory.includes('pant') || productCategory.includes('short') || productCategory.includes('skirt')) {
      category = 'lower_body';
    } else {
      category = 'upper_body'; // Default for shirts, hoodies, etc.
    }

    // Update job status to processing
    await prisma.aiTryOnJob.update({
      where: { id: aiTryOnJob.id },
      data: {
        status: 'PROCESSING',
      },
    });

    // Prepare Hugging Face request
    const hfRequest: HuggingFaceOOTDRequest = {
      person_image: userPhotoBase64,
      garment_image: garmentImageBase64,
      category,
      num_inference_steps: 20,
      guidance_scale: 2.0,
      seed: -1,
    };

    // Generate try-on image with Hugging Face
    const hfResponse = await huggingFaceService.generateTryOnWithRetry(hfRequest);

    if (hfResponse.success && hfResponse.result_image) {
      // Update job with successful result
      const updatedJob = await prisma.aiTryOnJob.update({
        where: { id: aiTryOnJob.id },
        data: {
          status: 'COMPLETED',
          resultImageUrl: hfResponse.result_image,
          processingTime: hfResponse.processing_time,
          confidence: 0.85, // Default confidence score
        },
        include: {
          customization: {
            select: {
              id: true,
              name: true,
              product: {
                select: {
                  name: true,
                  category: true,
                },
              },
            },
          },
        },
      });

      const response: AiTryOnResponse = {
        success: true,
        data: {
          jobId: updatedJob.id,
          status: 'COMPLETED',
          resultImageUrl: updatedJob.resultImageUrl!,
          confidence: updatedJob.confidence!,
        },
        message: 'AI try-on completed successfully',
      };

      return NextResponse.json(response);

    } else {
      // Check if we should add to fallback queue
      const currentJob = await prisma.aiTryOnJob.findUnique({
        where: { id: aiTryOnJob.id },
        select: { retryCount: true }
      });

      const retryCount = (currentJob?.retryCount || 0) + 1;
      const maxRetries = 3; // Configuration value

      if (retryCount >= maxRetries) {
        // Add to fallback queue for manual processing
        await prisma.$transaction(async (tx) => {
          // Update job status to indicate fallback
          await tx.aiTryOnJob.update({
            where: { id: aiTryOnJob.id },
            data: {
              status: 'QUEUED_FOR_FALLBACK',
              errorMessage: hfResponse.error || 'Unknown error during AI processing',
              processingTime: hfResponse.processing_time,
              retryCount: retryCount,
            },
          });

          // Create fallback queue entry
          await tx.aiTryOnFallbackQueue.create({
            data: {
              aiTryOnJobId: aiTryOnJob.id,
              status: 'PENDING',
              priority: customization.product.aiTryOnPriority || 3,
              originalError: hfResponse.error || 'AI processing failed after maximum retries',
              retryCount: retryCount,
            },
          });
        });

        return NextResponse.json(
          {
            success: false,
            error: 'AI try-on failed and has been queued for manual processing',
            details: 'You will be notified when manual processing is complete',
          },
          { status: 202 } // Accepted for processing
        );
      } else {
        // Update job with failure but allow retry
        await prisma.aiTryOnJob.update({
          where: { id: aiTryOnJob.id },
          data: {
            status: 'FAILED',
            errorMessage: hfResponse.error || 'Unknown error during AI processing',
            processingTime: hfResponse.processing_time,
            retryCount: retryCount,
          },
        });

        return NextResponse.json(
          {
            success: false,
            error: 'AI try-on generation failed',
            details: hfResponse.error,
            retryCount: retryCount,
            maxRetries: maxRetries,
          },
          { status: 500 }
        );
      }
    }

  } catch (error) {
    console.error('Error in AI try-on creation:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/ai-tryon/create?jobId=xxx - Get AI try-on job status
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Job ID is required',
        },
        { status: 400 }
      );
    }

    // Fetch job status
    const job = await prisma.aiTryOnJob.findUnique({
      where: { id: jobId },
      include: {
        customization: {
          select: {
            id: true,
            name: true,
            product: {
              select: {
                name: true,
                category: true,
              },
            },
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!job) {
      return NextResponse.json(
        {
          success: false,
          error: 'Job not found',
        },
        { status: 404 }
      );
    }

    const response: AiTryOnResponse = {
      success: true,
      data: {
        jobId: job.id,
        status: job.status,
        resultImageUrl: job.resultImageUrl || undefined,
        confidence: job.confidence || undefined,
      },
    };

    if (job.status === 'FAILED') {
      response.error = job.errorMessage || 'AI try-on failed';
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching AI try-on job status:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
