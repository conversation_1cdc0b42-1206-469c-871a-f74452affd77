import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { generatePreviewImage, generateImageVariants } from '@/lib/utils/imageProcessing';
import { calculateCanvasPrintArea, PRINT_SIZE_CATEGORIES } from '@/lib/utils/areaCalculation';
import { DesignCanvas } from '@/types';

// Request validation schema
const PreviewRequestSchema = z.object({
  canvas: z.object({
    width: z.number().min(100).max(2000),
    height: z.number().min(100).max(2000),
    backgroundColor: z.string(),
    elements: z.array(z.object({
      id: z.string(),
      type: z.enum(['text', 'image', 'shape']),
      x: z.number(),
      y: z.number(),
      width: z.number().min(1),
      height: z.number().min(1),
      rotation: z.number().optional().default(0),
      opacity: z.number().min(0).max(1).optional().default(1),
      visible: z.boolean().optional().default(true),
      locked: z.boolean().optional().default(false),
      data: z.any().optional(),
    })),
    layers: z.array(z.string()).optional().default([]),
  }),
  options: z.object({
    format: z.enum(['png', 'jpeg', 'webp']).optional().default('png'),
    quality: z.number().min(1).max(100).optional().default(85),
    includeVariants: z.boolean().optional().default(false),
    includePricing: z.boolean().optional().default(true),
  }).optional().default({}),
});

export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json();
    const validationResult = PreviewRequestSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: validationResult.error.issues.map((err: any) => ({
            field: err.path.join('.'),
            message: err.message
          }))
        },
        { status: 400 }
      );
    }

    const { canvas, options } = validationResult.data;

    // Calculate print area
    const areaCalculation = calculateCanvasPrintArea(canvas);
    
    // Check if design has elements
    if (canvas.elements.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Canvas must contain at least one design element',
          message: 'Add some text, images, or shapes to create your design'
        },
        { status: 400 }
      );
    }

    // Generate preview image(s)
    let imageData: any = {};
    
    if (options.includeVariants) {
      // Generate multiple variants
      const variants = await generateImageVariants(canvas);
      
      imageData = {
        preview: {
          buffer: variants.preview.toString('base64'),
          metadata: variants.metadata.preview,
        },
        thumbnail: {
          buffer: variants.thumbnail.toString('base64'),
          metadata: variants.metadata.thumbnail,
        },
        optimized: {
          buffer: variants.optimized.toString('base64'),
          metadata: variants.metadata.optimized,
        },
      };
    } else {
      // Generate single preview
      const preview = await generatePreviewImage(canvas, {
        format: options.format,
        quality: options.quality,
      });
      
      imageData = {
        preview: {
          buffer: preview.buffer.toString('base64'),
          metadata: preview.metadata,
        },
      };
    }

    // Prepare response data
    const responseData: any = {
      canvas: {
        width: canvas.width,
        height: canvas.height,
        elementCount: canvas.elements.length,
      },
      area: {
        totalArea: areaCalculation.totalArea,
        printSizeCategory: areaCalculation.printSizeCategory,
        categoryInfo: PRINT_SIZE_CATEGORIES[areaCalculation.printSizeCategory],
        isWithinLimits: areaCalculation.isWithinLimits,
        elementAreas: areaCalculation.elementAreas,
        overlappingArea: areaCalculation.overlappingArea,
        recommendations: areaCalculation.recommendations,
      },
      images: imageData,
      timestamp: new Date().toISOString(),
    };

    // Add pricing information if requested
    if (options.includePricing) {
      // This would integrate with your pricing engine
      // For now, we'll include basic pricing info
      responseData.pricing = {
        printSizeCategory: areaCalculation.printSizeCategory,
        categoryLabel: PRINT_SIZE_CATEGORIES[areaCalculation.printSizeCategory].label,
        categoryDescription: PRINT_SIZE_CATEGORIES[areaCalculation.printSizeCategory].description,
        impactMessage: getAreaImpactMessage(areaCalculation.totalArea),
      };
    }

    return NextResponse.json({
      success: true,
      data: responseData
    });

  } catch (error) {
    console.error('Preview generation error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to generate preview',
        message: 'Unable to process your design at this time. Please try again.'
      },
      { status: 500 }
    );
  }
}

// GET endpoint for preview configuration
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const configType = searchParams.get('config');

    switch (configType) {
      case 'print-sizes':
        return NextResponse.json({
          success: true,
          data: {
            categories: PRINT_SIZE_CATEGORIES,
            limits: {
              minArea: 1,
              maxArea: 500,
            }
          }
        });

      case 'formats':
        return NextResponse.json({
          success: true,
          data: {
            supportedFormats: ['png', 'jpeg', 'webp'],
            defaultFormat: 'png',
            qualityRange: { min: 1, max: 100 },
            defaultQuality: 85,
          }
        });

      default:
        return NextResponse.json({
          success: true,
          data: {
            printSizes: PRINT_SIZE_CATEGORIES,
            supportedFormats: ['png', 'jpeg', 'webp'],
            limits: {
              minArea: 1,
              maxArea: 500,
              maxElements: 50,
              maxCanvasSize: { width: 2000, height: 2000 },
            }
          }
        });
    }

  } catch (error) {
    console.error('Preview config error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Unable to fetch preview configuration'
      },
      { status: 500 }
    );
  }
}

/**
 * Generate emotional impact message based on print area
 */
function getAreaImpactMessage(area: number): string {
  if (area <= 25) {
    return "✨ Perfect for subtle, personal touches that speak volumes";
  } else if (area <= 100) {
    return "🎯 Great size for making a statement while staying stylish";
  } else if (area <= 250) {
    return "🚀 Bold and eye-catching - your style will turn heads";
  } else {
    return "💥 Maximum impact design - own every room you enter";
  }
}
