import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';
import { generatePreviewImage } from '@/lib/utils/imageProcessing';
import { calculateCanvasPrintArea } from '@/lib/utils/areaCalculation';
import { PricingEngine } from '@/lib/pricing/engine';
import { DesignCanvas } from '@/types';

const prisma = new PrismaClient();

// Request validation schema
const SaveCustomizationSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  canvas: z.object({
    width: z.number().min(100).max(2000),
    height: z.number().min(100).max(2000),
    backgroundColor: z.string(),
    elements: z.array(z.object({
      id: z.string(),
      type: z.enum(['text', 'image', 'shape']),
      x: z.number(),
      y: z.number(),
      width: z.number().min(1),
      height: z.number().min(1),
      rotation: z.number().optional().default(0),
      opacity: z.number().min(0).max(1).optional().default(1),
      visible: z.boolean().optional().default(true),
      locked: z.boolean().optional().default(false),
      data: z.any().optional(),
    })),
    layers: z.array(z.string()).optional().default([]),
  }),
  productId: z.string().min(1),
  templateId: z.string().optional(),
  userId: z.string().min(1),
  placement: z.enum(['front', 'back', 'left', 'right']).optional().default('front'),
  status: z.enum(['DRAFT', 'PUBLISHED']).optional().default('DRAFT'),
  generatePreview: z.boolean().optional().default(true),
});

export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json();
    const validationResult = SaveCustomizationSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: validationResult.error.issues.map((err: any) => ({
            field: err.path.join('.'),
            message: err.message
          }))
        },
        { status: 400 }
      );
    }

    const {
      name,
      description,
      canvas,
      productId,
      templateId,
      userId,
      placement,
      status,
      generatePreview
    } = validationResult.data;

    // Validate that user and product exist
    const [user, product] = await Promise.all([
      prisma.user.findUnique({ where: { id: userId } }),
      prisma.product.findUnique({ where: { id: productId } })
    ]);

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'User not found'
        },
        { status: 404 }
      );
    }

    if (!product) {
      return NextResponse.json(
        {
          success: false,
          error: 'Product not found'
        },
        { status: 404 }
      );
    }

    // Validate template if provided
    if (templateId) {
      const template = await prisma.template.findUnique({
        where: { id: templateId }
      });
      
      if (!template) {
        return NextResponse.json(
          {
            success: false,
            error: 'Template not found'
          },
          { status: 404 }
        );
      }
    }

    // Check if design has elements
    if (canvas.elements.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Canvas must contain at least one design element',
          message: 'Add some text, images, or shapes to create your design'
        },
        { status: 400 }
      );
    }

    // Calculate print area and pricing
    const areaCalculation = calculateCanvasPrintArea(canvas);
    
    if (!areaCalculation.isWithinLimits) {
      return NextResponse.json(
        {
          success: false,
          error: 'Design area is outside printable limits',
          message: areaCalculation.totalArea < 1 
            ? 'Design is too small to print clearly'
            : 'Design is too large for our printing capabilities',
          area: areaCalculation.totalArea,
          limits: { min: 1, max: 500 }
        },
        { status: 400 }
      );
    }

    // Generate preview image if requested
    let previewImage: string | null = null;
    if (generatePreview) {
      try {
        const preview = await generatePreviewImage(canvas, {
          format: 'png',
          quality: 85,
        });
        
        // In production, you'd upload this to your file storage (MinIO, S3, etc.)
        // For now, we'll store it as base64 (not recommended for production)
        previewImage = `data:image/png;base64,${preview.buffer.toString('base64')}`;
      } catch (error) {
        console.error('Preview generation failed:', error);
        // Continue without preview - don't fail the save operation
      }
    }

    // Calculate pricing using the pricing engine
    let pricingData = null;
    try {
      const pricingEngine = PricingEngine.getInstance();
      const pricingResult = await pricingEngine.calculatePrice({
        productId,
        quantity: 1,
        printSize: areaCalculation.printSizeCategory,
        qualityTier: 'standard',
        userId,
      });

      if (pricingResult.success && pricingResult.data) {
        pricingData = {
          totalPrice: pricingResult.data.totalPrice,
          breakdown: pricingResult.data.breakdown,
          printSizeCategory: areaCalculation.printSizeCategory,
          printArea: areaCalculation.totalArea,
        };
      }
    } catch (error) {
      console.error('Pricing calculation failed:', error);
      // Continue without pricing - can be calculated later
    }

    // Save customization to database
    const customization = await prisma.customization.create({
      data: {
        name,
        description,
        designData: {
          canvas,
          placement,
          areaCalculation,
          pricingData,
          metadata: {
            createdAt: new Date().toISOString(),
            version: '1.0',
          }
        },
        previewImage,
        userId,
        productId,
        templateId,
        status,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        },
        product: {
          select: {
            id: true,
            name: true,
            category: true,
            basePrice: true,
          }
        },
        template: templateId ? {
          select: {
            id: true,
            name: true,
            moodTag: true,
          }
        } : false,
      }
    });

    // Update template usage count if template was used
    if (templateId) {
      await prisma.template.update({
        where: { id: templateId },
        data: {
          usageCount: {
            increment: 1
          }
        }
      });
    }

    // Prepare response
    const responseData = {
      customization: {
        id: customization.id,
        name: customization.name,
        description: customization.description,
        status: customization.status,
        previewImage: customization.previewImage,
        createdAt: customization.createdAt,
        updatedAt: customization.updatedAt,
      },
      canvas: {
        width: canvas.width,
        height: canvas.height,
        elementCount: canvas.elements.length,
        placement,
      },
      area: {
        totalArea: areaCalculation.totalArea,
        printSizeCategory: areaCalculation.printSizeCategory,
        isWithinLimits: areaCalculation.isWithinLimits,
        elementAreas: areaCalculation.elementAreas,
      },
      pricing: pricingData,
      user: customization.user,
      product: customization.product,
      template: customization.template,
    };

    return NextResponse.json({
      success: true,
      data: responseData,
      message: 'Customization saved successfully! Your unique design is ready.'
    });

  } catch (error) {
    console.error('Save customization error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to save customization',
        message: 'Unable to save your design at this time. Please try again.'
      },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve saved customizations
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const productId = searchParams.get('productId');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: 'User ID is required'
        },
        { status: 400 }
      );
    }

    const where: any = { userId };
    if (productId) where.productId = productId;
    if (status) where.status = status;

    const [customizations, total] = await Promise.all([
      prisma.customization.findMany({
        where,
        include: {
          product: {
            select: {
              id: true,
              name: true,
              category: true,
              heroImage: true,
            }
          },
          template: {
            select: {
              id: true,
              name: true,
              moodTag: true,
            }
          }
        },
        orderBy: { updatedAt: 'desc' },
        take: limit,
        skip: offset,
      }),
      prisma.customization.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      data: {
        customizations,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total,
        }
      }
    });

  } catch (error) {
    console.error('Fetch customizations error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Unable to fetch customizations'
      },
      { status: 500 }
    );
  }
}
