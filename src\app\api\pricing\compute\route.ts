import { NextRequest, NextResponse } from 'next/server';
import { PricingEngine } from '@/lib/pricing/engine';
import { PricingRequest } from '@/types/pricing';
import { z } from 'zod';

// Request validation schema
const PricingRequestSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  variantId: z.string().optional(),
  customizationId: z.string().optional(),
  quantity: z.number().int().min(1, 'Quantity must be at least 1').max(100, 'Quantity cannot exceed 100'),
  fabricId: z.string().optional(),
  printSize: z.enum(['small', 'medium', 'large', 'full_coverage']).optional(),
  qualityTier: z.enum(['standard', 'premium', 'luxury']).optional(),
  userId: z.string().optional()
});

export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json();
    const validationResult = PricingRequestSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: validationResult.error.issues.map((err: any) => ({
            field: err.path.join('.'),
            message: err.message
          }))
        },
        { status: 400 }
      );
    }

    const pricingRequest: PricingRequest = validationResult.data;

    // Rate limiting check (in production, use Redis or similar)
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
    const rateLimitKey = `pricing_${clientIP}`;
    
    // For now, we'll skip rate limiting implementation
    // In production: implement proper rate limiting here

    // Calculate pricing using the engine
    const pricingEngine = PricingEngine.getInstance();
    const result = await pricingEngine.calculatePrice(pricingRequest);

    // Log pricing calculation for analytics (in production, use proper logging)
    console.log('Pricing calculation:', {
      productId: pricingRequest.productId,
      quantity: pricingRequest.quantity,
      totalPrice: result.data?.totalPrice,
      timestamp: new Date().toISOString(),
      clientIP
    });

    // Return response with appropriate status code
    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: 400 });
    }

  } catch (error) {
    console.error('Pricing API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Unable to calculate pricing at this time. Please try again later.'
      },
      { status: 500 }
    );
  }
}

// GET endpoint for pricing configuration (for admin use)
export async function GET(request: NextRequest) {
  try {
    // In production, add proper authentication/authorization here
    const { searchParams } = new URL(request.url);
    const configType = searchParams.get('config');

    const pricingEngine = PricingEngine.getInstance();

    switch (configType) {
      case 'print-sizes':
        return NextResponse.json({
          success: true,
          data: pricingEngine.getPrintSizeConfig()
        });

      case 'quality-tiers':
        return NextResponse.json({
          success: true,
          data: pricingEngine.getQualityTierConfig()
        });

      default:
        return NextResponse.json({
          success: true,
          data: {
            printSizes: pricingEngine.getPrintSizeConfig(),
            qualityTiers: pricingEngine.getQualityTierConfig()
          }
        });
    }

  } catch (error) {
    console.error('Pricing config API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Unable to fetch pricing configuration'
      },
      { status: 500 }
    );
  }
}
