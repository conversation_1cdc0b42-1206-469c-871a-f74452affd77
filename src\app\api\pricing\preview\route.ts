import { NextRequest, NextResponse } from 'next/server';
import { PricingEngine } from '@/lib/pricing/engine';
import { PricingRequest } from '@/types/pricing';
import { z } from 'zod';

// Preview request schema for testing multiple scenarios
const PreviewRequestSchema = z.object({
  scenarios: z.array(z.object({
    name: z.string(),
    description: z.string().optional(),
    request: z.object({
      productId: z.string(),
      variantId: z.string().optional(),
      customizationId: z.string().optional(),
      quantity: z.number().int().min(1).max(100),
      fabricId: z.string().optional(),
      printSize: z.enum(['small', 'medium', 'large', 'full_coverage']).optional(),
      qualityTier: z.enum(['standard', 'premium', 'luxury']).optional(),
      userId: z.string().optional()
    })
  })).min(1, 'At least one scenario is required').max(10, 'Maximum 10 scenarios allowed')
});

export async function POST(request: NextRequest) {
  try {
    // This endpoint is for admin/testing purposes
    // In production, add proper authentication here
    
    const body = await request.json();
    const validationResult = PreviewRequestSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid preview request',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const { scenarios } = validationResult.data;
    const pricingEngine = PricingEngine.getInstance();
    
    // Calculate pricing for each scenario
    const results = [];
    for (const scenario of scenarios) {
      const startTime = Date.now();
      const pricingResult = await pricingEngine.calculatePrice(scenario.request);
      const calculationTime = Date.now() - startTime;
      
      results.push({
        scenario: {
          name: scenario.name,
          description: scenario.description
        },
        request: scenario.request,
        result: pricingResult,
        metadata: {
          calculationTime,
          timestamp: new Date().toISOString()
        }
      });
    }

    // Calculate summary statistics
    const successfulResults = results.filter(r => r.result.success);
    const totalPrices = successfulResults.map(r => r.result.data?.totalPrice || 0);
    const avgPrice = totalPrices.length > 0 ? totalPrices.reduce((sum, price) => sum + price, 0) / totalPrices.length : 0;
    const minPrice = totalPrices.length > 0 ? Math.min(...totalPrices) : 0;
    const maxPrice = totalPrices.length > 0 ? Math.max(...totalPrices) : 0;

    return NextResponse.json({
      success: true,
      data: {
        results,
        summary: {
          totalScenarios: scenarios.length,
          successfulCalculations: successfulResults.length,
          failedCalculations: scenarios.length - successfulResults.length,
          priceRange: {
            min: minPrice,
            max: maxPrice,
            average: avgPrice
          }
        }
      }
    });

  } catch (error) {
    console.error('Pricing preview API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Unable to generate pricing preview'
      },
      { status: 500 }
    );
  }
}
