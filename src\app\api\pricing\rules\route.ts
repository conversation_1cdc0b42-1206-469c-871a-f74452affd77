import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { PricingValidator } from '@/lib/pricing/utils';
import { z } from 'zod';

const prisma = new PrismaClient();

// Pricing rule schema for validation
const PricingRuleSchema = z.object({
  name: z.string().min(1, 'Rule name is required'),
  description: z.string().min(1, 'Description is required'),
  type: z.enum([
    'fabric_surcharge',
    'print_size_cost', 
    'quality_tier',
    'quantity_discount',
    'seasonal_promotion',
    'loyalty_discount',
    'first_time_discount',
    'complexity_surcharge'
  ]),
  conditions: z.array(z.object({
    field: z.string(),
    operator: z.enum(['equals', 'greater_than', 'less_than', 'in', 'contains']),
    value: z.any()
  })).min(1, 'At least one condition is required'),
  modifier: z.number(),
  modifierType: z.enum(['fixed', 'percentage']),
  customerMessage: z.string().min(1, 'Customer message is required'),
  valueFraming: z.string().min(1, 'Value framing is required'),
  priority: z.number().int().min(0).max(100),
  isActive: z.boolean().default(true),
  validFrom: z.string().datetime().optional(),
  validUntil: z.string().datetime().optional()
});

// GET - Fetch all pricing rules
export async function GET(request: NextRequest) {
  try {
    // In production, add proper authentication/authorization
    
    const { searchParams } = new URL(request.url);
    const isActive = searchParams.get('active');
    const ruleType = searchParams.get('type');
    
    const where: any = {};
    if (isActive !== null) {
      where.isActive = isActive === 'true';
    }
    if (ruleType) {
      where.type = ruleType;
    }

    const rules = await prisma.priceRule.findMany({
      where,
      orderBy: [
        { priority: 'asc' },
        { createdAt: 'desc' }
      ]
    });

    return NextResponse.json({
      success: true,
      data: rules
    });

  } catch (error) {
    console.error('Fetch pricing rules error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Unable to fetch pricing rules'
      },
      { status: 500 }
    );
  }
}

// POST - Create new pricing rule
export async function POST(request: NextRequest) {
  try {
    // In production, add proper authentication/authorization
    
    const body = await request.json();
    const validationResult = PricingRuleSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid rule data',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const ruleData = validationResult.data;
    
    // Convert date strings to Date objects for validation
    const ruleForValidation = {
      ...ruleData,
      validFrom: ruleData.validFrom ? new Date(ruleData.validFrom) : undefined,
      validUntil: ruleData.validUntil ? new Date(ruleData.validUntil) : undefined,
    };

    // Additional business logic validation
    const businessValidation = PricingValidator.validateRule(ruleForValidation);
    if (!businessValidation.isValid) {
      return NextResponse.json(
        {
          success: false,
          error: 'Rule validation failed',
          details: businessValidation.errors
        },
        { status: 400 }
      );
    }

    // Check for conflicting rules
    const existingRules = await prisma.priceRule.findMany({
      where: {
        name: ruleData.name,
        isActive: true
      }
    });

    if (existingRules.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'A rule with this name already exists'
        },
        { status: 409 }
      );
    }

    // Create the rule
    const newRule = await prisma.priceRule.create({
      data: {
        name: ruleData.name,
        description: ruleData.description,
        conditions: ruleData.conditions,
        modifier: ruleData.modifier,
        modifierType: ruleData.modifierType,
        validFrom: ruleData.validFrom ? new Date(ruleData.validFrom) : null,
        validUntil: ruleData.validUntil ? new Date(ruleData.validUntil) : null,
        isActive: ruleData.isActive,
        priority: ruleData.priority
      }
    });

    return NextResponse.json({
      success: true,
      data: newRule
    }, { status: 201 });

  } catch (error) {
    console.error('Create pricing rule error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Unable to create pricing rule'
      },
      { status: 500 }
    );
  }
}

// PUT - Update existing pricing rule
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, ...updateData } = body;
    
    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Rule ID is required'
        },
        { status: 400 }
      );
    }

    const validationResult = PricingRuleSchema.partial().safeParse(updateData);
    
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid update data',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const updatedRule = await prisma.priceRule.update({
      where: { id },
      data: {
        ...validationResult.data,
        validFrom: validationResult.data.validFrom ? new Date(validationResult.data.validFrom) : undefined,
        validUntil: validationResult.data.validUntil ? new Date(validationResult.data.validUntil) : undefined,
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedRule
    });

  } catch (error) {
    console.error('Update pricing rule error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Unable to update pricing rule'
      },
      { status: 500 }
    );
  }
}

// DELETE - Delete pricing rule
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Rule ID is required'
        },
        { status: 400 }
      );
    }

    await prisma.priceRule.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'Pricing rule deleted successfully'
    });

  } catch (error) {
    console.error('Delete pricing rule error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Unable to delete pricing rule'
      },
      { status: 500 }
    );
  }
}
