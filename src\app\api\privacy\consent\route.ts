import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

const prisma = new PrismaClient();

// Validation schemas
const ConsentUpdateSchema = z.object({
  aiTryOnConsent: z.boolean(),
  privacyPolicyAccepted: z.boolean().optional(),
  dataRetentionDays: z.number().min(1).max(365).optional(),
});

const ConsentWithdrawalSchema = z.object({
  reason: z.string().optional(),
  deleteExistingData: z.boolean().default(false),
});

/**
 * GET /api/privacy/consent - Get user's current consent status
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        aiTryOnConsent: true,
        aiTryOnConsentDate: true,
        privacyPolicyAccepted: true,
        privacyPolicyDate: true,
        dataRetentionDays: true,
        dailyTryOnCount: true,
        lastTryOnDate: true,
        totalTryOnCount: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if daily count needs reset
    const today = new Date();
    const lastTryOn = user.lastTryOnDate;
    const needsReset = !lastTryOn || 
      lastTryOn.toDateString() !== today.toDateString();

    return NextResponse.json({
      success: true,
      data: {
        ...user,
        dailyTryOnCount: needsReset ? 0 : user.dailyTryOnCount,
        dailyLimit: 2, // Server-enforced limit
        canTryOn: user.aiTryOnConsent && (needsReset || user.dailyTryOnCount < 2),
      },
    });

  } catch (error) {
    console.error('Error fetching consent status:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/privacy/consent - Update user consent
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = ConsentUpdateSchema.parse(body);

    const updateData: any = {
      updatedAt: new Date(),
    };

    // Handle AI try-on consent
    if (validatedData.aiTryOnConsent !== undefined) {
      updateData.aiTryOnConsent = validatedData.aiTryOnConsent;
      updateData.aiTryOnConsentDate = validatedData.aiTryOnConsent ? new Date() : null;
      
      // Reset daily count when consent is given
      if (validatedData.aiTryOnConsent) {
        updateData.dailyTryOnCount = 0;
        updateData.lastTryOnDate = null;
      }
    }

    // Handle privacy policy acceptance
    if (validatedData.privacyPolicyAccepted !== undefined) {
      updateData.privacyPolicyAccepted = validatedData.privacyPolicyAccepted;
      updateData.privacyPolicyDate = validatedData.privacyPolicyAccepted ? new Date() : null;
    }

    // Handle data retention preference
    if (validatedData.dataRetentionDays !== undefined) {
      updateData.dataRetentionDays = validatedData.dataRetentionDays;
    }

    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: updateData,
      select: {
        aiTryOnConsent: true,
        aiTryOnConsentDate: true,
        privacyPolicyAccepted: true,
        privacyPolicyDate: true,
        dataRetentionDays: true,
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedUser,
      message: 'Consent preferences updated successfully',
    });

  } catch (error) {
    console.error('Error updating consent:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/privacy/consent - Withdraw consent and optionally delete data
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = ConsentWithdrawalSchema.parse(body);

    // Start a transaction for consent withdrawal
    const result = await prisma.$transaction(async (tx) => {
      // Update user consent
      const updatedUser = await tx.user.update({
        where: { id: session.user.id },
        data: {
          aiTryOnConsent: false,
          aiTryOnConsentDate: null,
          updatedAt: new Date(),
        },
      });

      // Create privacy request for audit trail
      const privacyRequest = await tx.privacyRequest.create({
        data: {
          type: 'CONSENT_WITHDRAWAL',
          status: validatedData.deleteExistingData ? 'IN_PROGRESS' : 'COMPLETED',
          description: validatedData.reason || 'User withdrew AI try-on consent',
          userId: session.user.id,
          completedAt: validatedData.deleteExistingData ? null : new Date(),
        },
      });

      // If user wants to delete existing data, mark AI try-on jobs for deletion
      if (validatedData.deleteExistingData) {
        await tx.aiTryOnJob.updateMany({
          where: { 
            userId: session.user.id,
            isDeleted: false,
          },
          data: {
            isDeleted: true,
            deletedAt: new Date(),
            scheduledDeletion: new Date(), // Immediate deletion
          },
        });
      }

      return { updatedUser, privacyRequest };
    });

    return NextResponse.json({
      success: true,
      data: {
        consentWithdrawn: true,
        dataScheduledForDeletion: validatedData.deleteExistingData,
        privacyRequestId: result.privacyRequest.id,
      },
      message: validatedData.deleteExistingData 
        ? 'Consent withdrawn and data scheduled for deletion'
        : 'Consent withdrawn successfully',
    });

  } catch (error) {
    console.error('Error withdrawing consent:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/privacy/consent - Check and enforce daily limits
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        aiTryOnConsent: true,
        dailyTryOnCount: true,
        lastTryOnDate: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    if (!user.aiTryOnConsent) {
      return NextResponse.json(
        { success: false, error: 'AI try-on consent required' },
        { status: 403 }
      );
    }

    const today = new Date();
    const lastTryOn = user.lastTryOnDate;
    const needsReset = !lastTryOn || 
      lastTryOn.toDateString() !== today.toDateString();

    let currentCount = needsReset ? 0 : user.dailyTryOnCount;

    if (currentCount >= 2) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Daily try-on limit reached',
          data: {
            dailyCount: currentCount,
            dailyLimit: 2,
            resetTime: new Date(today.getTime() + 24 * 60 * 60 * 1000), // Tomorrow
          }
        },
        { status: 429 }
      );
    }

    // Increment counter
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        dailyTryOnCount: currentCount + 1,
        lastTryOnDate: today,
        totalTryOnCount: { increment: 1 },
      },
      select: {
        dailyTryOnCount: true,
        totalTryOnCount: true,
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        dailyCount: updatedUser.dailyTryOnCount,
        dailyLimit: 2,
        totalCount: updatedUser.totalTryOnCount,
        remainingToday: 2 - updatedUser.dailyTryOnCount,
      },
      message: 'Try-on limit checked and updated',
    });

  } catch (error) {
    console.error('Error checking try-on limits:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
