import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

const prisma = new PrismaClient();

// Validation schema
const DeleteRequestSchema = z.object({
  type: z.enum(['IMMEDIATE', 'SCHEDULED']),
  reason: z.string().optional(),
  includeDesigns: z.boolean().default(false),
  includeOrders: z.boolean().default(false),
});

/**
 * POST /api/privacy/delete - Request data deletion
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = DeleteRequestSchema.parse(body);

    // Create privacy request
    const privacyRequest = await prisma.privacyRequest.create({
      data: {
        type: 'DATA_DELETION',
        status: 'PENDING',
        description: validatedData.reason || 'User requested data deletion',
        userId: session.user.id,
      },
    });

    // For immediate deletion, process AI try-on data right away
    if (validatedData.type === 'IMMEDIATE') {
      await prisma.$transaction(async (tx) => {
        // Mark AI try-on jobs for immediate deletion
        await tx.aiTryOnJob.updateMany({
          where: { 
            userId: session.user.id,
            isDeleted: false,
          },
          data: {
            isDeleted: true,
            deletedAt: new Date(),
            scheduledDeletion: new Date(),
          },
        });

        // Optionally delete designs if requested
        if (validatedData.includeDesigns) {
          await tx.design.updateMany({
            where: { userId: session.user.id },
            data: { status: 'ARCHIVED' },
          });

          await tx.customization.updateMany({
            where: { userId: session.user.id },
            data: { status: 'ARCHIVED' },
          });
        }

        // Update privacy request status
        await tx.privacyRequest.update({
          where: { id: privacyRequest.id },
          data: {
            status: 'IN_PROGRESS',
            itemsDeleted: {
              aiTryOnJobs: true,
              designs: validatedData.includeDesigns,
              orders: validatedData.includeOrders,
              timestamp: new Date().toISOString(),
            },
          },
        });
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        requestId: privacyRequest.id,
        type: validatedData.type,
        status: validatedData.type === 'IMMEDIATE' ? 'IN_PROGRESS' : 'PENDING',
        estimatedCompletion: validatedData.type === 'IMMEDIATE' 
          ? new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
          : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      },
      message: validatedData.type === 'IMMEDIATE' 
        ? 'Data deletion started. You will receive confirmation within 24 hours.'
        : 'Data deletion request submitted. Processing will begin within 30 days.',
    });

  } catch (error) {
    console.error('Error processing deletion request:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/privacy/delete - Get user's deletion requests
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const requests = await prisma.privacyRequest.findMany({
      where: { 
        userId: session.user.id,
        type: 'DATA_DELETION',
      },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        status: true,
        description: true,
        createdAt: true,
        processedAt: true,
        completedAt: true,
        itemsDeleted: true,
      },
    });

    return NextResponse.json({
      success: true,
      data: requests,
    });

  } catch (error) {
    console.error('Error fetching deletion requests:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/privacy/delete - Cancel pending deletion request
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const requestId = searchParams.get('requestId');

    if (!requestId) {
      return NextResponse.json(
        { success: false, error: 'Request ID is required' },
        { status: 400 }
      );
    }

    // Check if request exists and belongs to user
    const existingRequest = await prisma.privacyRequest.findFirst({
      where: {
        id: requestId,
        userId: session.user.id,
        type: 'DATA_DELETION',
        status: { in: ['PENDING', 'IN_PROGRESS'] },
      },
    });

    if (!existingRequest) {
      return NextResponse.json(
        { success: false, error: 'Request not found or cannot be cancelled' },
        { status: 404 }
      );
    }

    // Cancel the request
    await prisma.privacyRequest.update({
      where: { id: requestId },
      data: {
        status: 'CANCELLED',
        processedAt: new Date(),
        adminNotes: 'Cancelled by user',
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Deletion request cancelled successfully',
    });

  } catch (error) {
    console.error('Error cancelling deletion request:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
