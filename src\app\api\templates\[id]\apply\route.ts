import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();

// Validation schema for applying templates
const ApplyTemplateSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  productId: z.string().optional(), // Optional if applying to same product
  customizationName: z.string().optional(),
});

// POST /api/templates/[id]/apply - Apply template and track usage
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id: templateId } = params;
    const body = await request.json();

    const validationResult = ApplyTemplateSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: validationResult.error.issues.map((err: any) => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    const { userId, productId, customizationName } = validationResult.data;

    // Fetch template with product info
    const template = await prisma.template.findUnique({
      where: { id: templateId },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            category: true,
            basePrice: true,
          },
        },
      },
    });

    if (!template) {
      return NextResponse.json(
        {
          success: false,
          error: 'Template not found',
        },
        { status: 404 }
      );
    }

    if (!template.isActive) {
      return NextResponse.json(
        {
          success: false,
          error: 'Template is not active',
        },
        { status: 400 }
      );
    }

    // Verify user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'User not found',
        },
        { status: 404 }
      );
    }

    // Use provided productId or template's default product
    const targetProductId = productId || template.productId;

    // Verify target product exists
    const targetProduct = await prisma.product.findUnique({
      where: { id: targetProductId },
    });

    if (!targetProduct) {
      return NextResponse.json(
        {
          success: false,
          error: 'Target product not found',
        },
        { status: 404 }
      );
    }

    // Create a new customization based on the template
    const customization = await prisma.customization.create({
      data: {
        name: customizationName || `${template.name} - ${new Date().toLocaleDateString()}`,
        description: `Applied from template: ${template.name}`,
        designData: template.designData,
        userId,
        productId: targetProductId,
        templateId,
        status: 'DRAFT',
      },
      include: {
        template: {
          select: {
            id: true,
            name: true,
            moodTag: true,
          },
        },
        product: {
          select: {
            id: true,
            name: true,
            category: true,
            basePrice: true,
          },
        },
      },
    });

    // Increment template usage count
    await prisma.template.update({
      where: { id: templateId },
      data: {
        usageCount: {
          increment: 1,
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        customization,
        template: {
          id: template.id,
          name: template.name,
          moodTag: template.moodTag,
          usageCount: template.usageCount + 1,
        },
      },
      message: 'Template applied successfully',
    });
  } catch (error) {
    console.error('Error applying template:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to apply template',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET /api/templates/[id]/apply - Get template application preview
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id: templateId } = params;
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('productId');

    // Fetch template
    const template = await prisma.template.findUnique({
      where: { id: templateId },
      include: {
        product: true,
      },
    });

    if (!template) {
      return NextResponse.json(
        {
          success: false,
          error: 'Template not found',
        },
        { status: 404 }
      );
    }

    // If productId is provided, fetch that product, otherwise use template's product
    const targetProductId = productId || template.productId;
    const targetProduct = await prisma.product.findUnique({
      where: { id: targetProductId },
      include: {
        variants: {
          include: {
            color: true,
            size: true,
            fabric: true,
          },
          take: 5, // Show first 5 variants
        },
      },
    });

    if (!targetProduct) {
      return NextResponse.json(
        {
          success: false,
          error: 'Target product not found',
        },
        { status: 404 }
      );
    }

    // Calculate estimated pricing (basic calculation)
    const basePrice = targetProduct.basePrice;
    const customizationFee = 15.00; // Base customization fee
    const estimatedPrice = Number(basePrice) + customizationFee;

    return NextResponse.json({
      success: true,
      data: {
        template: {
          id: template.id,
          name: template.name,
          description: template.description,
          moodTag: template.moodTag,
          styleKeywords: template.styleKeywords,
          designData: template.designData,
          previewImage: template.previewImage,
          lifestyleContext: template.lifestyleContext,
        },
        product: targetProduct,
        pricing: {
          basePrice: Number(basePrice),
          customizationFee,
          estimatedTotal: estimatedPrice,
          currency: 'USD',
        },
        compatibility: {
          isCompatible: true, // You can add logic to check template-product compatibility
          notes: productId && productId !== template.productId 
            ? 'Template adapted for different product type' 
            : 'Original template design',
        },
      },
    });
  } catch (error) {
    console.error('Error getting template application preview:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get template preview',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
