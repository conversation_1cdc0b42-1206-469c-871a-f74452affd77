import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();

// Validation schema for updating templates
const UpdateTemplateSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().min(1).optional(),
  moodTag: z.string().min(1).optional(),
  styleKeywords: z.array(z.string()).min(1).optional(),
  targetAudience: z.string().min(1).optional(),
  designData: z.object({
    width: z.number().positive(),
    height: z.number().positive(),
    backgroundColor: z.string(),
    elements: z.array(z.any()),
    layers: z.array(z.string()),
  }).optional(),
  previewImage: z.string().url().optional(),
  lifestyleContext: z.array(z.string()).min(1).optional(),
  isFeatured: z.boolean().optional(),
  isActive: z.boolean().optional(),
});

// GET /api/templates/[id] - Fetch single template
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const template = await prisma.template.findUnique({
      where: { id },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            description: true,
            category: true,
            subcategory: true,
            basePrice: true,
            heroImage: true,
            lifestyleImages: true,
            moodTags: true,
            lifestyleTags: true,
          },
        },
        customizations: {
          select: {
            id: true,
            name: true,
            createdAt: true,
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          take: 5,
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });

    if (!template) {
      return NextResponse.json(
        {
          success: false,
          error: 'Template not found',
        },
        { status: 404 }
      );
    }

    // Increment view count (optional analytics)
    await prisma.template.update({
      where: { id },
      data: {
        // Add a viewCount field if you want to track views
        // viewCount: { increment: 1 }
      },
    });

    return NextResponse.json({
      success: true,
      data: template,
    });
  } catch (error) {
    console.error('Error fetching template:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch template',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// PUT /api/templates/[id] - Update template (Admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();

    const validationResult = UpdateTemplateSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid template data',
          details: validationResult.error.issues.map((err: any) => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    // Check if template exists
    const existingTemplate = await prisma.template.findUnique({
      where: { id },
    });

    if (!existingTemplate) {
      return NextResponse.json(
        {
          success: false,
          error: 'Template not found',
        },
        { status: 404 }
      );
    }

    // Update template
    const updatedTemplate = await prisma.template.update({
      where: { id },
      data: {
        ...validationResult.data,
        updatedAt: new Date(),
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            category: true,
            subcategory: true,
            basePrice: true,
            heroImage: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedTemplate,
      message: 'Template updated successfully',
    });
  } catch (error) {
    console.error('Error updating template:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update template',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/templates/[id] - Delete template (Admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // Check if template exists
    const existingTemplate = await prisma.template.findUnique({
      where: { id },
      include: {
        customizations: {
          select: { id: true },
        },
      },
    });

    if (!existingTemplate) {
      return NextResponse.json(
        {
          success: false,
          error: 'Template not found',
        },
        { status: 404 }
      );
    }

    // Check if template has associated customizations
    if (existingTemplate.customizations.length > 0) {
      // Soft delete by setting isActive to false
      await prisma.template.update({
        where: { id },
        data: {
          isActive: false,
          updatedAt: new Date(),
        },
      });

      return NextResponse.json({
        success: true,
        message: 'Template deactivated successfully (has associated customizations)',
      });
    } else {
      // Hard delete if no customizations
      await prisma.template.delete({
        where: { id },
      });

      return NextResponse.json({
        success: true,
        message: 'Template deleted successfully',
      });
    }
  } catch (error) {
    console.error('Error deleting template:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete template',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
