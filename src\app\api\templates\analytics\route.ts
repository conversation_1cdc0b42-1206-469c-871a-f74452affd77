import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET /api/templates/analytics - Get template analytics data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // days
    const templateId = searchParams.get('templateId');

    const periodDays = parseInt(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - periodDays);

    if (templateId) {
      // Get analytics for specific template
      const template = await prisma.template.findUnique({
        where: { id: templateId },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              category: true,
              basePrice: true,
            },
          },
          customizations: {
            where: {
              createdAt: {
                gte: startDate,
              },
            },
            select: {
              id: true,
              createdAt: true,
              status: true,
              user: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
            orderBy: {
              createdAt: 'desc',
            },
          },
        },
      });

      if (!template) {
        return NextResponse.json(
          {
            success: false,
            error: 'Template not found',
          },
          { status: 404 }
        );
      }

      // Calculate daily usage for the period
      const dailyUsage = [];
      for (let i = periodDays - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dayStart = new Date(date.setHours(0, 0, 0, 0));
        const dayEnd = new Date(date.setHours(23, 59, 59, 999));

        const count = template.customizations.filter(
          c => c.createdAt >= dayStart && c.createdAt <= dayEnd
        ).length;

        dailyUsage.push({
          date: dayStart.toISOString().split('T')[0],
          count,
        });
      }

      // Calculate status distribution
      const statusDistribution = template.customizations.reduce((acc, customization) => {
        acc[customization.status] = (acc[customization.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return NextResponse.json({
        success: true,
        data: {
          template: {
            id: template.id,
            name: template.name,
            moodTag: template.moodTag,
            usageCount: template.usageCount,
            isFeatured: template.isFeatured,
            isActive: template.isActive,
            product: template.product,
          },
          analytics: {
            totalUsage: template.customizations.length,
            dailyUsage,
            statusDistribution,
            recentCustomizations: template.customizations.slice(0, 10),
            averageUsagePerDay: template.customizations.length / periodDays,
          },
        },
      });
    } else {
      // Get overall template analytics
      const templates = await prisma.template.findMany({
        include: {
          product: {
            select: {
              id: true,
              name: true,
              category: true,
            },
          },
          customizations: {
            where: {
              createdAt: {
                gte: startDate,
              },
            },
            select: {
              id: true,
              createdAt: true,
              status: true,
            },
          },
        },
        orderBy: {
          usageCount: 'desc',
        },
      });

      // Calculate overall metrics
      const totalTemplates = templates.length;
      const activeTemplates = templates.filter(t => t.isActive).length;
      const featuredTemplates = templates.filter(t => t.isFeatured).length;
      const totalUsage = templates.reduce((sum, t) => sum + t.usageCount, 0);
      const recentUsage = templates.reduce((sum, t) => sum + t.customizations.length, 0);

      // Top performing templates
      const topTemplates = templates
        .sort((a, b) => b.customizations.length - a.customizations.length)
        .slice(0, 10)
        .map(template => ({
          id: template.id,
          name: template.name,
          moodTag: template.moodTag,
          usageCount: template.usageCount,
          recentUsage: template.customizations.length,
          product: template.product,
          isFeatured: template.isFeatured,
        }));

      // Usage by mood tag
      const moodTagUsage = templates.reduce((acc, template) => {
        const usage = template.customizations.length;
        acc[template.moodTag] = (acc[template.moodTag] || 0) + usage;
        return acc;
      }, {} as Record<string, number>);

      // Usage by product category
      const categoryUsage = templates.reduce((acc, template) => {
        const usage = template.customizations.length;
        const category = template.product.category;
        acc[category] = (acc[category] || 0) + usage;
        return acc;
      }, {} as Record<string, number>);

      // Daily usage trend
      const dailyUsage = [];
      for (let i = periodDays - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dayStart = new Date(date.setHours(0, 0, 0, 0));
        const dayEnd = new Date(date.setHours(23, 59, 59, 999));

        const count = templates.reduce((sum, template) => {
          return sum + template.customizations.filter(
            c => c.createdAt >= dayStart && c.createdAt <= dayEnd
          ).length;
        }, 0);

        dailyUsage.push({
          date: dayStart.toISOString().split('T')[0],
          count,
        });
      }

      return NextResponse.json({
        success: true,
        data: {
          overview: {
            totalTemplates,
            activeTemplates,
            featuredTemplates,
            totalUsage,
            recentUsage,
            averageUsagePerTemplate: totalUsage / totalTemplates || 0,
          },
          topTemplates,
          trends: {
            dailyUsage,
            moodTagUsage,
            categoryUsage,
          },
          period: {
            days: periodDays,
            startDate: startDate.toISOString(),
            endDate: new Date().toISOString(),
          },
        },
      });
    }
  } catch (error) {
    console.error('Error fetching template analytics:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch template analytics',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
