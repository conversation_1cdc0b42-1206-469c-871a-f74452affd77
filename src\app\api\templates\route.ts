import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();

// Validation schemas
const TemplateQuerySchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('12'),
  category: z.string().optional(),
  moodTag: z.string().optional(),
  styleKeywords: z.string().optional(),
  targetAudience: z.string().optional(),
  lifestyleContext: z.string().optional(),
  featured: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(['popular', 'recent', 'name', 'usage']).optional().default('popular'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

const CreateTemplateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  description: z.string().min(1, 'Description is required'),
  moodTag: z.string().min(1, 'Mood tag is required'),
  styleKeywords: z.array(z.string()).min(1, 'At least one style keyword is required'),
  targetAudience: z.string().min(1, 'Target audience is required'),
  designData: z.object({
    width: z.number().positive(),
    height: z.number().positive(),
    backgroundColor: z.string(),
    elements: z.array(z.any()),
    layers: z.array(z.string()),
  }),
  previewImage: z.string().url('Valid preview image URL is required'),
  lifestyleContext: z.array(z.string()).min(1, 'At least one lifestyle context is required'),
  productId: z.string().min(1, 'Product ID is required'),
  isFeatured: z.boolean().optional().default(false),
});

// GET /api/templates - Fetch templates with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validationResult = TemplateQuerySchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid query parameters',
          details: validationResult.error.issues,
        },
        { status: 400 }
      );
    }

    const {
      page,
      limit,
      category,
      moodTag,
      styleKeywords,
      targetAudience,
      lifestyleContext,
      featured,
      search,
      sortBy,
      sortOrder,
    } = validationResult.data;

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Build where clause for filtering
    const where: any = {
      isActive: true,
    };

    if (featured === 'true') {
      where.isFeatured = true;
    }

    if (moodTag) {
      where.moodTag = {
        contains: moodTag,
        mode: 'insensitive',
      };
    }

    if (targetAudience) {
      where.targetAudience = {
        contains: targetAudience,
        mode: 'insensitive',
      };
    }

    if (styleKeywords) {
      where.styleKeywords = {
        hasSome: styleKeywords.split(',').map(k => k.trim()),
      };
    }

    if (lifestyleContext) {
      where.lifestyleContext = {
        hasSome: lifestyleContext.split(',').map(c => c.trim()),
      };
    }

    if (search) {
      where.OR = [
        {
          name: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          description: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          moodTag: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    // Build orderBy clause
    let orderBy: any = {};
    switch (sortBy) {
      case 'popular':
        orderBy = { usageCount: sortOrder };
        break;
      case 'recent':
        orderBy = { createdAt: sortOrder };
        break;
      case 'name':
        orderBy = { name: sortOrder };
        break;
      case 'usage':
        orderBy = { usageCount: sortOrder };
        break;
      default:
        orderBy = { usageCount: 'desc' };
    }

    // Fetch templates with pagination
    const [templates, totalCount] = await Promise.all([
      prisma.template.findMany({
        where,
        orderBy,
        skip,
        take: limitNum,
        include: {
          product: {
            select: {
              id: true,
              name: true,
              category: true,
              subcategory: true,
              basePrice: true,
              heroImage: true,
              moodTags: true,
              lifestyleTags: true,
            },
          },
        },
      }),
      prisma.template.count({ where }),
    ]);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limitNum);
    const hasNextPage = pageNum < totalPages;
    const hasPrevPage = pageNum > 1;

    // Get unique filter options for frontend
    const filterOptions = await prisma.template.findMany({
      where: { isActive: true },
      select: {
        moodTag: true,
        styleKeywords: true,
        targetAudience: true,
        lifestyleContext: true,
      },
    });

    const uniqueMoodTags = [...new Set(filterOptions.map(t => t.moodTag))];
    const uniqueStyleKeywords = [...new Set(filterOptions.flatMap(t => t.styleKeywords))];
    const uniqueTargetAudiences = [...new Set(filterOptions.map(t => t.targetAudience))];
    const uniqueLifestyleContexts = [...new Set(filterOptions.flatMap(t => t.lifestyleContext))];

    return NextResponse.json({
      success: true,
      data: {
        templates,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalCount,
          hasNextPage,
          hasPrevPage,
          limit: limitNum,
        },
        filters: {
          moodTags: uniqueMoodTags,
          styleKeywords: uniqueStyleKeywords,
          targetAudiences: uniqueTargetAudiences,
          lifestyleContexts: uniqueLifestyleContexts,
        },
      },
    });
  } catch (error) {
    console.error('Error fetching templates:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch templates',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/templates - Create new template (Admin only)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validationResult = CreateTemplateSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid template data',
          details: validationResult.error.issues.map((err: any) => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    const templateData = validationResult.data;

    // Verify product exists
    const product = await prisma.product.findUnique({
      where: { id: templateData.productId },
    });

    if (!product) {
      return NextResponse.json(
        {
          success: false,
          error: 'Product not found',
        },
        { status: 404 }
      );
    }

    // Create template
    const template = await prisma.template.create({
      data: {
        ...templateData,
        usageCount: 0,
        isActive: true,
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            category: true,
            subcategory: true,
            basePrice: true,
            heroImage: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: template,
      message: 'Template created successfully',
    });
  } catch (error) {
    console.error('Error creating template:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create template',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
