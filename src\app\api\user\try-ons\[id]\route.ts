import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const prisma = new PrismaClient();

/**
 * GET /api/user/try-ons/[id] - Get specific try-on details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const tryOn = await prisma.aiTryOnJob.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
      include: {
        customization: {
          include: {
            product: {
              select: {
                name: true,
                category: true,
                heroImage: true,
              },
            },
          },
        },
      },
    });

    if (!tryOn) {
      return NextResponse.json(
        { success: false, error: 'Try-on not found' },
        { status: 404 }
      );
    }

    // Calculate scheduled deletion date
    let scheduledDeletion = null;
    if (!tryOn.isDeleted && tryOn.status === 'COMPLETED') {
      const retentionDays = 30; // Should be fetched from user preferences
      scheduledDeletion = new Date(
        tryOn.createdAt.getTime() + retentionDays * 24 * 60 * 60 * 1000
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        ...tryOn,
        scheduledDeletion: scheduledDeletion?.toISOString(),
      },
    });

  } catch (error) {
    console.error('Error fetching try-on:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/user/try-ons/[id] - Delete specific try-on
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if try-on exists and belongs to user
    const existingTryOn = await prisma.aiTryOnJob.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
        isDeleted: false,
      },
    });

    if (!existingTryOn) {
      return NextResponse.json(
        { success: false, error: 'Try-on not found or already deleted' },
        { status: 404 }
      );
    }

    // Soft delete the try-on
    await prisma.aiTryOnJob.update({
      where: { id: params.id },
      data: {
        isDeleted: true,
        deletedAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Try-on deleted successfully',
    });

  } catch (error) {
    console.error('Error deleting try-on:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/user/try-ons/[id] - Update try-on (e.g., restore from deletion)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action } = body;

    if (action !== 'restore') {
      return NextResponse.json(
        { success: false, error: 'Invalid action' },
        { status: 400 }
      );
    }

    // Check if try-on exists and belongs to user
    const existingTryOn = await prisma.aiTryOnJob.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    });

    if (!existingTryOn) {
      return NextResponse.json(
        { success: false, error: 'Try-on not found' },
        { status: 404 }
      );
    }

    if (!existingTryOn.isDeleted) {
      return NextResponse.json(
        { success: false, error: 'Try-on is not deleted' },
        { status: 400 }
      );
    }

    // Restore the try-on
    await prisma.aiTryOnJob.update({
      where: { id: params.id },
      data: {
        isDeleted: false,
        deletedAt: null,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Try-on restored successfully',
    });

  } catch (error) {
    console.error('Error restoring try-on:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
