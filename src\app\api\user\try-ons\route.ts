import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const prisma = new PrismaClient();

/**
 * GET /api/user/try-ons - Get user's AI try-on history
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status');
    const includeDeleted = searchParams.get('includeDeleted') === 'true';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      userId: session.user.id,
    };

    if (!includeDeleted) {
      where.isDeleted = false;
    }

    if (status) {
      where.status = status;
    }

    // Get try-ons with related data
    const [tryOns, total] = await Promise.all([
      prisma.aiTryOnJob.findMany({
        where,
        include: {
          customization: {
            select: {
              id: true,
              name: true,
              product: {
                select: {
                  name: true,
                  category: true,
                  heroImage: true,
                },
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.aiTryOnJob.count({ where }),
    ]);

    // Calculate auto-deletion dates for active try-ons
    const tryOnsWithDeletion = tryOns.map(tryOn => {
      let scheduledDeletion = null;
      
      if (!tryOn.isDeleted && tryOn.status === 'COMPLETED') {
        // Get user's retention preference
        const retentionDays = 30; // Default, should be fetched from user preferences
        scheduledDeletion = new Date(
          tryOn.createdAt.getTime() + retentionDays * 24 * 60 * 60 * 1000
        );
      }

      return {
        ...tryOn,
        scheduledDeletion: scheduledDeletion?.toISOString(),
      };
    });

    return NextResponse.json({
      success: true,
      data: tryOnsWithDeletion,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });

  } catch (error) {
    console.error('Error fetching user try-ons:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/user/try-ons - Bulk delete user's try-ons
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { tryOnIds, deleteAll = false } = body;

    if (!deleteAll && (!tryOnIds || !Array.isArray(tryOnIds) || tryOnIds.length === 0)) {
      return NextResponse.json(
        { success: false, error: 'Try-on IDs are required' },
        { status: 400 }
      );
    }

    let deletedCount = 0;

    if (deleteAll) {
      // Delete all user's try-ons
      const result = await prisma.aiTryOnJob.updateMany({
        where: {
          userId: session.user.id,
          isDeleted: false,
        },
        data: {
          isDeleted: true,
          deletedAt: new Date(),
        },
      });
      deletedCount = result.count;
    } else {
      // Delete specific try-ons
      const result = await prisma.aiTryOnJob.updateMany({
        where: {
          id: { in: tryOnIds },
          userId: session.user.id,
          isDeleted: false,
        },
        data: {
          isDeleted: true,
          deletedAt: new Date(),
        },
      });
      deletedCount = result.count;
    }

    return NextResponse.json({
      success: true,
      data: {
        deletedCount,
        message: `${deletedCount} try-on${deletedCount !== 1 ? 's' : ''} deleted successfully`,
      },
    });

  } catch (error) {
    console.error('Error deleting try-ons:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
