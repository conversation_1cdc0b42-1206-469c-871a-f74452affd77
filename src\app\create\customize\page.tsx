'use client';

import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { CustomizationEditor } from '@/components/editor';
import { useEditorStore } from '@/stores/editorStore';

export default function CustomizePage() {
  const searchParams = useSearchParams();
  const templateId = searchParams.get('template');
  const [templateLoading, setTemplateLoading] = useState(false);
  const { loadCanvas, saveToHistory } = useEditorStore();

  // Load template if specified in URL
  useEffect(() => {
    const loadTemplate = async () => {
      if (!templateId) return;

      try {
        setTemplateLoading(true);

        // Fetch template data
        const response = await fetch(`/api/templates/${templateId}`);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Failed to load template');
        }

        if (data.success && data.data) {
          // Apply template to canvas
          loadCanvas(data.data.designData);
          saveToHistory();

          // Track template application
          fetch(`/api/templates/${templateId}/apply`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId: 'current-user-id', // Replace with actual user ID
              customizationName: `${data.data.name} - ${new Date().toLocaleDateString()}`,
            }),
          }).catch(err => {
            console.warn('Failed to track template usage:', err);
          });
        }
      } catch (error) {
        console.error('Error loading template:', error);
        // Show error to user but don't block the editor
        alert('Failed to load template. You can still create your design from scratch.');
      } finally {
        setTemplateLoading(false);
      }
    };

    loadTemplate();
  }, [templateId, loadCanvas, saveToHistory]);

  return (
    <div className="min-h-screen">
      {templateLoading && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div className="w-6 h-6 border-4 border-primary-200 border-t-primary-500 rounded-full animate-spin"></div>
            <span className="text-gray-700">Loading template...</span>
          </div>
        </div>
      )}
      <CustomizationEditor />
    </div>
  );
}
