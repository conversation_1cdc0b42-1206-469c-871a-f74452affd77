'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  ProductSelectionFlow, 
  ProductSelectionStep,
  ProductSelections,
  SelectionPricing,
  SelectedProductType,
  SelectedFabric,
  SelectedColor,
  SelectedSize,
  SelectedCustomization
} from '@/types/product-selection';
import { ProductType, Fabric, Color, Size } from '@/types';
import { usePricing } from '@/hooks/usePricing';

// Import our selection components
import { ProductSelectionStepper } from '@/components/product-selection/ProductSelectionStepper';
import { ProductTypeSelection } from '@/components/product-selection/ProductTypeSelection';
import { FabricSelection } from '@/components/product-selection/FabricSelection';
import { ColorSelection } from '@/components/product-selection/ColorSelection';
import { SizeSelection } from '@/components/product-selection/SizeSelection';
import { CustomizationStep } from '@/components/product-selection/CustomizationStep';
import { StickyPriceBar } from '@/components/product-selection/StickyPriceBar';

interface ProductSelectionPageProps {
  currentStep?: ProductSelectionStep;
  onNext?: () => void;
  onPrev?: () => void;
  onComplete?: (stepId: ProductSelectionStep) => void;
  canGoBack?: boolean;
  canGoForward?: boolean;
  progress?: number;
}

// Mock data - In real app, this would come from API
const mockProductTypes: ProductType[] = [
  {
    id: 'essential-tee',
    name: 'Essential Crew Tee',
    category: 't-shirt',
    subcategory: 'crew-neck',
    moodTags: ['confident', 'versatile', 'comfortable', 'timeless'],
    lifestyleTags: ['casual', 'workwear', 'weekend', 'layering'],
    heroImage: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=800',
    lifestyleImages: [
      {
        url: 'https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=800',
        context: 'street',
        mood: 'confident',
        model: 'diverse',
        alt: 'Street style confidence'
      }
    ],
    tagline: 'Your everyday confidence boost',
    description: 'The perfect foundation for any wardrobe',
    emotionalHook: 'Feel confident and comfortable in any setting',
    basePrice: 29.99,
    currency: 'USD',
    isActive: true,
    isFeatured: true
  }
];

const mockFabrics: Fabric[] = [
  {
    id: 'organic-cotton',
    name: 'Organic Cotton',
    description: 'Sustainably sourced, incredibly soft',
    texture: 'soft',
    weight: 'medium',
    feel: 'comfortable',
    careInstructions: ['Machine wash cold', 'Tumble dry low'],
    durabilityRating: 4,
    emotionalBenefit: 'All-day comfort that moves with you',
    comfortPromise: 'Feels like a gentle hug',
    image: 'https://images.unsplash.com/photo-1586495777744-4413f21062fa?w=400',
    swatchImage: 'https://images.unsplash.com/photo-1586495777744-4413f21062fa?w=100',
    priceModifier: 1.0,
    isActive: true,
    isPremium: false
  }
];

const mockColors: Color[] = [
  {
    id: 'midnight-black',
    name: 'black',
    displayName: 'Midnight Black',
    hexCode: '#1a1a1a',
    mood: ['sophisticated', 'powerful', 'timeless'],
    personality: 'Bold and confident',
    occasion: ['professional', 'evening', 'casual'],
    stylingTips: ['Pairs perfectly with any color', 'Instantly elevates any look'],
    complementaryColors: ['white', 'gold', 'silver'],
    season: ['year-round'],
    trendStatus: 'classic',
    isActive: true,
    stockLevel: 'high'
  }
];

const mockSizes: Size[] = [
  {
    id: 'size-m',
    name: 'M',
    displayName: 'Medium',
    measurements: {
      chest: 40,
      waist: 38,
      length: 28,
      shoulderWidth: 18
    },
    fitDescription: 'Relaxed fit with room to move',
    bodyPositiveMessage: 'Designed to flatter every body',
    confidenceBooster: "You'll feel amazing in this",
    recommendedFor: ['athletic build', 'average frame'],
    fitTips: ['Perfect for layering', 'True to size fit'],
    isActive: true,
    stockLevel: 75
  }
];

const ProductSelectionContent: React.FC<ProductSelectionPageProps> = ({
  currentStep = 'product-type',
  onNext,
  onPrev,
  onComplete,
  canGoBack,
  canGoForward,
  progress
}) => {
  const [selectionFlow, setSelectionFlow] = useState<ProductSelectionFlow>({
    id: `selection-${Date.now()}`,
    sessionId: `session-${Date.now()}`,
    currentStep: 'product-type',
    selections: {
      quantity: 1
    },
    emotionalJourney: [],
    pricing: {
      basePrice: 29.99,
      totalPrice: 29.99,
      currency: 'USD',
      breakdown: [],
      valueMessage: 'Exceptional value for your unique style',
      qualityPromise: 'Premium quality guaranteed'
    },
    startedAt: new Date(),
    lastUpdatedAt: new Date()
  });

  const { calculatePrice } = usePricing();

  // Update pricing when selections change
  useEffect(() => {
    const updatePricing = async () => {
      if (selectionFlow.selections.productType) {
        try {
          const pricingRequest = {
            productId: selectionFlow.selections.productType.id,
            quantity: selectionFlow.selections.quantity,
            fabricId: selectionFlow.selections.fabric?.id,
            // Add other selection IDs as needed
          };

          const pricingResponse = await calculatePrice(pricingRequest);
          
          if (pricingResponse?.success && pricingResponse.data) {
            setSelectionFlow(prev => ({
              ...prev,
              pricing: {
                basePrice: pricingResponse.data!.basePrice,
                totalPrice: pricingResponse.data!.totalPrice,
                currency: pricingResponse.data!.currency,
                breakdown: pricingResponse.data!.breakdown.map(component => ({
                  id: component.id,
                  name: component.name,
                  amount: component.amount,
                  type: component.type as 'base' | 'upgrade' | 'discount',
                  emotionalValue: component.valueMessage,
                  icon: component.icon || '✨'
                })),
                valueMessage: pricingResponse.data!.valueMessage,
                qualityPromise: pricingResponse.data!.qualityPromise
              }
            }));
          }
        } catch (error) {
          console.error('Pricing calculation failed:', error);
        }
      }
    };

    updatePricing();
  }, [selectionFlow.selections, calculatePrice]);

  const handleProductTypeSelect = (productType: ProductType, emotionalReason: string) => {
    const selectedProductType: SelectedProductType = {
      id: productType.id,
      name: productType.name,
      emotionalReason,
      lifestyleMatch: productType.lifestyleTags,
      confidenceLevel: 5
    };

    setSelectionFlow(prev => ({
      ...prev,
      selections: {
        ...prev.selections,
        productType: selectedProductType
      },
      lastUpdatedAt: new Date()
    }));

    onComplete?.('product-type');
  };

  const handleFabricSelect = (fabric: Fabric, sensoryAppeal: string, touchExperience: number) => {
    const selectedFabric: SelectedFabric = {
      id: fabric.id,
      name: fabric.name,
      sensoryAppeal,
      comfortPriority: fabric.emotionalBenefit,
      touchExperience
    };

    setSelectionFlow(prev => ({
      ...prev,
      selections: {
        ...prev.selections,
        fabric: selectedFabric
      },
      lastUpdatedAt: new Date()
    }));

    onComplete?.('fabric');
  };

  const handleColorSelect = (color: Color, emotionalConnection: string, personalityExpression: string) => {
    const selectedColor: SelectedColor = {
      id: color.id,
      name: color.name,
      hexCode: color.hexCode,
      emotionalConnection,
      personalityExpression,
      occasionIntent: color.occasion
    };

    setSelectionFlow(prev => ({
      ...prev,
      selections: {
        ...prev.selections,
        color: selectedColor
      },
      lastUpdatedAt: new Date()
    }));

    onComplete?.('color');
  };

  const handleSizeSelect = (size: Size, fitPreference: string, confidenceBoost: string) => {
    const selectedSize: SelectedSize = {
      id: size.id,
      name: size.name,
      fitPreference,
      confidenceBoost,
      bodyPositivity: 5
    };

    setSelectionFlow(prev => ({
      ...prev,
      selections: {
        ...prev.selections,
        size: selectedSize
      },
      lastUpdatedAt: new Date()
    }));

    onComplete?.('size');
  };

  const handleCustomizationComplete = (customization: SelectedCustomization) => {
    setSelectionFlow(prev => ({
      ...prev,
      selections: {
        ...prev.selections,
        customization
      },
      lastUpdatedAt: new Date()
    }));

    onComplete?.('customize');
  };

  const canProceed = () => {
    switch (currentStep) {
      case 'product-type':
        return !!selectionFlow.selections.productType;
      case 'fabric':
        return !!selectionFlow.selections.fabric;
      case 'color':
        return !!selectionFlow.selections.color;
      case 'size':
        return !!selectionFlow.selections.size;
      case 'customize':
        return !!selectionFlow.selections.customization;
      default:
        return false;
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'product-type':
        return (
          <ProductTypeSelection
            productTypes={mockProductTypes}
            selectedType={mockProductTypes.find(pt => pt.id === selectionFlow.selections.productType?.id)}
            onSelect={handleProductTypeSelect}
          />
        );
      case 'fabric':
        return (
          <FabricSelection
            fabrics={mockFabrics}
            selectedFabric={mockFabrics.find(f => f.id === selectionFlow.selections.fabric?.id)}
            onSelect={handleFabricSelect}
          />
        );
      case 'color':
        return (
          <ColorSelection
            colors={mockColors}
            selectedColor={mockColors.find(c => c.id === selectionFlow.selections.color?.id)}
            onSelect={handleColorSelect}
          />
        );
      case 'size':
        return (
          <SizeSelection
            sizes={mockSizes}
            selectedSize={mockSizes.find(s => s.id === selectionFlow.selections.size?.id)}
            onSelect={handleSizeSelect}
          />
        );
      case 'customize':
        return (
          <CustomizationStep
            onNext={handleCustomizationComplete}
            onPrev={onPrev}
          />
        );
      default:
        return <div>Step not found</div>;
    }
  };

  return (
    <>
      {renderCurrentStep()}
      
      {/* Sticky Price Bar */}
      <StickyPriceBar
        pricing={selectionFlow.pricing}
        isVisible={true}
        canProceed={canProceed()}
        currentStep={currentStep}
        totalSteps={5}
        onNext={onNext || (() => {})}
      />
    </>
  );
};

export default function ProductSelectionPage() {
  const [currentStep, setCurrentStep] = useState<ProductSelectionStep>('product-type');

  const handleStepChange = (step: ProductSelectionStep) => {
    setCurrentStep(step);
  };

  return (
    <ProductSelectionStepper
      initialStep={currentStep}
      onStepChange={handleStepChange}
      className="min-h-screen"
    >
      <ProductSelectionContent currentStep={currentStep} />
    </ProductSelectionStepper>
  );
}
