@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables for Brand Colors */
:root {
  --color-primary: 245 149 50;
  --color-warm: 234 143 92;
  --color-accent: 231 92 88;
  --color-emerald: 16 185 129;
  --color-royal: 99 102 241;
}

/* Base Styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply text-gray-900 leading-relaxed;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-primary-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary-400;
  }
}

/* Component Styles */
@layer components {
  /* Button Variants */
  .btn-primary {
    @apply bg-gradient-to-r from-primary-500 to-warm-500 hover:from-primary-600 hover:to-warm-600 text-white font-semibold py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105 shadow-warm hover:shadow-glow;
  }
  
  .btn-secondary {
    @apply bg-white hover:bg-gray-50 text-primary-600 font-semibold py-3 px-6 rounded-full border-2 border-primary-200 hover:border-primary-300 transition-all duration-300;
  }
  
  .btn-ghost {
    @apply text-primary-600 hover:text-primary-700 font-semibold py-3 px-6 rounded-full hover:bg-primary-50 transition-all duration-300;
  }
  
  /* Card Styles */
  .card {
    @apply bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/50 shadow-soft hover:shadow-lg transition-all duration-300;
  }
  
  .card-premium {
    @apply bg-gradient-to-br from-white to-primary-50/30 backdrop-blur-sm rounded-2xl border border-primary-200/50 shadow-warm hover:shadow-glow transition-all duration-300;
  }
  
  /* Input Styles */
  .input-primary {
    @apply w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-primary-400 focus:ring-4 focus:ring-primary-100 transition-all duration-200 bg-white/80 backdrop-blur-sm;
  }
  
  /* Text Gradients */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 via-warm-600 to-accent-600 bg-clip-text text-transparent;
  }
  
  .text-gradient-warm {
    @apply bg-gradient-to-r from-warm-500 to-primary-600 bg-clip-text text-transparent;
  }
  
  /* Layout Utilities */
  .container-fluid {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section-padding {
    @apply py-16 md:py-24;
  }
  
  /* Animation Utilities */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }
}

/* Utility Styles */
@layer utilities {
  /* Glass morphism effect */
  .glass {
    @apply bg-white/20 backdrop-blur-md border border-white/30;
  }
  
  .glass-warm {
    @apply bg-primary-50/20 backdrop-blur-md border border-primary-200/30;
  }
  
  /* Gradient backgrounds */
  .bg-gradient-warm {
    background: linear-gradient(135deg, rgb(254 250 245) 0%, rgb(253 237 211) 100%);
  }
  
  .bg-gradient-primary {
    background: linear-gradient(135deg, rgb(245 149 50) 0%, rgb(234 143 92) 100%);
  }
  
  /* Text selection */
  .selection-primary {
    @apply selection:bg-primary-200 selection:text-primary-900;
  }
}

/* Custom Keyframes */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  from { box-shadow: 0 0 20px rgba(245, 149, 50, 0.4); }
  to { box-shadow: 0 0 30px rgba(245, 149, 50, 0.6); }
}

/* Mobile-first responsive design helpers */
@media (max-width: 640px) {
  .mobile-padding {
    @apply px-4 py-6;
  }

  .mobile-text {
    @apply text-sm leading-relaxed;
  }

  /* Touch-friendly interactions */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Mobile-optimized cards */
  .mobile-card {
    @apply rounded-xl shadow-lg;
  }

  /* Mobile typography scale */
  .mobile-h1 {
    @apply text-2xl font-bold leading-tight;
  }

  .mobile-h2 {
    @apply text-xl font-semibold leading-snug;
  }

  .mobile-body {
    @apply text-base leading-relaxed;
  }

  /* Mobile spacing */
  .mobile-section {
    @apply py-8 px-4;
  }

  .mobile-grid {
    @apply grid-cols-1 gap-4;
  }
}

/* Tablet optimizations */
@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-grid {
    @apply grid-cols-2 gap-6;
  }

  .tablet-padding {
    @apply px-6 py-8;
  }
}

/* Desktop enhancements */
@media (min-width: 1025px) {
  .desktop-grid {
    @apply grid-cols-3 gap-8;
  }

  .desktop-padding {
    @apply px-8 py-12;
  }

  /* Hover states only on desktop */
  .desktop-hover:hover {
    @apply transform scale-105 shadow-xl;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Remove hover effects on touch devices */
  .no-touch-hover:hover {
    @apply transform-none shadow-none;
  }

  /* Larger touch targets */
  .touch-button {
    @apply py-4 px-6 text-lg;
  }

  /* Simplified animations for performance */
  .touch-simple {
    @apply transition-transform duration-200;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .high-contrast {
    @apply border-2 border-gray-900;
  }

  .high-contrast-text {
    @apply text-gray-900;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .respect-motion-preference {
    @apply transition-none;
  }

  .respect-motion-preference * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
