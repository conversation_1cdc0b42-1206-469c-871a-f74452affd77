'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Container, Section } from '@/components/layout';
import { Button } from '@/components/ui';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';

interface PrivacySettings {
  aiTryOnConsent: boolean;
  aiTryOnConsentDate?: string;
  privacyPolicyAccepted: boolean;
  privacyPolicyDate?: string;
  dataRetentionDays: number;
  dailyTryOnCount: number;
  totalTryOnCount: number;
}

interface DeletionRequest {
  id: string;
  status: string;
  description: string;
  createdAt: string;
  processedAt?: string;
  completedAt?: string;
}

export default function PrivacyPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [settings, setSettings] = useState<PrivacySettings | null>(null);
  const [deletionRequests, setDeletionRequests] = useState<DeletionRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }

    if (status === 'authenticated') {
      fetchPrivacySettings();
    }
  }, [status, router]);

  const fetchPrivacySettings = async () => {
    try {
      setLoading(true);
      
      // Fetch current privacy settings
      const settingsResponse = await fetch('/api/privacy/consent');
      if (settingsResponse.ok) {
        const settingsData = await settingsResponse.json();
        setSettings(settingsData.data);
      }

      // Fetch deletion requests
      const deletionResponse = await fetch('/api/privacy/delete');
      if (deletionResponse.ok) {
        const deletionData = await deletionResponse.json();
        setDeletionRequests(deletionData.data || []);
      }
    } catch (error) {
      console.error('Error fetching privacy settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateRetention = async (days: number) => {
    try {
      setSaving(true);
      const response = await fetch('/api/privacy/consent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          aiTryOnConsent: settings?.aiTryOnConsent,
          dataRetentionDays: days,
        }),
      });

      if (response.ok) {
        await fetchPrivacySettings();
      }
    } catch (error) {
      console.error('Error updating retention:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleWithdrawConsent = async () => {
    if (!confirm('Are you sure you want to withdraw consent? This will disable AI try-on features and optionally delete your existing data.')) {
      return;
    }

    const deleteData = confirm('Do you also want to delete all your existing try-on data immediately?');

    try {
      setSaving(true);
      const response = await fetch('/api/privacy/consent', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          reason: 'User withdrew consent from privacy settings',
          deleteExistingData: deleteData,
        }),
      });

      if (response.ok) {
        await fetchPrivacySettings();
      }
    } catch (error) {
      console.error('Error withdrawing consent:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleRequestDeletion = async () => {
    if (!confirm('Are you sure you want to request deletion of all your data? This action cannot be undone.')) {
      return;
    }

    try {
      setSaving(true);
      const response = await fetch('/api/privacy/delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'IMMEDIATE',
          reason: 'User requested data deletion from privacy settings',
          includeDesigns: confirm('Do you also want to delete your custom designs?'),
        }),
      });

      if (response.ok) {
        await fetchPrivacySettings();
        alert('Data deletion request submitted successfully. You will receive confirmation within 24 hours.');
      }
    } catch (error) {
      console.error('Error requesting deletion:', error);
    } finally {
      setSaving(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-warm-50 to-amber-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-warm-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading privacy settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-warm-50 to-amber-50">
      <Section variant="primary" padding="lg">
        <Container>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-4xl mx-auto"
          >
            {/* Header */}
            <div className="text-center mb-12">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Privacy & Data Settings
              </h1>
              <p className="text-xl text-gray-600">
                Control how your data is used and manage your privacy preferences
              </p>
            </div>

            {/* AI Try-On Consent Status */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 mb-8">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-semibold text-gray-900">AI Try-On Status</h2>
                <div className={`px-4 py-2 rounded-full text-sm font-medium ${
                  settings?.aiTryOnConsent 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {settings?.aiTryOnConsent ? 'Consent Given' : 'Consent Not Given'}
                </div>
              </div>

              {settings?.aiTryOnConsent ? (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center p-4 bg-warm-50 rounded-lg">
                      <div className="text-3xl font-bold text-warm-600 mb-2">
                        {settings.dailyTryOnCount}
                      </div>
                      <div className="text-sm text-gray-600">Try-ons Today</div>
                    </div>
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-3xl font-bold text-blue-600 mb-2">
                        {settings.totalTryOnCount}
                      </div>
                      <div className="text-sm text-gray-600">Total Try-ons</div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <div className="text-3xl font-bold text-purple-600 mb-2">
                        {settings.dataRetentionDays}
                      </div>
                      <div className="text-sm text-gray-600">Days Kept</div>
                    </div>
                  </div>

                  {settings.aiTryOnConsentDate && (
                    <p className="text-sm text-gray-600">
                      Consent given on {formatDate(settings.aiTryOnConsentDate)}
                    </p>
                  )}

                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button
                      onClick={handleWithdrawConsent}
                      variant="outline"
                      className="text-red-600 border-red-300 hover:bg-red-50"
                      disabled={saving}
                    >
                      Withdraw Consent
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-gray-600 mb-4">
                    You haven't given consent for AI try-on features yet.
                  </div>
                  <Button
                    onClick={() => router.push('/customize')}
                    className="bg-gradient-to-r from-warm-500 to-amber-500 hover:from-warm-600 hover:to-amber-600"
                  >
                    Learn About AI Try-On
                  </Button>
                </div>
              )}
            </div>

            {/* Data Retention Settings */}
            {settings?.aiTryOnConsent && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 mb-8">
                <h2 className="text-2xl font-semibold text-gray-900 mb-6">Data Retention</h2>
                
                <div className="space-y-4">
                  <p className="text-gray-600">
                    Choose how long we keep your try-on images. After this period, they are automatically deleted.
                  </p>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {[7, 14, 30, 90].map((days) => (
                      <button
                        key={days}
                        onClick={() => handleUpdateRetention(days)}
                        disabled={saving}
                        className={`p-4 rounded-lg border-2 transition-colors ${
                          settings.dataRetentionDays === days
                            ? 'border-warm-500 bg-warm-50 text-warm-700'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <div className="text-2xl font-bold mb-1">{days}</div>
                        <div className="text-sm">Days</div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Data Deletion */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">Data Deletion</h2>
              
              <div className="space-y-6">
                <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-red-800 mb-2">Delete All Data</h3>
                  <p className="text-red-700 mb-4">
                    Permanently delete all your try-on data, images, and related information. This action cannot be undone.
                  </p>
                  <Button
                    onClick={handleRequestDeletion}
                    variant="outline"
                    className="text-red-600 border-red-300 hover:bg-red-50"
                    disabled={saving}
                  >
                    Request Data Deletion
                  </Button>
                </div>

                {/* Deletion Requests History */}
                {deletionRequests.length > 0 && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Deletion Requests</h3>
                    <div className="space-y-3">
                      {deletionRequests.map((request) => (
                        <div key={request.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex justify-between items-start">
                            <div>
                              <div className="font-medium text-gray-900">{request.description}</div>
                              <div className="text-sm text-gray-600">
                                Requested on {formatDate(request.createdAt)}
                              </div>
                            </div>
                            <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                              request.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                              request.status === 'IN_PROGRESS' ? 'bg-yellow-100 text-yellow-800' :
                              request.status === 'PENDING' ? 'bg-blue-100 text-blue-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {request.status}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Privacy Policy */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">Privacy Information</h2>
              
              <div className="prose max-w-none">
                <p className="text-gray-600 mb-4">
                  We are committed to protecting your privacy and being transparent about how we handle your data.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium text-gray-900">What we collect</h3>
                    <ul className="text-sm text-gray-600 space-y-2">
                      <li>• Photos you upload for AI try-on</li>
                      <li>• Your custom designs and preferences</li>
                      <li>• Usage data and analytics</li>
                      <li>• Account information</li>
                    </ul>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium text-gray-900">How we protect it</h3>
                    <ul className="text-sm text-gray-600 space-y-2">
                      <li>• End-to-end encryption</li>
                      <li>• Automatic deletion after retention period</li>
                      <li>• No sharing with third parties</li>
                      <li>• Secure processing infrastructure</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </Container>
      </Section>
    </div>
  );
}
