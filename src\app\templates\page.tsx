'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Container, Section } from '@/components/layout';
import { Button } from '@/components/ui';
import { TemplateCard } from '@/components/templates/TemplateCard';
import { TemplateFilters } from '@/components/templates/TemplateFilters';
import { TemplateSearch } from '@/components/templates/TemplateSearch';
import { TemplatePagination } from '@/components/templates/TemplatePagination';
import { EmptyState } from '@/components/templates/EmptyState';
import { LoadingGrid } from '@/components/templates/LoadingGrid';
import { TemplatePreviewModal } from '@/components/templates/TemplatePreviewModal';

interface Template {
  id: string;
  name: string;
  description: string;
  moodTag: string;
  styleKeywords: string[];
  targetAudience: string;
  designData: any;
  previewImage: string;
  lifestyleContext: string[];
  usageCount: number;
  isFeatured: boolean;
  createdAt: string;
  product: {
    id: string;
    name: string;
    category: string;
    subcategory?: string;
    basePrice: number;
    heroImage: string;
    moodTags: string[];
    lifestyleTags: string[];
  };
}

interface TemplateFilters {
  moodTags: string[];
  styleKeywords: string[];
  targetAudiences: string[];
  lifestyleContexts: string[];
}

interface PaginationData {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  limit: number;
}

export default function TemplatesPage() {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [filters, setFilters] = useState<TemplateFilters>({
    moodTags: [],
    styleKeywords: [],
    targetAudiences: [],
    lifestyleContexts: [],
  });
  const [pagination, setPagination] = useState<PaginationData>({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    hasNextPage: false,
    hasPrevPage: false,
    limit: 12,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filter states
  const [selectedMoodTag, setSelectedMoodTag] = useState<string>('');
  const [selectedStyleKeywords, setSelectedStyleKeywords] = useState<string[]>([]);
  const [selectedLifestyleContext, setSelectedLifestyleContext] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortBy, setSortBy] = useState<'popular' | 'recent' | 'name'>('popular');
  const [showFeaturedOnly, setShowFeaturedOnly] = useState<boolean>(false);

  // Preview modal state
  const [previewTemplateId, setPreviewTemplateId] = useState<string | null>(null);

  // Fetch templates
  const fetchTemplates = async (page: number = 1) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        sortBy,
        sortOrder: 'desc',
      });

      if (selectedMoodTag) params.append('moodTag', selectedMoodTag);
      if (selectedStyleKeywords.length > 0) {
        params.append('styleKeywords', selectedStyleKeywords.join(','));
      }
      if (selectedLifestyleContext) {
        params.append('lifestyleContext', selectedLifestyleContext);
      }
      if (searchQuery) params.append('search', searchQuery);
      if (showFeaturedOnly) params.append('featured', 'true');

      const response = await fetch(`/api/templates?${params}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch templates');
      }

      if (data.success) {
        setTemplates(data.data.templates);
        setPagination(data.data.pagination);
        setFilters(data.data.filters);
      } else {
        throw new Error(data.error || 'Failed to fetch templates');
      }
    } catch (err) {
      console.error('Error fetching templates:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch templates');
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchTemplates();
  }, []);

  // Refetch when filters change
  useEffect(() => {
    if (!loading) {
      fetchTemplates(1); // Reset to page 1 when filters change
    }
  }, [selectedMoodTag, selectedStyleKeywords, selectedLifestyleContext, searchQuery, sortBy, showFeaturedOnly]);

  // Handle template application
  const handleApplyTemplate = async (templateId: string) => {
    try {
      // Redirect to create page with template
      window.location.href = `/create/customize?template=${templateId}`;
    } catch (error) {
      console.error('Error applying template:', error);
    }
  };

  // Handle template preview
  const handlePreviewTemplate = (templateId: string) => {
    setPreviewTemplateId(templateId);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    fetchTemplates(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Clear all filters
  const clearFilters = () => {
    setSelectedMoodTag('');
    setSelectedStyleKeywords([]);
    setSelectedLifestyleContext('');
    setSearchQuery('');
    setShowFeaturedOnly(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-warm-50 to-amber-50">
      <Section variant="primary" padding="lg">
        <Container>
          {/* Header */}
          <div className="text-center space-y-6 mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Ready-Made Templates
              </h1>
              <p className="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
                Instant inspiration for your next creation. Choose from our curated collection 
                of lifestyle-driven designs that capture your unique style and mood.
              </p>
            </motion.div>

            {/* Quick Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex justify-center items-center space-x-8 text-sm text-gray-600"
            >
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-primary-500 rounded-full"></span>
                <span>{pagination.totalCount} Templates</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-accent-500 rounded-full"></span>
                <span>{filters.moodTags.length} Mood Styles</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-secondary-500 rounded-full"></span>
                <span>One-Click Apply</span>
              </div>
            </motion.div>
          </div>

          {/* Search and Filters */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mb-8"
          >
            <TemplateSearch
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
              sortBy={sortBy}
              onSortChange={setSortBy}
              showFeaturedOnly={showFeaturedOnly}
              onFeaturedToggle={setShowFeaturedOnly}
            />
            
            <TemplateFilters
              filters={filters}
              selectedMoodTag={selectedMoodTag}
              selectedStyleKeywords={selectedStyleKeywords}
              selectedLifestyleContext={selectedLifestyleContext}
              onMoodTagChange={setSelectedMoodTag}
              onStyleKeywordsChange={setSelectedStyleKeywords}
              onLifestyleContextChange={setSelectedLifestyleContext}
              onClearFilters={clearFilters}
            />
          </motion.div>

          {/* Content */}
          {loading ? (
            <LoadingGrid />
          ) : error ? (
            <div className="text-center py-12">
              <div className="text-red-600 mb-4">⚠️ {error}</div>
              <Button onClick={() => fetchTemplates()}>
                Try Again
              </Button>
            </div>
          ) : templates.length === 0 ? (
            <EmptyState onClearFilters={clearFilters} />
          ) : (
            <>
              {/* Templates Grid */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12"
              >
                <AnimatePresence>
                  {templates.map((template, index) => (
                    <motion.div
                      key={template.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ delay: index * 0.1 }}
                      layout
                    >
                      <TemplateCard
                        template={template}
                        onApply={() => handleApplyTemplate(template.id)}
                        onPreview={() => handlePreviewTemplate(template.id)}
                      />
                    </motion.div>
                  ))}
                </AnimatePresence>
              </motion.div>

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <TemplatePagination
                  pagination={pagination}
                  onPageChange={handlePageChange}
                />
              )}
            </>
          )}
        </Container>
      </Section>

      {/* Template Preview Modal */}
      <TemplatePreviewModal
        templateId={previewTemplateId}
        isOpen={!!previewTemplateId}
        onClose={() => setPreviewTemplateId(null)}
        onApply={handleApplyTemplate}
      />
    </div>
  );
}
