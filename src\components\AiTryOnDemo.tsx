/**
 * AI Try-On Demo Component
 * 
 * Example component demonstrating how to use the AI Try-On functionality
 * This shows the complete flow from image upload to result display
 */

'use client';

import React, { useRef } from 'react';
import { useAiTryOn } from '@/hooks/useAiTryOn';
import { PhotoIcon, SparklesIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface AiTryOnDemoProps {
  customizationId: string;
  userId: string;
  className?: string;
}

export function AiTryOnDemo({ customizationId, userId, className = '' }: AiTryOnDemoProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const {
    // State
    selectedImage,
    imagePreview,
    isLoading,
    isUploading,
    isProcessing,
    error,
    resultImageUrl,
    confidence,
    progress,
    estimatedTimeRemaining,
    status,
    
    // Actions
    selectImage,
    clearImage,
    generateTryOn,
    reset,
    clearError,
  } = useAiTryOn();

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      selectImage(file);
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file) {
      selectImage(file);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleGenerateTryOn = () => {
    generateTryOn(customizationId, userId, 'outdoor');
  };

  const handleReset = () => {
    reset();
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={`max-w-4xl mx-auto p-6 ${className}`}>
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          See Yourself in This Design
        </h2>
        <p className="text-lg text-gray-600">
          Upload your photo and watch the magic happen ✨
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Upload Section */}
        <div className="space-y-6">
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Upload Your Photo
            </h3>
            
            {!imagePreview ? (
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onClick={() => fileInputRef.current?.click()}
              >
                <PhotoIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p className="text-gray-600 mb-2">
                  Drop your photo here or click to browse
                </p>
                <p className="text-sm text-gray-500">
                  JPEG, PNG, or WebP up to 5MB
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="hidden"
                />
              </div>
            ) : (
              <div className="relative">
                <img
                  src={imagePreview}
                  alt="Selected photo"
                  className="w-full h-64 object-cover rounded-lg"
                />
                <button
                  onClick={clearImage}
                  className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              </div>
            )}

            {isUploading && (
              <div className="mt-4">
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">Processing image...</span>
                </div>
              </div>
            )}

            {error && (
              <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center justify-between">
                  <p className="text-red-700">{error}</p>
                  <button
                    onClick={clearError}
                    className="text-red-500 hover:text-red-700"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            )}

            {selectedImage && !isLoading && !error && (
              <button
                onClick={handleGenerateTryOn}
                disabled={isProcessing}
                className="w-full mt-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-purple-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                <SparklesIcon className="h-5 w-5 mr-2" />
                {isProcessing ? 'Creating Magic...' : 'Try It On!'}
              </button>
            )}
          </div>

          {/* Progress Section */}
          {isProcessing && (
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                AI Magic in Progress
              </h3>
              
              <div className="space-y-4">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
                
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Status: {status}</span>
                  <span>{progress}% complete</span>
                </div>
                
                {estimatedTimeRemaining && (
                  <p className="text-sm text-gray-500 text-center">
                    Estimated time remaining: {Math.round(estimatedTimeRemaining)}s
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Result Section */}
        <div className="space-y-6">
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Your Try-On Result
            </h3>
            
            {!resultImageUrl ? (
              <div className="bg-gray-50 rounded-lg p-8 text-center">
                <SparklesIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p className="text-gray-600">
                  Your AI try-on result will appear here
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <img
                  src={resultImageUrl}
                  alt="AI try-on result"
                  className="w-full h-64 object-cover rounded-lg"
                />
                
                {confidence && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">AI Confidence:</span>
                    <span className="font-semibold text-green-600">
                      {Math.round(confidence * 100)}%
                    </span>
                  </div>
                )}
                
                <div className="flex space-x-3">
                  <button
                    onClick={() => {
                      // Download functionality
                      const link = document.createElement('a');
                      link.href = resultImageUrl;
                      link.download = 'ai-tryon-result.jpg';
                      link.click();
                    }}
                    className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors"
                  >
                    Download
                  </button>
                  
                  <button
                    onClick={() => {
                      // Share functionality
                      if (navigator.share) {
                        navigator.share({
                          title: 'Check out my AI try-on!',
                          text: 'I tried on this design using AI - what do you think?',
                          url: window.location.href,
                        });
                      }
                    }}
                    className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                  >
                    Share
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Tips Section */}
          <div className="bg-gradient-to-br from-purple-50 to-blue-50 rounded-xl p-6">
            <h4 className="font-semibold text-gray-900 mb-3">
              💡 Tips for Best Results
            </h4>
            <ul className="space-y-2 text-sm text-gray-700">
              <li>• Use a clear, well-lit photo</li>
              <li>• Face the camera directly</li>
              <li>• Wear fitted clothing for better results</li>
              <li>• Avoid busy backgrounds</li>
              <li>• Higher resolution photos work better</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Reset Button */}
      {(selectedImage || resultImageUrl) && (
        <div className="text-center mt-8">
          <button
            onClick={handleReset}
            className="text-gray-600 hover:text-gray-800 underline"
          >
            Start Over
          </button>
        </div>
      )}
    </div>
  );
}
