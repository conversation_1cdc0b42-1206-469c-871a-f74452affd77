'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface AnalyticsData {
  overview: {
    totalJobs: number;
    successfulJobs: number;
    failedJobs: number;
    averageProcessingTime: number;
    averageConfidence: number;
    totalUsers: number;
    activeProducts: number;
  };
  trends: {
    dailyJobs: Array<{ date: string; total: number; successful: number; failed: number }>;
    categoryPerformance: Array<{ category: string; jobs: number; successRate: number; avgConfidence: number }>;
    userEngagement: Array<{ date: string; uniqueUsers: number; repeatUsers: number }>;
  };
  topProducts: Array<{
    id: string;
    name: string;
    category: string;
    totalJobs: number;
    successRate: number;
    avgConfidence: number;
    heroImage: string;
  }>;
  recentFailures: Array<{
    id: string;
    productName: string;
    errorMessage: string;
    createdAt: string;
    retryCount: number;
  }>;
}

export function AiTryOnAnalyticsTab() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/ai-tryon/analytics?range=${timeRange}`);
      const data = await response.json();
      
      if (data.success) {
        setAnalyticsData(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-warm-600"></div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Failed to load analytics data</p>
      </div>
    );
  }

  const { overview, trends, topProducts, recentFailures } = analyticsData;
  const successRate = overview.totalJobs > 0 ? (overview.successfulJobs / overview.totalJobs) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">AI Try-On Analytics</h2>
        <div className="flex space-x-2">
          {[
            { value: '7d', label: '7 Days' },
            { value: '30d', label: '30 Days' },
            { value: '90d', label: '90 Days' },
          ].map((option) => (
            <button
              key={option.value}
              onClick={() => setTimeRange(option.value as any)}
              className={`
                px-4 py-2 text-sm font-medium rounded-md
                ${timeRange === option.value
                  ? 'bg-warm-600 text-white'
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }
              `}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
        {[
          { label: 'Total Jobs', value: overview.totalJobs, color: 'bg-blue-500', icon: '📊' },
          { label: 'Success Rate', value: `${successRate.toFixed(1)}%`, color: successRate > 90 ? 'bg-green-500' : successRate > 70 ? 'bg-yellow-500' : 'bg-red-500', icon: '✅' },
          { label: 'Failed Jobs', value: overview.failedJobs, color: 'bg-red-500', icon: '❌' },
          { label: 'Avg Time', value: `${overview.averageProcessingTime}s`, color: 'bg-purple-500', icon: '⚡' },
          { label: 'Avg Confidence', value: `${(overview.averageConfidence * 100).toFixed(1)}%`, color: 'bg-indigo-500', icon: '🎯' },
          { label: 'Active Users', value: overview.totalUsers, color: 'bg-pink-500', icon: '👥' },
          { label: 'Active Products', value: overview.activeProducts, color: 'bg-teal-500', icon: '🎨' },
        ].map((stat, index) => (
          <motion.div
            key={stat.label}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-lg p-4 shadow-sm border border-gray-200"
          >
            <div className="flex items-center space-x-3">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm ${stat.color}`}>
                {stat.icon}
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                <div className="text-sm text-gray-500">{stat.label}</div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Daily Jobs Trend */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Daily Jobs Trend</h3>
          <div className="h-64 flex items-end justify-between space-x-1">
            {trends.dailyJobs.map((day, index) => {
              const maxJobs = Math.max(...trends.dailyJobs.map(d => d.total));
              const height = maxJobs > 0 ? (day.total / maxJobs) * 100 : 0;
              
              return (
                <div key={day.date} className="flex-1 flex flex-col items-center">
                  <div className="w-full flex flex-col justify-end h-48">
                    <div
                      className="w-full bg-green-500 rounded-t"
                      style={{ height: `${(day.successful / day.total) * height}%` }}
                      title={`Successful: ${day.successful}`}
                    ></div>
                    <div
                      className="w-full bg-red-500"
                      style={{ height: `${(day.failed / day.total) * height}%` }}
                      title={`Failed: ${day.failed}`}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-500 mt-2 transform -rotate-45">
                    {new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                  </div>
                </div>
              );
            })}
          </div>
          <div className="flex justify-center space-x-4 mt-4 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span>Successful</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded"></div>
              <span>Failed</span>
            </div>
          </div>
        </div>

        {/* Category Performance */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Category Performance</h3>
          <div className="space-y-4">
            {trends.categoryPerformance.map((category, index) => (
              <div key={category.category} className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium text-gray-900 capitalize">
                      {category.category}
                    </span>
                    <span className="text-sm text-gray-500">
                      {category.jobs} jobs
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        category.successRate > 0.9 ? 'bg-green-500' :
                        category.successRate > 0.7 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${category.successRate * 100}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>Success: {(category.successRate * 100).toFixed(1)}%</span>
                    <span>Confidence: {(category.avgConfidence * 100).toFixed(1)}%</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Top Products and Recent Failures */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Performing Products */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Performing Products</h3>
          <div className="space-y-4">
            {topProducts.slice(0, 5).map((product, index) => (
              <div key={product.id} className="flex items-center space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-warm-100 rounded-full flex items-center justify-center text-sm font-medium text-warm-600">
                  {index + 1}
                </div>
                <div className="flex-shrink-0">
                  <img
                    src={product.heroImage}
                    alt={product.name}
                    className="w-12 h-12 rounded-lg object-cover"
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900 truncate">
                    {product.name}
                  </div>
                  <div className="text-sm text-gray-500">
                    {product.category} • {product.totalJobs} jobs
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {(product.successRate * 100).toFixed(1)}%
                  </div>
                  <div className="text-xs text-gray-500">
                    {(product.avgConfidence * 100).toFixed(1)}% conf
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Failures */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Failures</h3>
          <div className="space-y-4">
            {recentFailures.slice(0, 5).map((failure, index) => (
              <div key={failure.id} className="border-l-4 border-red-400 pl-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-900">
                      {failure.productName}
                    </div>
                    <div className="text-sm text-red-600 mt-1">
                      {failure.errorMessage}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {new Date(failure.createdAt).toLocaleString()}
                      {failure.retryCount > 0 && ` • ${failure.retryCount} retries`}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* User Engagement Chart */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">User Engagement</h3>
        <div className="h-64 flex items-end justify-between space-x-1">
          {trends.userEngagement.map((day, index) => {
            const maxUsers = Math.max(...trends.userEngagement.map(d => d.uniqueUsers + d.repeatUsers));
            const totalHeight = maxUsers > 0 ? ((day.uniqueUsers + day.repeatUsers) / maxUsers) * 100 : 0;
            const uniqueHeight = maxUsers > 0 ? (day.uniqueUsers / maxUsers) * 100 : 0;
            
            return (
              <div key={day.date} className="flex-1 flex flex-col items-center">
                <div className="w-full flex flex-col justify-end h-48">
                  <div
                    className="w-full bg-blue-500 rounded-t"
                    style={{ height: `${uniqueHeight}%` }}
                    title={`New Users: ${day.uniqueUsers}`}
                  ></div>
                  <div
                    className="w-full bg-blue-300"
                    style={{ height: `${totalHeight - uniqueHeight}%` }}
                    title={`Returning Users: ${day.repeatUsers}`}
                  ></div>
                </div>
                <div className="text-xs text-gray-500 mt-2 transform -rotate-45">
                  {new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                </div>
              </div>
            );
          })}
        </div>
        <div className="flex justify-center space-x-4 mt-4 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-500 rounded"></div>
            <span>New Users</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-300 rounded"></div>
            <span>Returning Users</span>
          </div>
        </div>
      </div>
    </div>
  );
}
