'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { Button } from '@/components/ui';

interface QueueItem {
  id: string;
  status: 'PENDING' | 'CLAIMED' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  priority: number;
  assignedTo?: string;
  colabNotebookUrl?: string;
  processingNotes?: string;
  estimatedCompletion?: string;
  originalError?: string;
  retryCount: number;
  lastRetryAt?: string;
  createdAt: string;
  updatedAt: string;
  aiTryOnJob: {
    id: string;
    userPhotoUrl: string;
    resultImageUrl?: string;
    confidence?: number;
    user: {
      id: string;
      name?: string;
      email: string;
      image?: string;
    };
    customization: {
      id: string;
      name: string;
      previewImage?: string;
      product: {
        id: string;
        name: string;
        category: string;
        heroImage: string;
      };
    };
  };
}

interface QueueStats {
  total: number;
  pending: number;
  claimed: number;
  processing: number;
  completed: number;
  failed: number;
  cancelled: number;
}

interface AiTryOnFallbackQueueTabProps {
  onRefresh: () => void;
}

export function AiTryOnFallbackQueueTab({ onRefresh }: AiTryOnFallbackQueueTabProps) {
  const [queueItems, setQueueItems] = useState<QueueItem[]>([]);
  const [stats, setStats] = useState<QueueStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    assignedTo: '',
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });
  const [selectedItem, setSelectedItem] = useState<QueueItem | null>(null);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    fetchQueueItems();
  }, [filters, pagination.page]);

  const fetchQueueItems = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(filters.status && { status: filters.status }),
        ...(filters.priority && { priority: filters.priority }),
        ...(filters.assignedTo && { assignedTo: filters.assignedTo }),
      });

      const response = await fetch(`/api/admin/ai-tryon/fallback-queue?${params}`);
      const data = await response.json();

      if (data.success) {
        setQueueItems(data.data.queueItems);
        setStats(data.data.stats);
        setPagination(prev => ({
          ...prev,
          total: data.data.pagination.total,
          totalPages: data.data.pagination.totalPages,
        }));
      }
    } catch (error) {
      console.error('Failed to fetch queue items:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClaimJob = async (queueId: string) => {
    try {
      const response = await fetch('/api/admin/ai-tryon/fallback-queue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          queueId,
          estimatedCompletion: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
        }),
      });

      if (response.ok) {
        fetchQueueItems();
        onRefresh();
      }
    } catch (error) {
      console.error('Failed to claim job:', error);
    }
  };

  const handleUpdateStatus = async (queueId: string, status: string, notes?: string) => {
    try {
      const response = await fetch('/api/admin/ai-tryon/fallback-queue', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          queueId,
          status,
          ...(notes && { processingNotes: notes }),
        }),
      });

      if (response.ok) {
        fetchQueueItems();
        onRefresh();
      }
    } catch (error) {
      console.error('Failed to update status:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800';
      case 'CLAIMED': return 'bg-blue-100 text-blue-800';
      case 'PROCESSING': return 'bg-purple-100 text-purple-800';
      case 'COMPLETED': return 'bg-green-100 text-green-800';
      case 'FAILED': return 'bg-red-100 text-red-800';
      case 'CANCELLED': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: number) => {
    switch (priority) {
      case 1: return 'bg-red-100 text-red-800';
      case 2: return 'bg-orange-100 text-orange-800';
      case 3: return 'bg-yellow-100 text-yellow-800';
      case 4: return 'bg-blue-100 text-blue-800';
      case 5: return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  return (
    <div className="space-y-6">
      {/* Queue Statistics */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
          {[
            { label: 'Total', value: stats.total, color: 'bg-gray-500' },
            { label: 'Pending', value: stats.pending, color: 'bg-yellow-500' },
            { label: 'Claimed', value: stats.claimed, color: 'bg-blue-500' },
            { label: 'Processing', value: stats.processing, color: 'bg-purple-500' },
            { label: 'Completed', value: stats.completed, color: 'bg-green-500' },
            { label: 'Failed', value: stats.failed, color: 'bg-red-500' },
            { label: 'Cancelled', value: stats.cancelled, color: 'bg-gray-400' },
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-lg p-4 shadow-sm border border-gray-200"
            >
              <div className="flex items-center space-x-3">
                <div className={`w-3 h-3 rounded-full ${stat.color}`}></div>
                <div>
                  <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                  <div className="text-sm text-gray-500">{stat.label}</div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-warm-500"
            >
              <option value="">All Statuses</option>
              <option value="PENDING">Pending</option>
              <option value="CLAIMED">Claimed</option>
              <option value="PROCESSING">Processing</option>
              <option value="COMPLETED">Completed</option>
              <option value="FAILED">Failed</option>
              <option value="CANCELLED">Cancelled</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
            <select
              value={filters.priority}
              onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-warm-500"
            >
              <option value="">All Priorities</option>
              <option value="1">Critical (1)</option>
              <option value="2">High (2)</option>
              <option value="3">Medium (3)</option>
              <option value="4">Low (4)</option>
              <option value="5">Lowest (5)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Assigned To</label>
            <select
              value={filters.assignedTo}
              onChange={(e) => setFilters(prev => ({ ...prev, assignedTo: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-warm-500"
            >
              <option value="">All Assignees</option>
              <option value="me">My Jobs</option>
              <option value="unassigned">Unassigned</option>
            </select>
          </div>

          <div className="flex items-end">
            <Button
              onClick={() => setPagination(prev => ({ ...prev, page: 1 }))}
              className="w-full"
            >
              🔍 Filter
            </Button>
          </div>
        </div>
      </div>

      {/* Queue Items */}
      <div className="space-y-4">
        <AnimatePresence>
          {queueItems.map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.05 }}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <div className="flex items-start justify-between">
                <div className="flex space-x-4">
                  {/* User Photo */}
                  <div className="flex-shrink-0">
                    <Image
                      src={item.aiTryOnJob.userPhotoUrl}
                      alt="User photo"
                      width={64}
                      height={64}
                      className="h-16 w-16 rounded-lg object-cover"
                    />
                  </div>

                  {/* Product Info */}
                  <div className="flex-shrink-0">
                    <Image
                      src={item.aiTryOnJob.customization.product.heroImage}
                      alt={item.aiTryOnJob.customization.product.name}
                      width={64}
                      height={64}
                      className="h-16 w-16 rounded-lg object-cover"
                    />
                  </div>

                  {/* Details */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(item.status)}`}>
                        {item.status}
                      </span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(item.priority)}`}>
                        Priority {item.priority}
                      </span>
                    </div>

                    <h3 className="text-lg font-medium text-gray-900 mb-1">
                      {item.aiTryOnJob.customization.product.name} - {item.aiTryOnJob.customization.name}
                    </h3>
                    
                    <div className="text-sm text-gray-500 space-y-1">
                      <div>User: {item.aiTryOnJob.user.name || item.aiTryOnJob.user.email}</div>
                      <div>Created: {formatTimeAgo(item.createdAt)}</div>
                      {item.retryCount > 0 && (
                        <div>Retries: {item.retryCount}</div>
                      )}
                      {item.originalError && (
                        <div className="text-red-600">Error: {item.originalError}</div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex space-x-2">
                  {item.status === 'PENDING' && (
                    <Button
                      size="sm"
                      onClick={() => handleClaimJob(item.id)}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      🎯 Claim
                    </Button>
                  )}
                  
                  {(item.status === 'CLAIMED' || item.status === 'PROCESSING') && (
                    <>
                      <Button
                        size="sm"
                        onClick={() => handleUpdateStatus(item.id, 'PROCESSING')}
                        className="bg-purple-600 hover:bg-purple-700"
                      >
                        🔄 Start Processing
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setSelectedItem(item);
                          setShowModal(true);
                        }}
                      >
                        📝 Complete
                      </Button>
                    </>
                  )}
                  
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setSelectedItem(item);
                      setShowModal(true);
                    }}
                  >
                    👁️ View Details
                  </Button>
                </div>
              </div>

              {/* Processing Notes */}
              {item.processingNotes && (
                <div className="mt-4 p-3 bg-gray-50 rounded-md">
                  <div className="text-sm font-medium text-gray-700 mb-1">Processing Notes:</div>
                  <div className="text-sm text-gray-600">{item.processingNotes}</div>
                </div>
              )}

              {/* Colab Notebook Link */}
              {item.colabNotebookUrl && (
                <div className="mt-4">
                  <a
                    href={item.colabNotebookUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center space-x-2 text-sm text-blue-600 hover:text-blue-700"
                  >
                    <span>📓</span>
                    <span>Open Colab Notebook</span>
                    <span>↗️</span>
                  </a>
                </div>
              )}
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
              {pagination.total} results
            </div>
            <div className="flex space-x-2">
              <Button
                size="sm"
                variant="outline"
                disabled={pagination.page === 1}
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
              >
                Previous
              </Button>
              <Button
                size="sm"
                variant="outline"
                disabled={pagination.page === pagination.totalPages}
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
              >
                Next
              </Button>
            </div>
          </div>
        </div>
      )}

      {loading && (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-warm-600"></div>
        </div>
      )}

      {/* Detail Modal */}
      {showModal && selectedItem && (
        <QueueItemDetailModal
          item={selectedItem}
          onClose={() => {
            setShowModal(false);
            setSelectedItem(null);
          }}
          onUpdate={() => {
            fetchQueueItems();
            onRefresh();
          }}
        />
      )}
    </div>
  );
}

// Queue Item Detail Modal Component (placeholder)
function QueueItemDetailModal({ 
  item, 
  onClose, 
  onUpdate 
}: { 
  item: QueueItem; 
  onClose: () => void; 
  onUpdate: () => void; 
}) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Queue Item Details</h2>
          <Button variant="outline" onClick={onClose}>✕</Button>
        </div>
        
        {/* Modal content would go here */}
        <div className="space-y-4">
          <div>
            <strong>Status:</strong> {item.status}
          </div>
          <div>
            <strong>Priority:</strong> {item.priority}
          </div>
          <div>
            <strong>Product:</strong> {item.aiTryOnJob.customization.product.name}
          </div>
          <div>
            <strong>User:</strong> {item.aiTryOnJob.user.name || item.aiTryOnJob.user.email}
          </div>
          {item.originalError && (
            <div>
              <strong>Error:</strong> {item.originalError}
            </div>
          )}
        </div>
        
        <div className="mt-6 flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>Close</Button>
          <Button onClick={() => { onUpdate(); onClose(); }}>Complete Job</Button>
        </div>
      </div>
    </div>
  );
}
