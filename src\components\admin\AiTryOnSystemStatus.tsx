'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui';

interface SystemStats {
  totalProducts: number;
  aiTryOnEnabledProducts: number;
  totalJobs: number;
  successRate: number;
  queueLength: number;
  averageProcessingTime: number;
  recentFailures: number;
  systemHealth: 'healthy' | 'warning' | 'critical';
  lastUpdated: string;
}

interface AiTryOnSystemStatusProps {
  stats: SystemStats;
  onRefresh: () => void;
  className?: string;
}

export function AiTryOnSystemStatus({ stats, onRefresh, className = '' }: AiTryOnSystemStatusProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await onRefresh();
    setTimeout(() => setIsRefreshing(false), 1000);
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getHealthIcon = (health: string) => {
    switch (health) {
      case 'healthy': return '✅';
      case 'warning': return '⚠️';
      case 'critical': return '🚨';
      default: return '❓';
    }
  };

  const formatProcessingTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    return `${Math.round(seconds / 3600)}h`;
  };

  const statusCards = [
    {
      title: 'System Health',
      value: stats.systemHealth,
      icon: getHealthIcon(stats.systemHealth),
      color: getHealthColor(stats.systemHealth),
      description: 'Overall system status'
    },
    {
      title: 'Success Rate',
      value: `${(stats.successRate * 100).toFixed(1)}%`,
      icon: '📈',
      color: stats.successRate > 0.9 ? 'text-green-600 bg-green-100' : 
             stats.successRate > 0.7 ? 'text-yellow-600 bg-yellow-100' : 
             'text-red-600 bg-red-100',
      description: 'AI try-on success rate'
    },
    {
      title: 'Queue Length',
      value: stats.queueLength.toString(),
      icon: '⏳',
      color: stats.queueLength === 0 ? 'text-green-600 bg-green-100' :
             stats.queueLength < 10 ? 'text-yellow-600 bg-yellow-100' :
             'text-red-600 bg-red-100',
      description: 'Jobs waiting for manual processing'
    },
    {
      title: 'Avg Processing Time',
      value: formatProcessingTime(stats.averageProcessingTime),
      icon: '⚡',
      color: stats.averageProcessingTime < 30 ? 'text-green-600 bg-green-100' :
             stats.averageProcessingTime < 60 ? 'text-yellow-600 bg-yellow-100' :
             'text-red-600 bg-red-100',
      description: 'Average time to complete'
    },
    {
      title: 'Enabled Products',
      value: `${stats.aiTryOnEnabledProducts}/${stats.totalProducts}`,
      icon: '🎨',
      color: 'text-blue-600 bg-blue-100',
      description: 'Products with AI try-on enabled'
    },
    {
      title: 'Recent Failures',
      value: stats.recentFailures.toString(),
      icon: '❌',
      color: stats.recentFailures === 0 ? 'text-green-600 bg-green-100' :
             stats.recentFailures < 5 ? 'text-yellow-600 bg-yellow-100' :
             'text-red-600 bg-red-100',
      description: 'Failures in last 24 hours'
    }
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">System Status</h2>
          <p className="text-sm text-gray-500">
            Last updated: {new Date(stats.lastUpdated).toLocaleString()}
          </p>
        </div>
        
        <Button
          onClick={handleRefresh}
          variant="outline"
          size="sm"
          disabled={isRefreshing}
          className="flex items-center space-x-2"
        >
          <span className={isRefreshing ? 'animate-spin' : ''}>🔄</span>
          <span>{isRefreshing ? 'Refreshing...' : 'Refresh'}</span>
        </Button>
      </div>

      {/* Status Cards Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {statusCards.map((card, index) => (
          <motion.div
            key={card.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-4"
          >
            <div className="flex items-center justify-between mb-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm ${card.color}`}>
                {card.icon}
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">{card.value}</div>
              </div>
            </div>
            
            <div>
              <div className="text-sm font-medium text-gray-900 mb-1">{card.title}</div>
              <div className="text-xs text-gray-500">{card.description}</div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* System Alerts */}
      {(stats.systemHealth === 'warning' || stats.systemHealth === 'critical') && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className={`
            rounded-lg p-4 border-l-4
            ${stats.systemHealth === 'critical' 
              ? 'bg-red-50 border-red-400 text-red-700' 
              : 'bg-yellow-50 border-yellow-400 text-yellow-700'
            }
          `}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <span className="text-xl">
                {stats.systemHealth === 'critical' ? '🚨' : '⚠️'}
              </span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium">
                {stats.systemHealth === 'critical' ? 'Critical System Alert' : 'System Warning'}
              </h3>
              <div className="mt-2 text-sm">
                <ul className="list-disc list-inside space-y-1">
                  {stats.successRate < 0.7 && (
                    <li>Low success rate detected ({(stats.successRate * 100).toFixed(1)}%)</li>
                  )}
                  {stats.queueLength > 10 && (
                    <li>High queue length ({stats.queueLength} jobs pending)</li>
                  )}
                  {stats.recentFailures > 5 && (
                    <li>High failure rate ({stats.recentFailures} failures in 24h)</li>
                  )}
                  {stats.averageProcessingTime > 120 && (
                    <li>Slow processing times ({formatProcessingTime(stats.averageProcessingTime)} avg)</li>
                  )}
                </ul>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <h3 className="text-sm font-medium text-gray-900 mb-3">Quick Actions</h3>
        <div className="flex flex-wrap gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => window.location.href = '/admin/ai-tryon?tab=queue'}
            className="flex items-center space-x-1"
          >
            <span>⚡</span>
            <span>View Queue</span>
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={() => window.location.href = '/admin/ai-tryon?tab=analytics'}
            className="flex items-center space-x-1"
          >
            <span>📊</span>
            <span>Analytics</span>
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={() => window.location.href = '/admin/ai-tryon?tab=settings'}
            className="flex items-center space-x-1"
          >
            <span>⚙️</span>
            <span>Settings</span>
          </Button>
          
          {stats.systemHealth === 'critical' && (
            <Button
              size="sm"
              variant="default"
              onClick={() => {
                if (confirm('This will disable AI try-on for all products. Continue?')) {
                  // Handle emergency disable
                }
              }}
              className="flex items-center space-x-1 bg-red-600 hover:bg-red-700"
            >
              <span>🚨</span>
              <span>Emergency Disable</span>
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
