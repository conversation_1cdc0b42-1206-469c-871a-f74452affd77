'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input, Textarea } from '@/components/ui/Input';
import { PricingRule, PricingCondition } from '@/types/pricing';
import { CONDITION_OPERATORS, RULE_FIELDS, PricingValidator } from '@/lib/pricing/utils';

interface PricingRuleBuilderProps {
  rule?: Partial<PricingRule>;
  onSave: (rule: Partial<PricingRule>) => void;
  onCancel: () => void;
}

export function PricingRuleBuilder({ rule, onSave, onCancel }: PricingRuleBuilderProps) {
  const [formData, setFormData] = useState<Partial<PricingRule>>({
    name: '',
    description: '',
    type: 'fabric_surcharge',
    conditions: [{ field: 'fabricType', operator: 'equals', value: '' }],
    modifier: 0,
    modifierType: 'fixed',
    customerMessage: '',
    valueFraming: '',
    priority: 1,
    isActive: true,
    ...rule
  });

  const [errors, setErrors] = useState<string[]>([]);
  const [previewMode, setPreviewMode] = useState(false);

  const handleInputChange = (field: keyof PricingRule, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear errors when user starts typing
    if (errors.length > 0) {
      setErrors([]);
    }
  };

  const handleConditionChange = (index: number, field: keyof PricingCondition, value: any) => {
    const newConditions = [...(formData.conditions || [])];
    newConditions[index] = { ...newConditions[index], [field]: value };
    setFormData(prev => ({ ...prev, conditions: newConditions }));
  };

  const addCondition = () => {
    const newConditions = [...(formData.conditions || [])];
    newConditions.push({ field: 'fabricType', operator: 'equals', value: '' });
    setFormData(prev => ({ ...prev, conditions: newConditions }));
  };

  const removeCondition = (index: number) => {
    const newConditions = formData.conditions?.filter((_, i) => i !== index) || [];
    setFormData(prev => ({ ...prev, conditions: newConditions }));
  };

  const handleSave = () => {
    const validation = PricingValidator.validateRule(formData);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    onSave(formData);
  };

  const generatePreviewMessage = () => {
    if (!formData.customerMessage || !formData.valueFraming) return '';
    
    const modifier = formData.modifierType === 'percentage' 
      ? `${formData.modifier}%` 
      : `$${formData.modifier}`;
    
    return `${formData.customerMessage} (${modifier}) - ${formData.valueFraming}`;
  };

  return (
    <div className="space-y-6">
      {/* Rule Basic Info */}
      <Card>
        <CardHeader>
          <CardTitle>Rule Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Input
            label="Rule Name"
            value={formData.name || ''}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="e.g., Premium Fabric Surcharge"
          />

          <Textarea
            label="Description"
            value={formData.description || ''}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Describe what this rule does and when it applies"
            rows={3}
          />

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Rule Type</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                value={formData.type || 'fabric_surcharge'}
                onChange={(e) => handleInputChange('type', e.target.value)}
              >
                <option value="fabric_surcharge">Fabric Surcharge</option>
                <option value="print_size_cost">Print Size Cost</option>
                <option value="quality_tier">Quality Tier</option>
                <option value="quantity_discount">Quantity Discount</option>
                <option value="seasonal_promotion">Seasonal Promotion</option>
                <option value="loyalty_discount">Loyalty Discount</option>
                <option value="first_time_discount">First Time Discount</option>
                <option value="complexity_surcharge">Complexity Surcharge</option>
              </select>
            </div>

            <Input
              label="Priority (0-100)"
              type="number"
              value={formData.priority || 1}
              onChange={(e) => handleInputChange('priority', parseInt(e.target.value))}
              min="0"
              max="100"
            />
          </div>
        </CardContent>
      </Card>

      {/* Rule Conditions */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Conditions</CardTitle>
            <Button variant="secondary" size="sm" onClick={addCondition}>
              + Add Condition
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {formData.conditions?.map((condition, index) => (
            <div key={index} className="p-4 border border-gray-200 rounded-lg">
              <div className="flex justify-between items-start mb-4">
                <h4 className="font-medium">Condition {index + 1}</h4>
                {formData.conditions!.length > 1 && (
                  <button
                    onClick={() => removeCondition(index)}
                    className="text-red-500 hover:text-red-700 text-sm"
                  >
                    Remove
                  </button>
                )}
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Field</label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    value={condition.field}
                    onChange={(e) => handleConditionChange(index, 'field', e.target.value)}
                  >
                    {RULE_FIELDS.map(field => (
                      <option key={field.value} value={field.value}>
                        {field.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Operator</label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    value={condition.operator}
                    onChange={(e) => handleConditionChange(index, 'operator', e.target.value)}
                  >
                    {CONDITION_OPERATORS.map(op => (
                      <option key={op.value} value={op.value}>
                        {op.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Value</label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    value={condition.value}
                    onChange={(e) => handleConditionChange(index, 'value', e.target.value)}
                    placeholder="Enter value"
                  />
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Price Modification */}
      <Card>
        <CardHeader>
          <CardTitle>Price Modification</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Input
              label="Modifier Amount"
              type="number"
              value={formData.modifier || 0}
              onChange={(e) => handleInputChange('modifier', parseFloat(e.target.value))}
              step="0.01"
            />

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Modifier Type</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                value={formData.modifierType || 'fixed'}
                onChange={(e) => handleInputChange('modifierType', e.target.value)}
              >
                <option value="fixed">Fixed Amount ($)</option>
                <option value="percentage">Percentage (%)</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Customer Messaging */}
      <Card>
        <CardHeader>
          <CardTitle>Customer Communication</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Input
            label="Customer Message"
            value={formData.customerMessage || ''}
            onChange={(e) => handleInputChange('customerMessage', e.target.value)}
            placeholder="e.g., Premium fabric upgrade"
          />

          <Textarea
            label="Value Framing"
            value={formData.valueFraming || ''}
            onChange={(e) => handleInputChange('valueFraming', e.target.value)}
            placeholder="e.g., Luxurious feel and enhanced durability"
            rows={2}
          />

          {/* Preview */}
          <div className="p-3 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Customer Preview:</h4>
            <p className="text-blue-800 text-sm">
              {generatePreviewMessage() || 'Enter customer message and value framing to see preview'}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Errors */}
      {errors.length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardContent>
            <h4 className="font-medium text-red-900 mb-2">Please fix the following errors:</h4>
            <ul className="list-disc list-inside space-y-1 text-red-800 text-sm">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Actions */}
      <div className="flex justify-end space-x-4">
        <Button variant="secondary" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSave}>
          {rule?.id ? 'Update Rule' : 'Create Rule'}
        </Button>
      </div>
    </div>
  );
}
