'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui';

interface CleanupStats {
  overview: {
    totalJobs: number;
    activeJobs: number;
    deletedJobs: number;
    expiredJobs: number;
    deletionRate: number;
  };
  recent: {
    deletionsLast24h: number;
    privacyRequestsLast7d: number;
  };
  users: {
    totalUsers: number;
    usersWithConsent: number;
    consentRate: number;
  };
  retentionPolicies: Array<{
    days: number;
    userCount: number;
  }>;
  recentRequests: Array<{
    id: string;
    type: string;
    status: string;
    createdAt: string;
    user: {
      email: string;
    };
  }>;
}

interface CleanupResult {
  deletedJobs: number;
  deletedFiles: number;
  errors: string[];
  processedAt: string;
}

export const PrivacyManagementTab: React.FC = () => {
  const [stats, setStats] = useState<CleanupStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [cleanupLoading, setCleanupLoading] = useState(false);
  const [lastCleanup, setLastCleanup] = useState<CleanupResult | null>(null);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/privacy/cleanup');
      if (response.ok) {
        const data = await response.json();
        setStats(data.data);
      }
    } catch (error) {
      console.error('Error fetching privacy stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const runCleanup = async (type: 'automatic' | 'manual') => {
    try {
      setCleanupLoading(true);
      const response = await fetch('/api/admin/privacy/cleanup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type }),
      });

      if (response.ok) {
        const data = await response.json();
        setLastCleanup(data.data);
        await fetchStats();
      }
    } catch (error) {
      console.error('Error running cleanup:', error);
    } finally {
      setCleanupLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-warm-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg p-6 border border-gray-200">
          <div className="text-2xl font-bold text-blue-600 mb-2">
            {stats?.overview.totalJobs || 0}
          </div>
          <div className="text-sm text-gray-600">Total AI Try-On Jobs</div>
        </div>

        <div className="bg-white rounded-lg p-6 border border-gray-200">
          <div className="text-2xl font-bold text-green-600 mb-2">
            {stats?.overview.activeJobs || 0}
          </div>
          <div className="text-sm text-gray-600">Active Jobs</div>
        </div>

        <div className="bg-white rounded-lg p-6 border border-gray-200">
          <div className="text-2xl font-bold text-red-600 mb-2">
            {stats?.overview.expiredJobs || 0}
          </div>
          <div className="text-sm text-gray-600">Expired Jobs</div>
        </div>

        <div className="bg-white rounded-lg p-6 border border-gray-200">
          <div className="text-2xl font-bold text-purple-600 mb-2">
            {stats?.overview.deletionRate.toFixed(1) || 0}%
          </div>
          <div className="text-sm text-gray-600">Deletion Rate</div>
        </div>
      </div>

      {/* User Consent Stats */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">User Consent Overview</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-warm-600 mb-2">
              {stats?.users.totalUsers || 0}
            </div>
            <div className="text-sm text-gray-600">Total Users</div>
          </div>
          
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">
              {stats?.users.usersWithConsent || 0}
            </div>
            <div className="text-sm text-gray-600">Users with Consent</div>
          </div>
          
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {stats?.users.consentRate.toFixed(1) || 0}%
            </div>
            <div className="text-sm text-gray-600">Consent Rate</div>
          </div>
        </div>
      </div>

      {/* Data Retention Policies */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Data Retention Preferences</h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {stats?.retentionPolicies.map((policy) => (
            <div key={policy.days} className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {policy.userCount}
              </div>
              <div className="text-sm text-gray-600">
                {policy.days} days
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Cleanup Controls */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Privacy Cleanup</h3>
          <div className="flex space-x-3">
            <Button
              onClick={() => runCleanup('manual')}
              disabled={cleanupLoading}
              className="bg-warm-600 hover:bg-warm-700"
            >
              {cleanupLoading ? 'Running...' : 'Run Manual Cleanup'}
            </Button>
            <Button
              onClick={fetchStats}
              variant="outline"
            >
              Refresh Stats
            </Button>
          </div>
        </div>

        {lastCleanup && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
            <h4 className="font-medium text-green-800 mb-2">Last Cleanup Result</h4>
            <div className="text-sm text-green-700">
              <p>Processed at: {formatDate(lastCleanup.processedAt)}</p>
              <p>Deleted jobs: {lastCleanup.deletedJobs}</p>
              <p>Deleted files: {lastCleanup.deletedFiles}</p>
              {lastCleanup.errors.length > 0 && (
                <p className="text-red-700">Errors: {lastCleanup.errors.length}</p>
              )}
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Recent Activity</h4>
            <div className="space-y-2 text-sm text-gray-600">
              <p>Deletions in last 24h: {stats?.recent.deletionsLast24h || 0}</p>
              <p>Privacy requests in last 7d: {stats?.recent.privacyRequestsLast7d || 0}</p>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Cleanup Schedule</h4>
            <div className="text-sm text-gray-600">
              <p>Automatic cleanup runs daily at midnight</p>
              <p>Expired data is deleted based on user retention preferences</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Privacy Requests */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Privacy Requests</h3>
        
        {stats?.recentRequests.length === 0 ? (
          <p className="text-gray-600">No recent privacy requests</p>
        ) : (
          <div className="space-y-3">
            {stats?.recentRequests.map((request) => (
              <div key={request.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="font-medium text-gray-900">
                      {request.type.replace('_', ' ')}
                    </div>
                    <div className="text-sm text-gray-600">
                      {request.user.email} • {formatDate(request.createdAt)}
                    </div>
                  </div>
                  <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                    request.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                    request.status === 'IN_PROGRESS' ? 'bg-yellow-100 text-yellow-800' :
                    request.status === 'PENDING' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {request.status}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
