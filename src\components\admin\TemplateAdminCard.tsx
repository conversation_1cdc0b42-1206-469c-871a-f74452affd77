'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { Button } from '@/components/ui';

interface AdminTemplate {
  id: string;
  name: string;
  description: string;
  moodTag: string;
  styleKeywords: string[];
  targetAudience: string;
  designData: any;
  previewImage: string;
  lifestyleContext: string[];
  usageCount: number;
  isFeatured: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  product: {
    id: string;
    name: string;
    category: string;
    subcategory?: string;
    basePrice: number;
    heroImage: string;
  };
}

interface TemplateAdminCardProps {
  template: AdminTemplate;
  onEdit: () => void;
  onDelete: () => void;
  onToggleActive: () => void;
  onToggleFeatured: () => void;
}

export function TemplateAdminCard({
  template,
  onEdit,
  onDelete,
  onToggleActive,
  onToggleFeatured,
}: TemplateAdminCardProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [showActions, setShowActions] = useState(false);

  // Get status color
  const getStatusColor = () => {
    if (!template.isActive) return 'bg-gray-500';
    if (template.isFeatured) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  // Get status text
  const getStatusText = () => {
    if (!template.isActive) return 'Inactive';
    if (template.isFeatured) return 'Featured';
    return 'Active';
  };

  return (
    <motion.div
      className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300"
      onHoverStart={() => setShowActions(true)}
      onHoverEnd={() => setShowActions(false)}
      whileHover={{ y: -2 }}
    >
      {/* Header */}
      <div className="relative">
        {/* Status Badge */}
        <div className="absolute top-3 left-3 z-10">
          <div className={`${getStatusColor()} text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg`}>
            {getStatusText()}
          </div>
        </div>

        {/* Usage Count */}
        <div className="absolute top-3 right-3 z-10">
          <div className="bg-black/70 text-white text-xs font-medium px-2 py-1 rounded-full backdrop-blur-sm">
            {template.usageCount} uses
          </div>
        </div>

        {/* Preview Image */}
        <div className="relative aspect-square overflow-hidden bg-gray-100">
          <Image
            src={template.previewImage}
            alt={template.name}
            fill
            className={`object-cover transition-all duration-300 ${
              imageLoaded ? 'opacity-100' : 'opacity-0'
            }`}
            onLoad={() => setImageLoaded(true)}
          />
          
          {/* Loading placeholder */}
          {!imageLoaded && (
            <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
              <div className="text-gray-400 text-sm">Loading...</div>
            </div>
          )}

          {/* Quick Actions Overlay */}
          <motion.div
            className="absolute inset-0 bg-black/60 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: showActions ? 1 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <div className="flex space-x-2">
              <Button
                onClick={onEdit}
                size="sm"
                className="bg-white text-gray-900 hover:bg-gray-100"
              >
                ✏️ Edit
              </Button>
              <Button
                onClick={onToggleActive}
                size="sm"
                variant="outline"
                className="bg-white/90 text-gray-900 hover:bg-white"
              >
                {template.isActive ? '⏸️ Deactivate' : '▶️ Activate'}
              </Button>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 space-y-3">
        {/* Title and Mood */}
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="font-bold text-lg text-gray-900 line-clamp-1 mb-1">
              {template.name}
            </h3>
            <div className="inline-block bg-gradient-to-r from-primary-500 to-secondary-500 text-white text-xs font-bold px-2 py-1 rounded-full">
              {template.moodTag}
            </div>
          </div>
        </div>

        {/* Description */}
        <p className="text-gray-600 text-sm line-clamp-2 leading-relaxed">
          {template.description}
        </p>

        {/* Product Info */}
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm font-medium text-gray-900">
                {template.product.name}
              </div>
              <div className="text-xs text-gray-500">
                {template.product.category} • ${template.product.basePrice}
              </div>
            </div>
            <div className="text-xs text-gray-500">
              {template.targetAudience}
            </div>
          </div>
        </div>

        {/* Style Keywords */}
        <div className="flex flex-wrap gap-1">
          {template.styleKeywords.slice(0, 3).map((keyword, index) => (
            <span
              key={index}
              className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full"
            >
              {keyword}
            </span>
          ))}
          {template.styleKeywords.length > 3 && (
            <span className="text-xs text-gray-500">
              +{template.styleKeywords.length - 3}
            </span>
          )}
        </div>

        {/* Lifestyle Context */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center space-x-1">
            <span>Perfect for:</span>
            <span className="font-medium">
              {template.lifestyleContext.slice(0, 2).join(', ')}
              {template.lifestyleContext.length > 2 && ` +${template.lifestyleContext.length - 2}`}
            </span>
          </div>
          <div>
            {new Date(template.createdAt).toLocaleDateString()}
          </div>
        </div>

        {/* Actions */}
        <div className="pt-3 border-t border-gray-100 flex space-x-2">
          <Button
            onClick={onEdit}
            size="sm"
            variant="outline"
            className="flex-1 text-xs"
          >
            ✏️ Edit
          </Button>
          
          <Button
            onClick={onToggleFeatured}
            size="sm"
            variant={template.isFeatured ? "default" : "outline"}
            className="text-xs"
          >
            {template.isFeatured ? '⭐' : '☆'}
          </Button>
          
          <Button
            onClick={onToggleActive}
            size="sm"
            variant={template.isActive ? "outline" : "default"}
            className="text-xs"
          >
            {template.isActive ? '⏸️' : '▶️'}
          </Button>
          
          <Button
            onClick={onDelete}
            size="sm"
            variant="outline"
            className="text-xs text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            🗑️
          </Button>
        </div>

        {/* Analytics Preview */}
        <div className="bg-blue-50 rounded-lg p-2">
          <div className="flex items-center justify-between text-xs">
            <span className="text-blue-700 font-medium">Performance</span>
            <div className="flex space-x-3 text-blue-600">
              <span>{template.usageCount} uses</span>
              <span>•</span>
              <span className="capitalize">{template.isActive ? 'active' : 'inactive'}</span>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
