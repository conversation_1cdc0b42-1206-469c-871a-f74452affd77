'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui';

interface AnalyticsOverview {
  totalTemplates: number;
  activeTemplates: number;
  featuredTemplates: number;
  totalUsage: number;
  recentUsage: number;
  averageUsagePerTemplate: number;
}

interface TopTemplate {
  id: string;
  name: string;
  moodTag: string;
  usageCount: number;
  recentUsage: number;
  isFeatured: boolean;
  product: {
    id: string;
    name: string;
    category: string;
  };
}

interface DailyUsage {
  date: string;
  count: number;
}

interface AnalyticsData {
  overview: AnalyticsOverview;
  topTemplates: TopTemplate[];
  trends: {
    dailyUsage: DailyUsage[];
    moodTagUsage: Record<string, number>;
    categoryUsage: Record<string, number>;
  };
  period: {
    days: number;
    startDate: string;
    endDate: string;
  };
}

interface TemplateAnalyticsProps {
  className?: string;
}

export function TemplateAnalytics({ className }: TemplateAnalyticsProps) {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [period, setPeriod] = useState<'7' | '30' | '90'>('30');

  // Fetch analytics data
  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/templates/analytics?period=${period}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch analytics');
      }

      if (data.success) {
        setAnalyticsData(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch analytics');
      }
    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch analytics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [period]);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-16">
        <motion.div
          className="w-12 h-12 border-4 border-primary-200 border-t-primary-500 rounded-full"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
        />
        <span className="ml-3 text-gray-600">Loading analytics...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-16">
        <div className="text-red-600 mb-4">⚠️ {error}</div>
        <Button onClick={fetchAnalytics}>
          Try Again
        </Button>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-16">
        <div className="text-gray-500">No analytics data available</div>
      </div>
    );
  }

  const { overview, topTemplates, trends } = analyticsData;

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Period Selector */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Template Analytics</h2>
        <div className="flex bg-gray-100 rounded-lg p-1">
          {[
            { value: '7', label: '7 Days' },
            { value: '30', label: '30 Days' },
            { value: '90', label: '90 Days' },
          ].map((option) => (
            <button
              key={option.value}
              onClick={() => setPeriod(option.value as any)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                period === option.value
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-2 lg:grid-cols-6 gap-4">
        {[
          { label: 'Total Templates', value: overview.totalTemplates, color: 'bg-blue-500' },
          { label: 'Active', value: overview.activeTemplates, color: 'bg-green-500' },
          { label: 'Featured', value: overview.featuredTemplates, color: 'bg-yellow-500' },
          { label: 'Total Usage', value: overview.totalUsage, color: 'bg-purple-500' },
          { label: 'Recent Usage', value: overview.recentUsage, color: 'bg-indigo-500' },
          { label: 'Avg per Template', value: Math.round(overview.averageUsagePerTemplate), color: 'bg-pink-500' },
        ].map((stat, index) => (
          <motion.div
            key={stat.label}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-lg p-4 shadow-sm border border-gray-200"
          >
            <div className="flex items-center space-x-3">
              <div className={`w-3 h-3 rounded-full ${stat.color}`}></div>
              <div>
                <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Daily Usage Trend */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Daily Usage Trend</h3>
          <div className="space-y-2">
            {trends.dailyUsage.slice(-7).map((day, index) => (
              <div key={day.date} className="flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  {new Date(day.date).toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' })}
                </span>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-primary-500 h-2 rounded-full"
                      style={{
                        width: `${Math.max(5, (day.count / Math.max(...trends.dailyUsage.map(d => d.count))) * 100)}%`
                      }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-8 text-right">{day.count}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Mood Tag Distribution */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Popular Mood Tags</h3>
          <div className="space-y-2">
            {Object.entries(trends.moodTagUsage)
              .sort(([,a], [,b]) => b - a)
              .slice(0, 6)
              .map(([mood, count], index) => (
                <div key={mood} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{mood}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-accent-500 h-2 rounded-full"
                        style={{
                          width: `${Math.max(5, (count / Math.max(...Object.values(trends.moodTagUsage))) * 100)}%`
                        }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900 w-8 text-right">{count}</span>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </div>

      {/* Top Templates */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Performing Templates</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-2 text-sm font-medium text-gray-600">Template</th>
                <th className="text-left py-2 text-sm font-medium text-gray-600">Mood</th>
                <th className="text-left py-2 text-sm font-medium text-gray-600">Product</th>
                <th className="text-right py-2 text-sm font-medium text-gray-600">Total Usage</th>
                <th className="text-right py-2 text-sm font-medium text-gray-600">Recent</th>
                <th className="text-center py-2 text-sm font-medium text-gray-600">Status</th>
              </tr>
            </thead>
            <tbody>
              {topTemplates.slice(0, 10).map((template, index) => (
                <tr key={template.id} className="border-b border-gray-100">
                  <td className="py-3">
                    <div className="font-medium text-gray-900">{template.name}</div>
                  </td>
                  <td className="py-3">
                    <span className="inline-block bg-primary-100 text-primary-700 text-xs font-medium px-2 py-1 rounded-full">
                      {template.moodTag}
                    </span>
                  </td>
                  <td className="py-3">
                    <div className="text-sm text-gray-600">
                      {template.product.name}
                      <div className="text-xs text-gray-500">{template.product.category}</div>
                    </div>
                  </td>
                  <td className="py-3 text-right font-medium text-gray-900">
                    {template.usageCount}
                  </td>
                  <td className="py-3 text-right text-gray-600">
                    {template.recentUsage}
                  </td>
                  <td className="py-3 text-center">
                    {template.isFeatured && (
                      <span className="text-yellow-500">⭐</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
