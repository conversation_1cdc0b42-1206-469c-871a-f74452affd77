'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button, Input } from '@/components/ui';

interface TemplateCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function TemplateCreateModal({ isOpen, onClose, onSuccess }: TemplateCreateModalProps) {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    moodTag: '',
    styleKeywords: '',
    targetAudience: '',
    previewImage: '',
    lifestyleContext: '',
    productId: '',
    isFeatured: false,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Basic validation
      if (!formData.name || !formData.description || !formData.moodTag) {
        alert('Please fill in all required fields');
        return;
      }

      const templateData = {
        ...formData,
        styleKeywords: formData.styleKeywords.split(',').map(k => k.trim()).filter(k => k),
        lifestyleContext: formData.lifestyleContext.split(',').map(c => c.trim()).filter(c => c),
        designData: {
          width: 400,
          height: 400,
          backgroundColor: '#ffffff',
          elements: [
            {
              id: 'text-1',
              type: 'text',
              x: 50,
              y: 150,
              width: 300,
              height: 100,
              rotation: 0,
              opacity: 1,
              visible: true,
              locked: false,
              data: {
                text: formData.name.toUpperCase(),
                fontSize: 32,
                fontFamily: 'Inter',
                fontWeight: 'bold',
                fontStyle: 'normal',
                color: '#000000',
                align: 'center',
              }
            }
          ],
          layers: ['text-1']
        },
      };

      const response = await fetch('/api/templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(templateData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create template');
      }

      onSuccess();
      setFormData({
        name: '',
        description: '',
        moodTag: '',
        styleKeywords: '',
        targetAudience: '',
        previewImage: '',
        lifestyleContext: '',
        productId: '',
        isFeatured: false,
      });
    } catch (error) {
      console.error('Error creating template:', error);
      alert('Failed to create template. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white rounded-xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900">Create New Template</h2>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 text-2xl"
              >
                ×
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Template Name *
                  </label>
                  <Input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="e.g., Bold Statement"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Mood Tag *
                  </label>
                  <Input
                    type="text"
                    value={formData.moodTag}
                    onChange={(e) => handleInputChange('moodTag', e.target.value)}
                    placeholder="e.g., Bold Streetwear"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Describe the template and its emotional appeal..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  rows={3}
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Target Audience
                  </label>
                  <Input
                    type="text"
                    value={formData.targetAudience}
                    onChange={(e) => handleInputChange('targetAudience', e.target.value)}
                    placeholder="e.g., young professionals"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Product ID
                  </label>
                  <Input
                    type="text"
                    value={formData.productId}
                    onChange={(e) => handleInputChange('productId', e.target.value)}
                    placeholder="Product ID to associate with"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Style Keywords
                </label>
                <Input
                  type="text"
                  value={formData.styleKeywords}
                  onChange={(e) => handleInputChange('styleKeywords', e.target.value)}
                  placeholder="bold, modern, impactful (comma-separated)"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Lifestyle Context
                </label>
                <Input
                  type="text"
                  value={formData.lifestyleContext}
                  onChange={(e) => handleInputChange('lifestyleContext', e.target.value)}
                  placeholder="work, weekend, casual (comma-separated)"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Preview Image URL
                </label>
                <Input
                  type="url"
                  value={formData.previewImage}
                  onChange={(e) => handleInputChange('previewImage', e.target.value)}
                  placeholder="https://example.com/preview.jpg"
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="featured"
                  checked={formData.isFeatured}
                  onChange={(e) => handleInputChange('isFeatured', e.target.checked)}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <label htmlFor="featured" className="text-sm font-medium text-gray-700">
                  Mark as Featured Template
                </label>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={loading}
                >
                  {loading ? 'Creating...' : 'Create Template'}
                </Button>
              </div>
            </form>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
