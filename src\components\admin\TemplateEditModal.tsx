'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button, Input } from '@/components/ui';

interface AdminTemplate {
  id: string;
  name: string;
  description: string;
  moodTag: string;
  styleKeywords: string[];
  targetAudience: string;
  designData: any;
  previewImage: string;
  lifestyleContext: string[];
  usageCount: number;
  isFeatured: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  product: {
    id: string;
    name: string;
    category: string;
    subcategory?: string;
    basePrice: number;
    heroImage: string;
  };
}

interface TemplateEditModalProps {
  template: AdminTemplate | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function TemplateEditModal({ template, isOpen, onClose, onSuccess }: TemplateEditModalProps) {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    moodTag: '',
    styleKeywords: '',
    targetAudience: '',
    previewImage: '',
    lifestyleContext: '',
    isFeatured: false,
    isActive: true,
  });

  // Update form data when template changes
  useEffect(() => {
    if (template) {
      setFormData({
        name: template.name,
        description: template.description,
        moodTag: template.moodTag,
        styleKeywords: template.styleKeywords.join(', '),
        targetAudience: template.targetAudience,
        previewImage: template.previewImage,
        lifestyleContext: template.lifestyleContext.join(', '),
        isFeatured: template.isFeatured,
        isActive: template.isActive,
      });
    }
  }, [template]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!template) return;

    setLoading(true);

    try {
      // Basic validation
      if (!formData.name || !formData.description || !formData.moodTag) {
        alert('Please fill in all required fields');
        return;
      }

      const updateData = {
        name: formData.name,
        description: formData.description,
        moodTag: formData.moodTag,
        styleKeywords: formData.styleKeywords.split(',').map(k => k.trim()).filter(k => k),
        targetAudience: formData.targetAudience,
        previewImage: formData.previewImage,
        lifestyleContext: formData.lifestyleContext.split(',').map(c => c.trim()).filter(c => c),
        isFeatured: formData.isFeatured,
        isActive: formData.isActive,
      };

      const response = await fetch(`/api/templates/${template.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update template');
      }

      onSuccess();
    } catch (error) {
      console.error('Error updating template:', error);
      alert('Failed to update template. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  if (!template) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white rounded-xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900">Edit Template</h2>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 text-2xl"
              >
                ×
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Template Info */}
              <div className="bg-gray-50 rounded-lg p-3 mb-4">
                <div className="text-sm text-gray-600">
                  Template ID: <span className="font-mono">{template.id}</span>
                </div>
                <div className="text-sm text-gray-600">
                  Usage Count: <span className="font-semibold">{template.usageCount}</span>
                </div>
                <div className="text-sm text-gray-600">
                  Created: {new Date(template.createdAt).toLocaleDateString()}
                </div>
              </div>

              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Template Name *
                  </label>
                  <Input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Mood Tag *
                  </label>
                  <Input
                    type="text"
                    value={formData.moodTag}
                    onChange={(e) => handleInputChange('moodTag', e.target.value)}
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  rows={3}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Target Audience
                </label>
                <Input
                  type="text"
                  value={formData.targetAudience}
                  onChange={(e) => handleInputChange('targetAudience', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Style Keywords
                </label>
                <Input
                  type="text"
                  value={formData.styleKeywords}
                  onChange={(e) => handleInputChange('styleKeywords', e.target.value)}
                  placeholder="bold, modern, impactful (comma-separated)"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Lifestyle Context
                </label>
                <Input
                  type="text"
                  value={formData.lifestyleContext}
                  onChange={(e) => handleInputChange('lifestyleContext', e.target.value)}
                  placeholder="work, weekend, casual (comma-separated)"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Preview Image URL
                </label>
                <Input
                  type="url"
                  value={formData.previewImage}
                  onChange={(e) => handleInputChange('previewImage', e.target.value)}
                />
              </div>

              {/* Status Controls */}
              <div className="space-y-3 pt-4 border-t border-gray-200">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="active"
                    checked={formData.isActive}
                    onChange={(e) => handleInputChange('isActive', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <label htmlFor="active" className="text-sm font-medium text-gray-700">
                    Template is Active
                  </label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="featured"
                    checked={formData.isFeatured}
                    onChange={(e) => handleInputChange('isFeatured', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <label htmlFor="featured" className="text-sm font-medium text-gray-700">
                    Mark as Featured Template
                  </label>
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={loading}
                >
                  {loading ? 'Updating...' : 'Update Template'}
                </Button>
              </div>
            </form>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
