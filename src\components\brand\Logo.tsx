'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

export interface LogoProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  animated?: boolean;
  showTagline?: boolean;
}

const logoSizes = {
  sm: 'text-2xl',
  md: 'text-4xl',
  lg: 'text-6xl',
  xl: 'text-8xl',
};

export const Logo: React.FC<LogoProps> = ({
  className,
  size = 'md',
  animated = true,
  showTagline = false,
  ...props
}) => {
  return (
    <div
      className={cn('text-center space-y-2', className)}
      {...props}
    >
      <h1
        className={cn(
          'font-bold text-gradient-primary',
          logoSizes[size]
        )}
      >
        Ottiq
      </h1>
      
      {showTagline && (
        <p
          className={cn(
            'text-gray-700 font-light',
            size === 'sm' && 'text-sm',
            size === 'md' && 'text-lg',
            size === 'lg' && 'text-xl',
            size === 'xl' && 'text-2xl'
          )}
        >
          Wear Your Imagination
        </p>
      )}
    </div>
  );
};
