'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui';
import { useResponsive } from '@/hooks/useResponsive';
import { AiTryOnConsentModal } from '@/components/privacy/AiTryOnConsentModal';

interface TryOnJob {
  id: string;
  status: string;
  resultImageUrl?: string;
  confidence?: number;
  createdAt: string;
  scheduledDeletion?: string;
  customization: {
    name: string;
    product: {
      name: string;
      category: string;
    };
  };
}

interface ConsentStatus {
  aiTryOnConsent: boolean;
  aiTryOnConsentDate?: string;
  dailyTryOnCount: number;
  dailyLimit: number;
  totalTryOnCount: number;
  canTryOn: boolean;
  dataRetentionDays: number;
}

export const MyTryOnsTab: React.FC = () => {
  const { isMobile } = useResponsive();
  const [tryOns, setTryOns] = useState<TryOnJob[]>([]);
  const [consentStatus, setConsentStatus] = useState<ConsentStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [showConsentModal, setShowConsentModal] = useState(false);
  const [selectedTryOn, setSelectedTryOn] = useState<TryOnJob | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Fetch consent status
      const consentResponse = await fetch('/api/privacy/consent');
      if (consentResponse.ok) {
        const consentData = await consentResponse.json();
        setConsentStatus(consentData.data);
      }

      // Fetch try-on history
      const tryOnsResponse = await fetch('/api/user/try-ons');
      if (tryOnsResponse.ok) {
        const tryOnsData = await tryOnsResponse.json();
        setTryOns(tryOnsData.data || []);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleConsentAccept = async () => {
    try {
      const response = await fetch('/api/privacy/consent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          aiTryOnConsent: true,
          privacyPolicyAccepted: true,
        }),
      });

      if (response.ok) {
        setShowConsentModal(false);
        await fetchData();
      }
    } catch (error) {
      console.error('Error updating consent:', error);
    }
  };

  const handleConsentDecline = () => {
    setShowConsentModal(false);
  };

  const handleWithdrawConsent = async () => {
    if (!confirm('Are you sure you want to withdraw consent? This will disable AI try-on features.')) {
      return;
    }

    try {
      const response = await fetch('/api/privacy/consent', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          reason: 'User withdrew consent from dashboard',
          deleteExistingData: false,
        }),
      });

      if (response.ok) {
        await fetchData();
      }
    } catch (error) {
      console.error('Error withdrawing consent:', error);
    }
  };

  const handleDeleteTryOn = async (tryOnId: string) => {
    if (!confirm('Are you sure you want to delete this try-on? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/user/try-ons/${tryOnId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setTryOns(prev => prev.filter(t => t.id !== tryOnId));
      }
    } catch (error) {
      console.error('Error deleting try-on:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getDaysUntilDeletion = (scheduledDeletion?: string) => {
    if (!scheduledDeletion) return null;
    const days = Math.ceil((new Date(scheduledDeletion).getTime() - Date.now()) / (1000 * 60 * 60 * 24));
    return Math.max(0, days);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-warm-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Consent Status Card */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">AI Try-On Status</h3>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            consentStatus?.aiTryOnConsent 
              ? 'bg-green-100 text-green-800' 
              : 'bg-gray-100 text-gray-800'
          }`}>
            {consentStatus?.aiTryOnConsent ? 'Active' : 'Inactive'}
          </div>
        </div>

        {consentStatus?.aiTryOnConsent ? (
          <div className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-warm-600">
                  {consentStatus.dailyTryOnCount}
                </div>
                <div className="text-sm text-gray-600">Today</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {consentStatus.dailyLimit - consentStatus.dailyTryOnCount}
                </div>
                <div className="text-sm text-gray-600">Remaining</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {consentStatus.totalTryOnCount}
                </div>
                <div className="text-sm text-gray-600">Total</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-amber-600">
                  {consentStatus.dataRetentionDays}
                </div>
                <div className="text-sm text-gray-600">Days Kept</div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                onClick={handleWithdrawConsent}
                variant="outline"
                className="text-red-600 border-red-300 hover:bg-red-50"
              >
                Withdraw Consent
              </Button>
              <Button
                onClick={() => window.open('/privacy', '_blank')}
                variant="outline"
              >
                Privacy Settings
              </Button>
            </div>
          </div>
        ) : (
          <div className="text-center py-6">
            <div className="text-gray-600 mb-4">
              Enable AI try-on to see yourself in your custom designs
            </div>
            <Button
              onClick={() => setShowConsentModal(true)}
              className="bg-gradient-to-r from-warm-500 to-amber-500 hover:from-warm-600 hover:to-amber-600"
            >
              ✨ Enable AI Try-On
            </Button>
          </div>
        )}
      </div>

      {/* Try-On History */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Your Try-On History</h3>

        {tryOns.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-6xl mb-4">👗</div>
            <h4 className="text-lg font-medium text-gray-900 mb-2">No try-ons yet</h4>
            <p className="text-gray-600 mb-4">
              Create a custom design and try it on to see how it looks on you!
            </p>
            <Button
              onClick={() => window.location.href = '/customize'}
              className="bg-gradient-to-r from-warm-500 to-amber-500 hover:from-warm-600 hover:to-amber-600"
            >
              Start Designing
            </Button>
          </div>
        ) : (
          <div className={`grid gap-4 ${isMobile ? 'grid-cols-1' : 'grid-cols-2 lg:grid-cols-3'}`}>
            {tryOns.map((tryOn) => (
              <motion.div
                key={tryOn.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
              >
                {tryOn.resultImageUrl && (
                  <div className="aspect-square bg-gray-100 relative">
                    <img
                      src={tryOn.resultImageUrl}
                      alt={`Try-on of ${tryOn.customization.name}`}
                      className="w-full h-full object-cover cursor-pointer"
                      onClick={() => setSelectedTryOn(tryOn)}
                    />
                    {tryOn.confidence && (
                      <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                        {Math.round(tryOn.confidence * 100)}%
                      </div>
                    )}
                  </div>
                )}

                <div className="p-4">
                  <h4 className="font-medium text-gray-900 mb-1">
                    {tryOn.customization.name}
                  </h4>
                  <p className="text-sm text-gray-600 mb-2">
                    {tryOn.customization.product.name}
                  </p>
                  
                  <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                    <span>{formatDate(tryOn.createdAt)}</span>
                    <span className={`px-2 py-1 rounded ${
                      tryOn.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                      tryOn.status === 'FAILED' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {tryOn.status}
                    </span>
                  </div>

                  {tryOn.scheduledDeletion && (
                    <div className="text-xs text-amber-600 mb-3">
                      Auto-deletes in {getDaysUntilDeletion(tryOn.scheduledDeletion)} days
                    </div>
                  )}

                  <Button
                    onClick={() => handleDeleteTryOn(tryOn.id)}
                    variant="outline"
                    size="sm"
                    className="w-full text-red-600 border-red-300 hover:bg-red-50"
                  >
                    Delete Now
                  </Button>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>

      {/* Consent Modal */}
      <AiTryOnConsentModal
        isOpen={showConsentModal}
        onAccept={handleConsentAccept}
        onDecline={handleConsentDecline}
        onClose={() => setShowConsentModal(false)}
      />

      {/* Try-On Detail Modal */}
      <AnimatePresence>
        {selectedTryOn && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedTryOn(null)}
          >
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.9 }}
              className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-4 border-b border-gray-200 flex justify-between items-center">
                <h3 className="text-lg font-semibold">
                  {selectedTryOn.customization.name}
                </h3>
                <button
                  onClick={() => setSelectedTryOn(null)}
                  className="text-gray-400 hover:text-gray-600 text-2xl"
                >
                  ×
                </button>
              </div>
              
              {selectedTryOn.resultImageUrl && (
                <div className="p-4">
                  <img
                    src={selectedTryOn.resultImageUrl}
                    alt={`Try-on of ${selectedTryOn.customization.name}`}
                    className="w-full max-h-96 object-contain rounded-lg"
                  />
                </div>
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
