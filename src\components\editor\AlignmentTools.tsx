'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  AlignLeft, 
  AlignCenter, 
  AlignRight, 
  AlignVerticalJustifyCenter,
  AlignHorizontalJustifyCenter,
  AlignVerticalJustifyStart,
  AlignVerticalJustifyEnd,
  DistributeHorizontal,
  DistributeVertical
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { useEditorStore } from '@/stores/editorStore';
import { DesignElement } from '@/types';
import { triggerHapticFeedback } from '@/utils/touchGestures';
import { cn } from '@/lib/utils';

interface AlignmentToolsProps {
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  size?: 'sm' | 'md' | 'lg';
}

export const AlignmentTools: React.FC<AlignmentToolsProps> = ({
  className,
  orientation = 'horizontal',
  size = 'md'
}) => {
  const {
    canvas,
    selectedElementId,
    updateElement,
    saveToHistory
  } = useEditorStore();

  const selectedElements = canvas.elements.filter(el => 
    selectedElementId === el.id || el.id === selectedElementId
  );

  const hasMultipleElements = canvas.elements.length > 1;
  const hasSelection = selectedElementId !== null;

  // Alignment functions
  const alignLeft = () => {
    if (!hasSelection) return;
    
    const selectedElement = canvas.elements.find(el => el.id === selectedElementId);
    if (!selectedElement) return;

    const leftMost = Math.min(...canvas.elements.map(el => el.x));
    updateElement(selectedElementId, { x: leftMost });
    saveToHistory();
    triggerHapticFeedback('selection');
  };

  const alignCenter = () => {
    if (!hasSelection) return;
    
    const selectedElement = canvas.elements.find(el => el.id === selectedElementId);
    if (!selectedElement) return;

    const centerX = canvas.width / 2 - selectedElement.width / 2;
    updateElement(selectedElementId, { x: centerX });
    saveToHistory();
    triggerHapticFeedback('selection');
  };

  const alignRight = () => {
    if (!hasSelection) return;
    
    const selectedElement = canvas.elements.find(el => el.id === selectedElementId);
    if (!selectedElement) return;

    const rightMost = Math.max(...canvas.elements.map(el => el.x + el.width));
    updateElement(selectedElementId, { x: rightMost - selectedElement.width });
    saveToHistory();
    triggerHapticFeedback('selection');
  };

  const alignTop = () => {
    if (!hasSelection) return;
    
    const selectedElement = canvas.elements.find(el => el.id === selectedElementId);
    if (!selectedElement) return;

    const topMost = Math.min(...canvas.elements.map(el => el.y));
    updateElement(selectedElementId, { y: topMost });
    saveToHistory();
    triggerHapticFeedback('selection');
  };

  const alignMiddle = () => {
    if (!hasSelection) return;
    
    const selectedElement = canvas.elements.find(el => el.id === selectedElementId);
    if (!selectedElement) return;

    const centerY = canvas.height / 2 - selectedElement.height / 2;
    updateElement(selectedElementId, { y: centerY });
    saveToHistory();
    triggerHapticFeedback('selection');
  };

  const alignBottom = () => {
    if (!hasSelection) return;
    
    const selectedElement = canvas.elements.find(el => el.id === selectedElementId);
    if (!selectedElement) return;

    const bottomMost = Math.max(...canvas.elements.map(el => el.y + el.height));
    updateElement(selectedElementId, { y: bottomMost - selectedElement.height });
    saveToHistory();
    triggerHapticFeedback('selection');
  };

  const distributeHorizontally = () => {
    if (canvas.elements.length < 3) return;

    const sortedElements = [...canvas.elements].sort((a, b) => a.x - b.x);
    const leftMost = sortedElements[0].x;
    const rightMost = sortedElements[sortedElements.length - 1].x + sortedElements[sortedElements.length - 1].width;
    const totalWidth = rightMost - leftMost;
    const spacing = totalWidth / (sortedElements.length - 1);

    sortedElements.forEach((element, index) => {
      if (index === 0 || index === sortedElements.length - 1) return;
      const newX = leftMost + (spacing * index) - (element.width / 2);
      updateElement(element.id, { x: newX });
    });

    saveToHistory();
    triggerHapticFeedback('medium');
  };

  const distributeVertically = () => {
    if (canvas.elements.length < 3) return;

    const sortedElements = [...canvas.elements].sort((a, b) => a.y - b.y);
    const topMost = sortedElements[0].y;
    const bottomMost = sortedElements[sortedElements.length - 1].y + sortedElements[sortedElements.length - 1].height;
    const totalHeight = bottomMost - topMost;
    const spacing = totalHeight / (sortedElements.length - 1);

    sortedElements.forEach((element, index) => {
      if (index === 0 || index === sortedElements.length - 1) return;
      const newY = topMost + (spacing * index) - (element.height / 2);
      updateElement(element.id, { y: newY });
    });

    saveToHistory();
    triggerHapticFeedback('medium');
  };

  const tools = [
    {
      id: 'align-left',
      icon: AlignLeft,
      label: 'Align Left',
      action: alignLeft,
      disabled: !hasSelection,
      group: 'horizontal'
    },
    {
      id: 'align-center',
      icon: AlignCenter,
      label: 'Align Center',
      action: alignCenter,
      disabled: !hasSelection,
      group: 'horizontal'
    },
    {
      id: 'align-right',
      icon: AlignRight,
      label: 'Align Right',
      action: alignRight,
      disabled: !hasSelection,
      group: 'horizontal'
    },
    {
      id: 'align-top',
      icon: AlignVerticalJustifyStart,
      label: 'Align Top',
      action: alignTop,
      disabled: !hasSelection,
      group: 'vertical'
    },
    {
      id: 'align-middle',
      icon: AlignVerticalJustifyCenter,
      label: 'Align Middle',
      action: alignMiddle,
      disabled: !hasSelection,
      group: 'vertical'
    },
    {
      id: 'align-bottom',
      icon: AlignVerticalJustifyEnd,
      label: 'Align Bottom',
      action: alignBottom,
      disabled: !hasSelection,
      group: 'vertical'
    },
    {
      id: 'distribute-horizontal',
      icon: DistributeHorizontal,
      label: 'Distribute Horizontally',
      action: distributeHorizontally,
      disabled: canvas.elements.length < 3,
      group: 'distribute'
    },
    {
      id: 'distribute-vertical',
      icon: DistributeVertical,
      label: 'Distribute Vertically',
      action: distributeVertically,
      disabled: canvas.elements.length < 3,
      group: 'distribute'
    }
  ];

  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12'
  };

  const iconSizes = {
    sm: 14,
    md: 16,
    lg: 18
  };

  const containerClasses = cn(
    'flex gap-1 p-2 bg-white/90 backdrop-blur-sm rounded-lg shadow-lg border border-gray-200',
    orientation === 'vertical' ? 'flex-col' : 'flex-row flex-wrap',
    className
  );

  // Group tools by category
  const horizontalTools = tools.filter(tool => tool.group === 'horizontal');
  const verticalTools = tools.filter(tool => tool.group === 'vertical');
  const distributeTools = tools.filter(tool => tool.group === 'distribute');

  const renderToolGroup = (groupTools: typeof tools, groupName: string) => (
    <div key={groupName} className="flex gap-1">
      {groupTools.map(tool => {
        const Icon = tool.icon;
        
        return (
          <motion.div
            key={tool.id}
            whileHover={{ scale: tool.disabled ? 1 : 1.05 }}
            whileTap={{ scale: tool.disabled ? 1 : 0.95 }}
          >
            <Button
              variant="ghost"
              size="sm"
              onClick={tool.action}
              disabled={tool.disabled}
              className={cn(
                sizeClasses[size],
                'relative group',
                tool.disabled 
                  ? 'opacity-40 cursor-not-allowed' 
                  : 'hover:bg-blue-50 hover:text-blue-600'
              )}
              title={tool.label}
            >
              <Icon size={iconSizes[size]} />
              
              {/* Tooltip */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                {tool.label}
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
              </div>
            </Button>
          </motion.div>
        );
      })}
    </div>
  );

  if (!hasMultipleElements && !hasSelection) {
    return (
      <div className={cn('p-4 text-center text-gray-500 text-sm', className)}>
        Add more elements to use alignment tools
      </div>
    );
  }

  return (
    <div className={containerClasses}>
      {renderToolGroup(horizontalTools, 'horizontal')}
      
      {/* Divider */}
      <div className="w-px bg-gray-300 mx-1"></div>
      
      {renderToolGroup(verticalTools, 'vertical')}
      
      {/* Divider */}
      <div className="w-px bg-gray-300 mx-1"></div>
      
      {renderToolGroup(distributeTools, 'distribute')}
    </div>
  );
};
