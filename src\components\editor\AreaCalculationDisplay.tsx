'use client';

import React, { useMemo, useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Calculator, 
  TrendingUp, 
  TrendingDown, 
  Info, 
  Zap,
  Target,
  Sparkles
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useEditorStore } from '@/stores/editorStore';
import { 
  calculateCanvasPrintArea, 
  PRINT_SIZE_CATEGORIES, 
  getPricingImpactMessage,
  type PrintSizeCategory 
} from '@/lib/utils/areaCalculation';
import { cn } from '@/lib/utils';

interface AreaCalculationDisplayProps {
  className?: string;
  showDetails?: boolean;
  onPricingImpact?: (impact: any) => void;
}

export const AreaCalculationDisplay: React.FC<AreaCalculationDisplayProps> = ({
  className,
  showDetails = false,
  onPricingImpact
}) => {
  const { canvas } = useEditorStore();
  const [previousArea, setPreviousArea] = useState(0);
  const [showImpactAnimation, setShowImpactAnimation] = useState(false);

  // Calculate current area and pricing info
  const areaData = useMemo(() => {
    return calculateCanvasPrintArea(canvas);
  }, [canvas]);

  // Track area changes for impact messaging
  useEffect(() => {
    if (previousArea > 0 && areaData.totalArea !== previousArea) {
      const impact = getPricingImpactMessage(previousArea, areaData.totalArea);
      
      if (impact.hasImpact) {
        setShowImpactAnimation(true);
        onPricingImpact?.(impact);
        
        // Hide animation after 3 seconds
        setTimeout(() => setShowImpactAnimation(false), 3000);
      }
    }
    
    setPreviousArea(areaData.totalArea);
  }, [areaData.totalArea, previousArea, onPricingImpact]);

  const categoryInfo = PRINT_SIZE_CATEGORIES[areaData.printSizeCategory];
  const hasElements = canvas.elements.length > 0;

  // Get category color
  const getCategoryColor = (category: PrintSizeCategory) => {
    switch (category) {
      case 'small': return 'text-green-600 bg-green-50 border-green-200';
      case 'medium': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'large': return 'text-purple-600 bg-purple-50 border-purple-200';
      case 'full_coverage': return 'text-pink-600 bg-pink-50 border-pink-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  // Get impact icon
  const getImpactIcon = () => {
    if (areaData.totalArea <= 25) return Target;
    if (areaData.totalArea <= 100) return Zap;
    if (areaData.totalArea <= 250) return TrendingUp;
    return Sparkles;
  };

  const ImpactIcon = getImpactIcon();

  if (!hasElements) {
    return (
      <Card className={cn('border-dashed border-2', className)}>
        <CardContent className="p-4 text-center">
          <Calculator className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-500">
            Add design elements to see print area
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn('relative overflow-hidden', className)}>
      <CardContent className="p-4 space-y-4">
        {/* Impact Animation Overlay */}
        <AnimatePresence>
          {showImpactAnimation && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 flex items-center justify-center z-10"
            >
              <motion.div
                initial={{ y: 20 }}
                animate={{ y: 0 }}
                className="text-center"
              >
                <Sparkles className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                <p className="text-sm font-medium text-purple-700">
                  Print size updated!
                </p>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Area Display */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
              <ImpactIcon className="w-5 h-5 text-white" />
            </div>
            <div>
              <h4 className="font-semibold text-gray-900">Print Area</h4>
              <p className="text-sm text-gray-600">
                {areaData.totalArea.toFixed(1)} cm²
              </p>
            </div>
          </div>
          
          <div className={cn(
            'px-3 py-1 rounded-full border text-sm font-medium',
            getCategoryColor(areaData.printSizeCategory)
          )}>
            {categoryInfo.label}
          </div>
        </div>

        {/* Category Description */}
        <div className="bg-gray-50 p-3 rounded-lg">
          <p className="text-sm text-gray-700 font-medium mb-1">
            {categoryInfo.description}
          </p>
          <p className="text-xs text-gray-600">
            {getAreaImpactMessage(areaData.totalArea)}
          </p>
        </div>

        {/* Detailed Information */}
        {showDetails && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="space-y-3 border-t pt-3"
          >
            {/* Element Breakdown */}
            <div>
              <h5 className="text-sm font-medium text-gray-900 mb-2">
                Element Breakdown
              </h5>
              <div className="space-y-1">
                {areaData.elementAreas.map((element, index) => (
                  <div key={element.id} className="flex justify-between text-xs">
                    <span className="text-gray-600">
                      {element.type} {index + 1}
                    </span>
                    <span className="font-medium">
                      {element.area.toFixed(1)} cm²
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Overlapping Area */}
            {areaData.overlappingArea > 0 && (
              <div className="bg-yellow-50 p-2 rounded border border-yellow-200">
                <div className="flex items-center gap-2 text-yellow-700">
                  <Info className="w-4 h-4" />
                  <span className="text-xs font-medium">
                    {areaData.overlappingArea.toFixed(1)} cm² overlapping
                  </span>
                </div>
              </div>
            )}

            {/* Recommendations */}
            {areaData.recommendations && areaData.recommendations.length > 0 && (
              <div className="space-y-1">
                <h5 className="text-sm font-medium text-gray-900">
                  Suggestions
                </h5>
                {areaData.recommendations.map((rec, index) => (
                  <p key={index} className="text-xs text-gray-600 flex items-start gap-1">
                    <span className="text-blue-500 mt-0.5">•</span>
                    {rec}
                  </p>
                ))}
              </div>
            )}

            {/* Limits Warning */}
            {!areaData.isWithinLimits && (
              <div className="bg-red-50 p-2 rounded border border-red-200">
                <div className="flex items-center gap-2 text-red-700">
                  <Info className="w-4 h-4" />
                  <span className="text-xs font-medium">
                    {areaData.totalArea < 1 
                      ? 'Design too small for printing'
                      : 'Design exceeds maximum print area'
                    }
                  </span>
                </div>
              </div>
            )}
          </motion.div>
        )}

        {/* Quick Actions */}
        <div className="flex gap-2 pt-2 border-t">
          <Button
            variant="outline"
            size="sm"
            className="flex-1 text-xs"
            onClick={() => {
              // This would trigger a preview generation
              console.log('Generate preview for area:', areaData.totalArea);
            }}
          >
            <Calculator className="w-3 h-3 mr-1" />
            Preview
          </Button>
          
          {areaData.totalArea > PRINT_SIZE_CATEGORIES.small.maxArea && (
            <Button
              variant="outline"
              size="sm"
              className="flex-1 text-xs"
              onClick={() => {
                // This would show pricing impact
                console.log('Show pricing for category:', areaData.printSizeCategory);
              }}
            >
              <TrendingUp className="w-3 h-3 mr-1" />
              Pricing
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Get emotional impact message based on print area
 */
function getAreaImpactMessage(area: number): string {
  if (area <= 25) {
    return "✨ Perfect for subtle, personal touches that speak volumes";
  } else if (area <= 100) {
    return "🎯 Great size for making a statement while staying stylish";
  } else if (area <= 250) {
    return "🚀 Bold and eye-catching - your style will turn heads";
  } else {
    return "💥 Maximum impact design - own every room you enter";
  }
}
