'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Sparkles, Palette, Layers, Settings } from 'lucide-react';
import { ResponsiveContainer, ResponsiveGrid } from '@/components/ui/ResponsiveContainer';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { DesignCanvas } from './DesignCanvas';
import { EditorToolbar } from './EditorToolbar';
import { TextTool } from './TextTool';
import { ImageTool } from './ImageTool';
import { PlacementSelector } from './PlacementSelector';
import { TemplateGallery } from './TemplateGallery';
import { MobileCanvasControls } from './MobileCanvasControls';
import { SnapGuidesOverlay, SnapSettingsPanel } from './SnapGuidesOverlay';
import { AlignmentTools } from './AlignmentTools';
import { AreaCalculationDisplay } from './AreaCalculationDisplay';
import { PricingImpactIndicator, usePricingImpact } from './PricingImpactIndicator';
import { SavePreviewPanel } from './SavePreviewPanel';
import { useEditorStore } from '@/stores/editorStore';
import { useResponsive } from '@/components/ui/ResponsiveContainer';
import { calculateCanvasPrintArea } from '@/lib/utils/areaCalculation';
import { cn } from '@/lib/utils';

interface CustomizationEditorProps {
  className?: string;
}

type TabType = 'text' | 'image' | 'templates' | 'placement' | 'settings';

const TABS = [
  {
    id: 'text' as TabType,
    name: 'Text',
    icon: Palette,
    description: 'Add your words',
    gradient: 'from-purple-500 to-pink-500'
  },
  {
    id: 'image' as TabType,
    name: 'Images',
    icon: Layers,
    description: 'Upload visuals',
    gradient: 'from-blue-500 to-cyan-500'
  },
  {
    id: 'templates' as TabType,
    name: 'Templates',
    icon: Sparkles,
    description: 'Ready designs',
    gradient: 'from-orange-500 to-red-500'
  },
  {
    id: 'placement' as TabType,
    name: 'Placement',
    icon: Settings,
    description: 'Choose position',
    gradient: 'from-green-500 to-teal-500'
  }
];

export const CustomizationEditor: React.FC<CustomizationEditorProps> = ({ className }) => {
  const [activeTab, setActiveTab] = useState<TabType>('text');
  const [isToolsPanelOpen, setIsToolsPanelOpen] = useState(true);
  const [showSnapSettings, setShowSnapSettings] = useState(false);
  const [previousArea, setPreviousArea] = useState(0);
  const [showAreaDetails, setShowAreaDetails] = useState(false);

  const { addImpact, impactHistory } = usePricingImpact();

  const { isMobile, isTouchDevice } = useResponsive();
  const {
    activeTool,
    canvas,
    zoom,
    snapEnabled,
    snapThreshold,
    gridEnabled,
    gridSize,
    snapGuides,
    setSnapEnabled,
    setSnapThreshold,
    setGridEnabled,
    setGridSize,
    setMobileMode,
    updateSnapGuides
  } = useEditorStore();

  // Update mobile mode when screen size changes
  React.useEffect(() => {
    setMobileMode(isMobile);
  }, [isMobile, setMobileMode]);

  // Update snap guides when canvas changes
  React.useEffect(() => {
    updateSnapGuides();
  }, [canvas, updateSnapGuides]);

  // Track area changes for pricing impact
  React.useEffect(() => {
    const currentAreaData = calculateCanvasPrintArea(canvas);
    const currentArea = currentAreaData.totalArea;

    if (previousArea > 0 && currentArea !== previousArea) {
      // Add to impact history for analytics
      addImpact(previousArea, currentArea, {
        category: currentAreaData.printSizeCategory,
        timestamp: Date.now(),
      });
    }

    setPreviousArea(currentArea);
  }, [canvas, previousArea, addImpact]);

  const renderToolPanel = () => {
    switch (activeTab) {
      case 'text':
        return <TextTool />;
      case 'image':
        return <ImageTool />;
      case 'templates':
        return (
          <TemplateGallery
            onTemplateSelect={() => setActiveTab('text')}
            maxColumns={isMobile ? 1 : 2}
          />
        );
      case 'placement':
        return <PlacementSelector />;
      default:
        return null;
    }
  };

  return (
    <ResponsiveContainer className={cn('min-h-screen bg-gray-50', className)}>
      <div className="py-6 space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center space-y-4"
        >
          <div className="flex items-center justify-center gap-3">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
                Own the look
              </h1>
              <p className="text-gray-600 text-lg">
                Create something uniquely yours
              </p>
            </div>
          </div>
          
          <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-xl max-w-2xl mx-auto">
            <p className="text-purple-800 font-medium">
              ✨ Every design tells a story. What's yours?
            </p>
          </div>
        </motion.div>

        {/* Main Editor Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* Tools Panel - Mobile: Full width, Desktop: Sidebar */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="lg:col-span-4 xl:col-span-3 space-y-4"
          >
            {/* Tab Navigation */}
            <Card>
              <CardContent className="p-3">
                <div className="grid grid-cols-3 gap-1">
                  {TABS.map((tab) => {
                    const Icon = tab.icon;
                    const isActive = activeTab === tab.id;
                    
                    return (
                      <motion.button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={cn(
                          'p-3 rounded-lg text-center transition-all',
                          isActive
                            ? 'bg-gradient-to-r text-white shadow-lg'
                            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                        )}
                        style={isActive ? { backgroundImage: `linear-gradient(to right, var(--tw-gradient-stops))` } : {}}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className={cn(
                          isActive && `bg-gradient-to-r ${tab.gradient}`
                        )}>
                          <Icon className="w-5 h-5 mx-auto mb-1" />
                          <div className="text-sm font-medium">{tab.name}</div>
                          <div className="text-xs opacity-80">{tab.description}</div>
                        </div>
                      </motion.button>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Active Tool Panel */}
            <AnimatePresence mode="wait">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
              >
                {renderToolPanel()}
              </motion.div>
            </AnimatePresence>

            {/* Area Calculation Display */}
            <AreaCalculationDisplay
              showDetails={showAreaDetails}
              onPricingImpact={(impact) => {
                console.log('Pricing impact:', impact);
              }}
            />

            {/* Toggle Area Details */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAreaDetails(!showAreaDetails)}
              className="w-full text-xs"
            >
              {showAreaDetails ? 'Hide' : 'Show'} Area Details
            </Button>

            {/* Save & Preview Panel */}
            <SavePreviewPanel
              productId="sample-product-id"
              userId="sample-user-id"
              onSaveSuccess={(data) => {
                console.log('Design saved successfully:', data);
                // You could show a success toast here
              }}
              onPreviewGenerated={(data) => {
                console.log('Preview generated:', data);
                // You could update UI state here
              }}
            />
          </motion.div>

          {/* Canvas Area */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="lg:col-span-8 xl:col-span-9 space-y-4"
          >
            {/* Toolbar */}
            <EditorToolbar />

            {/* Alignment Tools */}
            {!isMobile && (
              <AlignmentTools
                className="justify-center"
                size="sm"
              />
            )}

            {/* Canvas Container */}
            <Card className="p-6">
              <CardContent className="p-0">
                <div className="flex flex-col items-center space-y-4">
                  {/* Canvas */}
                  <div className="relative">
                    <DesignCanvas className="max-w-full" />

                    {/* Snap Guides Overlay */}
                    <SnapGuidesOverlay
                      canvas={canvas}
                      elements={canvas.elements}
                      selectedElementId={null}
                      activeSnapGuides={snapGuides}
                      zoom={zoom}
                      showGrid={gridEnabled}
                      gridSize={gridSize}
                      className="absolute inset-0 pointer-events-none"
                    />

                    {/* Canvas Info */}
                    <div className="absolute -bottom-12 left-0 right-0 text-center">
                      <p className="text-sm text-gray-500">
                        Canvas: {canvas.width} × {canvas.height}px • Zoom: {Math.round(zoom * 100)}%
                      </p>
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className="flex flex-wrap gap-3 justify-center pt-8">
                    <Button
                      variant="outline"
                      onClick={() => setActiveTab('text')}
                      className="flex items-center gap-2"
                    >
                      <Palette className="w-4 h-4" />
                      Add Text
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setActiveTab('image')}
                      className="flex items-center gap-2"
                    >
                      <Layers className="w-4 h-4" />
                      Add Image
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setActiveTab('placement')}
                      className="flex items-center gap-2"
                    >
                      <Settings className="w-4 h-4" />
                      Change Placement
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Inspirational Footer */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="text-center space-y-3"
            >
              <div className="bg-gradient-to-r from-purple-50 via-pink-50 to-blue-50 p-6 rounded-xl">
                <h3 className="font-semibold text-gray-900 mb-2">
                  🎨 Place your mark on the world
                </h3>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Every great design starts with a single element. Add text, upload images, 
                  and watch your vision come to life. This is your canvas—make it unforgettable.
                </p>
              </div>
              
              <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
                <span>✨ Unlimited creativity</span>
                <span>🎯 Perfect placement</span>
                <span>💫 Your unique style</span>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Pricing Impact Indicator */}
      <PricingImpactIndicator
        currentArea={calculateCanvasPrintArea(canvas).totalArea}
        previousArea={previousArea}
        currentCategory={calculateCanvasPrintArea(canvas).printSizeCategory}
        onDismiss={() => {
          console.log('Pricing impact dismissed');
        }}
      />

      {/* Mobile Canvas Controls */}
      {isTouchDevice && (
        <MobileCanvasControls
          position="bottom"
          showLabels={false}
        />
      )}

      {/* Snap Settings Panel */}
      {showSnapSettings && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
          onClick={() => setShowSnapSettings(false)}
        >
          <motion.div
            onClick={(e) => e.stopPropagation()}
            className="max-w-md w-full"
          >
            <SnapSettingsPanel
              snapEnabled={snapEnabled}
              onSnapToggle={setSnapEnabled}
              gridEnabled={gridEnabled}
              onGridToggle={setGridEnabled}
              gridSize={gridSize}
              onGridSizeChange={setGridSize}
              snapThreshold={snapThreshold}
              onSnapThresholdChange={setSnapThreshold}
            />
          </motion.div>
        </motion.div>
      )}
    </ResponsiveContainer>
  );
};
