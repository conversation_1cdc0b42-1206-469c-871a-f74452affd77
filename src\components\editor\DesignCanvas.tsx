'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Stage, Layer, Rect, Text, Image as KonvaImage, Transformer, Line } from 'react-konva';
import { useGesture } from '@use-gesture/react';
import { useEditorStore } from '@/stores/editorStore';
import { DesignElement, TextElementData, ImageElementData, TouchGestureState, SnapGuide } from '@/types';
import {
  recognizeGesture,
  getTouchCenter,
  getTouchDistance,
  getTouchAngle,
  triggerHapticFeedback,
  constrainScale,
  constrainRotation,
  throttle
} from '@/utils/touchGestures';
import {
  generateSnapGuides,
  calculateSnapResult,
  applySnapFeedback
} from '@/utils/snapGuides';
import Konva from 'konva';

interface DesignCanvasProps {
  className?: string;
}

// Text Element Component
const TextElement: React.FC<{
  element: DesignElement;
  isSelected: boolean;
  onSelect: () => void;
  onDrag?: (position: { x: number; y: number }) => void;
}> = ({
  element,
  isSelected,
  onSelect,
  onDrag
}) => {
  const updateElement = useEditorStore(state => state.updateElement);
  const textData = element.data as TextElementData;

  const getFontFamily = (mood: string) => {
    switch (mood) {
      case 'bold': return 'Inter, Arial, sans-serif';
      case 'elegant': return 'Playfair Display, serif';
      case 'playful': return 'Poppins, sans-serif';
      default: return 'Inter, Arial, sans-serif';
    }
  };

  return (
    <Text
      x={element.x}
      y={element.y}
      width={element.width}
      height={element.height}
      text={textData.text}
      fontSize={textData.fontSize}
      fontFamily={getFontFamily(textData.mood)}
      fontStyle={textData.fontWeight === 'bold' ? 'bold' : 'normal'}
      fill={textData.color}
      align={textData.align}
      rotation={element.rotation}
      opacity={element.opacity}
      visible={element.visible}
      draggable={!element.locked}
      onClick={onSelect}
      onTap={onSelect}
      onDragMove={(e) => {
        const newPosition = { x: e.target.x(), y: e.target.y() };
        onDrag?.(newPosition);
      }}
      onDragEnd={(e) => {
        const newPosition = { x: e.target.x(), y: e.target.y() };
        updateElement(element.id, newPosition);
      }}
      onTransformEnd={(e) => {
        const node = e.target;
        const scaleX = node.scaleX();
        const scaleY = node.scaleY();

        // Reset scale and update dimensions
        node.scaleX(1);
        node.scaleY(1);

        updateElement(element.id, {
          x: node.x(),
          y: node.y(),
          width: Math.max(5, node.width() * scaleX),
          height: Math.max(5, node.height() * scaleY),
          rotation: node.rotation()
        });
      }}
    />
  );
};

// Image Element Component
const ImageElement: React.FC<{
  element: DesignElement;
  isSelected: boolean;
  onSelect: () => void;
  onDrag?: (position: { x: number; y: number }) => void;
}> = ({
  element,
  isSelected,
  onSelect,
  onDrag
}) => {
  const updateElement = useEditorStore(state => state.updateElement);
  const [image, setImage] = useState<HTMLImageElement | null>(null);
  const imageData = element.data as ImageElementData;

  useEffect(() => {
    const img = new window.Image();
    img.crossOrigin = 'anonymous';
    img.onload = () => {
      setImage(img);
    };
    img.src = imageData.src;
  }, [imageData.src]);

  if (!image) return null;

  return (
    <KonvaImage
      x={element.x}
      y={element.y}
      width={element.width}
      height={element.height}
      image={image}
      rotation={element.rotation}
      opacity={element.opacity}
      visible={element.visible}
      draggable={!element.locked}
      onClick={onSelect}
      onTap={onSelect}
      onDragMove={(e) => {
        const newPosition = { x: e.target.x(), y: e.target.y() };
        onDrag?.(newPosition);
      }}
      onDragEnd={(e) => {
        const newPosition = { x: e.target.x(), y: e.target.y() };
        updateElement(element.id, newPosition);
      }}
      onTransformEnd={(e) => {
        const node = e.target;
        const scaleX = node.scaleX();
        const scaleY = node.scaleY();

        // Reset scale and update dimensions
        node.scaleX(1);
        node.scaleY(1);

        updateElement(element.id, {
          x: node.x(),
          y: node.y(),
          width: Math.max(5, node.width() * scaleX),
          height: Math.max(5, node.height() * scaleY),
          rotation: node.rotation()
        });
      }}
    />
  );
};

export const DesignCanvas: React.FC<DesignCanvasProps> = ({ className }) => {
  const stageRef = useRef<Konva.Stage>(null);
  const transformerRef = useRef<Konva.Transformer>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const [gestureState, setGestureState] = useState<TouchGestureState>({
    isActive: false,
    type: 'tap',
    startTime: 0,
    scale: 1,
    rotation: 0,
    translation: { x: 0, y: 0 },
    velocity: { x: 0, y: 0 },
    center: { x: 0, y: 0 },
    fingers: 0,
  });

  const [snapGuides, setSnapGuides] = useState<SnapGuide[]>([]);
  const [activeSnapGuides, setActiveSnapGuides] = useState<SnapGuide[]>([]);

  const {
    canvas,
    selectedElementId,
    selectElement,
    updateElement,
    zoom,
    setZoom,
    activeTool,
    saveToHistory
  } = useEditorStore();

  // Generate snap guides when canvas or elements change
  useEffect(() => {
    const guides = generateSnapGuides(canvas, canvas.elements, selectedElementId || undefined);
    setSnapGuides(guides);
  }, [canvas, selectedElementId]);

  // Handle element selection and transformer
  useEffect(() => {
    if (selectedElementId && transformerRef.current && stageRef.current) {
      const selectedNode = stageRef.current.findOne(`#${selectedElementId}`);
      if (selectedNode) {
        transformerRef.current.nodes([selectedNode]);
        transformerRef.current.getLayer()?.batchDraw();
      }
    } else if (transformerRef.current) {
      transformerRef.current.nodes([]);
      transformerRef.current.getLayer()?.batchDraw();
    }
  }, [selectedElementId]);

  // Throttled update function for performance
  const throttledUpdateElement = useCallback(
    throttle((elementId: string, updates: Partial<DesignElement>) => {
      updateElement(elementId, updates);
    }, 16), // ~60fps
    [updateElement]
  );

  // Handle element drag with snap guides
  const handleElementDrag = useCallback((elementId: string, newPosition: { x: number; y: number }) => {
    const element = canvas.elements.find(el => el.id === elementId);
    if (!element) return;

    const snapResult = calculateSnapResult(
      newPosition,
      { width: element.width, height: element.height },
      snapGuides,
      10
    );

    setActiveSnapGuides(snapResult.guides);

    if (snapResult.snapped) {
      applySnapFeedback(snapResult, true);
      throttledUpdateElement(elementId, snapResult.adjustedPosition);
    } else {
      throttledUpdateElement(elementId, newPosition);
    }
  }, [canvas.elements, snapGuides, throttledUpdateElement]);

  // Touch gesture handlers
  const handleTouchStart = useCallback((e: TouchEvent) => {
    e.preventDefault();
    const touches = e.touches;

    setGestureState(prev => ({
      ...prev,
      isActive: true,
      type: recognizeGesture(touches),
      startTime: Date.now(),
      fingers: touches.length,
      center: getTouchCenter(touches),
    }));
  }, []);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    e.preventDefault();
    const touches = e.touches;

    if (touches.length === 2 && stageRef.current) {
      // Handle pinch-to-zoom
      const distance = getTouchDistance(touches[0], touches[1]);
      const angle = getTouchAngle(touches[0], touches[1]);
      const center = getTouchCenter(touches);

      setGestureState(prev => {
        const scaleFactor = distance / (prev.scale * 100 || 100);
        const newScale = constrainScale(zoom * scaleFactor, 0.1, 5.0);
        const rotationDelta = constrainRotation(angle - prev.rotation);

        // Apply zoom
        if (Math.abs(scaleFactor - 1) > 0.01) {
          setZoom(newScale);
          triggerHapticFeedback('light');
        }

        return {
          ...prev,
          scale: distance,
          rotation: angle,
          center,
          type: Math.abs(scaleFactor - 1) > Math.abs(rotationDelta) / 180 ? 'pinch' : 'rotate',
        };
      });
    }
  }, [zoom, setZoom]);

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    e.preventDefault();

    setGestureState(prev => ({
      ...prev,
      isActive: false,
      fingers: 0,
    }));

    setActiveSnapGuides([]);
    saveToHistory();
  }, [saveToHistory]);

  // Set up touch event listeners
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('touchstart', handleTouchStart, { passive: false });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd, { passive: false });

    return () => {
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
    };
  }, [handleTouchStart, handleTouchMove, handleTouchEnd]);

  const handleStageClick = (e: any) => {
    // Deselect when clicking on empty area
    if (e.target === e.target.getStage()) {
      selectElement(null);
    }
  };

  const handleElementSelect = (elementId: string) => {
    selectElement(elementId);
  };

  const renderElement = (element: DesignElement) => {
    const isSelected = element.id === selectedElementId;
    const key = element.id;

    switch (element.type) {
      case 'text':
        return (
          <TextElement
            key={key}
            element={element}
            isSelected={isSelected}
            onSelect={() => handleElementSelect(element.id)}
            onDrag={(newPosition) => handleElementDrag(element.id, newPosition)}
          />
        );
      case 'image':
        return (
          <ImageElement
            key={key}
            element={element}
            isSelected={isSelected}
            onSelect={() => handleElementSelect(element.id)}
            onDrag={(newPosition) => handleElementDrag(element.id, newPosition)}
          />
        );
      default:
        return null;
    }
  };

  // Render snap guides
  const renderSnapGuides = () => {
    return activeSnapGuides.map(guide => (
      <Line
        key={guide.id}
        points={
          guide.type === 'horizontal'
            ? [0, guide.position, canvas.width, guide.position]
            : [guide.position, 0, guide.position, canvas.height]
        }
        stroke={guide.color}
        strokeWidth={1}
        opacity={0.8}
        dash={[5, 5]}
        listening={false}
      />
    ));
  };

  return (
    <div
      ref={containerRef}
      className={`relative overflow-hidden rounded-xl border-2 border-gray-200 touch-none ${className}`}
    >
      <Stage
        ref={stageRef}
        width={canvas.width * zoom}
        height={canvas.height * zoom}
        scaleX={zoom}
        scaleY={zoom}
        onClick={handleStageClick}
        onTap={handleStageClick}
      >
        <Layer>
          {/* Canvas background */}
          <Rect
            width={canvas.width}
            height={canvas.height}
            fill={canvas.backgroundColor}
          />

          {/* Snap guides */}
          {renderSnapGuides()}

          {/* Render all elements */}
          {canvas.elements.map(renderElement)}

          {/* Transformer for selected elements */}
          <Transformer
            ref={transformerRef}
            boundBoxFunc={(oldBox, newBox) => {
              // Limit resize
              if (newBox.width < 5 || newBox.height < 5) {
                return oldBox;
              }
              return newBox;
            }}
            enabledAnchors={[
              'top-left',
              'top-right',
              'bottom-left',
              'bottom-right',
              'middle-left',
              'middle-right'
            ]}
            rotateEnabled={true}
            borderStroke="#4F46E5"
            borderStrokeWidth={2}
            anchorFill="#4F46E5"
            anchorStroke="#ffffff"
            anchorStrokeWidth={2}
            anchorSize={8}
          />
        </Layer>
      </Stage>

      {/* Touch gesture feedback overlay */}
      {gestureState.isActive && (
        <div className="absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs z-10">
          {gestureState.type === 'pinch' && `Zoom: ${(zoom * 100).toFixed(0)}%`}
          {gestureState.type === 'rotate' && `Rotate: ${gestureState.rotation.toFixed(0)}°`}
          {gestureState.type === 'pan' && 'Moving'}
        </div>
      )}

      {/* Canvas overlay for tools */}
      {activeTool !== 'select' && (
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm">
            {activeTool === 'text' && 'Click to add text'}
            {activeTool === 'image' && 'Click to place image'}
          </div>
        </div>
      )}
    </div>
  );
};
