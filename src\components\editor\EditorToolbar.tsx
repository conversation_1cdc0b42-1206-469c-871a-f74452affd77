'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  MousePointer,
  Type,
  Image,
  Trash2,
  Copy,
  ZoomIn,
  ZoomOut,
  RotateCcw
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { useEditorStore } from '@/stores/editorStore';
import { UndoRedoToolbar, useUndoRedoShortcuts } from './UndoRedoToolbar';
import { cn } from '@/lib/utils';

interface EditorToolbarProps {
  className?: string;
}

const TOOLS = [
  {
    id: 'select' as const,
    name: 'Select',
    icon: MousePointer,
    description: 'Select and move elements',
    shortcut: 'V'
  },
  {
    id: 'text' as const,
    name: 'Text',
    icon: Type,
    description: 'Add text to your design',
    shortcut: 'T'
  },
  {
    id: 'image' as const,
    name: 'Image',
    icon: Image,
    description: 'Upload and place images',
    shortcut: 'I'
  }
];

export const EditorToolbar: React.FC<EditorToolbarProps> = ({ className }) => {
  // Enable keyboard shortcuts
  useUndoRedoShortcuts();

  const {
    activeTool,
    setActiveTool,
    selectedElementId,
    deleteElement,
    duplicateElement,
    zoom,
    setZoom,
    clearCanvas
  } = useEditorStore();

  const hasSelection = selectedElementId !== null;

  const handleToolSelect = (toolId: typeof activeTool) => {
    setActiveTool(toolId);
  };

  const handleZoomIn = () => {
    setZoom(Math.min(zoom + 0.1, 3));
  };

  const handleZoomOut = () => {
    setZoom(Math.max(zoom - 0.1, 0.1));
  };

  const handleResetZoom = () => {
    setZoom(1);
  };

  const handleDelete = () => {
    if (selectedElementId) {
      deleteElement(selectedElementId);
    }
  };

  const handleDuplicate = () => {
    if (selectedElementId) {
      duplicateElement(selectedElementId);
    }
  };

  const handleClearCanvas = () => {
    if (confirm('Are you sure you want to clear the canvas? This action cannot be undone.')) {
      clearCanvas();
    }
  };

  return (
    <div className={cn('bg-white border border-gray-200 rounded-xl p-3', className)}>
      <div className="flex flex-wrap gap-2">
        {/* Main Tools */}
        <div className="flex gap-1 p-1 bg-gray-100 rounded-lg">
          {TOOLS.map((tool) => {
            const Icon = tool.icon;
            const isActive = activeTool === tool.id;
            
            return (
              <motion.button
                key={tool.id}
                onClick={() => handleToolSelect(tool.id)}
                className={cn(
                  'p-2 rounded-md transition-all relative group',
                  isActive
                    ? 'bg-white shadow-sm text-purple-600'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                )}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                title={`${tool.name} (${tool.shortcut})`}
              >
                <Icon className="w-5 h-5" />
                
                {/* Tooltip */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                  {tool.description}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                </div>
              </motion.button>
            );
          })}
        </div>

        {/* Divider */}
        <div className="w-px bg-gray-300 mx-1"></div>

        {/* Enhanced History Controls */}
        <UndoRedoToolbar size="sm" />

        {/* Divider */}
        <div className="w-px bg-gray-300 mx-1"></div>

        {/* Element Actions */}
        <div className="flex gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDuplicate}
            disabled={!hasSelection}
            title="Duplicate (Ctrl+D)"
            className="p-2"
          >
            <Copy className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDelete}
            disabled={!hasSelection}
            title="Delete (Del)"
            className="p-2 text-red-600 hover:text-red-700"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>

        {/* Divider */}
        <div className="w-px bg-gray-300 mx-1"></div>

        {/* Zoom Controls */}
        <div className="flex gap-1 items-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleZoomOut}
            title="Zoom Out (-)"
            className="p-2"
          >
            <ZoomOut className="w-4 h-4" />
          </Button>
          
          <button
            onClick={handleResetZoom}
            className="px-2 py-1 text-sm font-medium text-gray-600 hover:text-gray-900 min-w-[3rem] text-center"
            title="Reset Zoom (0)"
          >
            {Math.round(zoom * 100)}%
          </button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleZoomIn}
            title="Zoom In (+)"
            className="p-2"
          >
            <ZoomIn className="w-4 h-4" />
          </Button>
        </div>

        {/* Divider */}
        <div className="w-px bg-gray-300 mx-1"></div>

        {/* Clear Canvas */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClearCanvas}
          title="Clear Canvas"
          className="p-2 text-red-600 hover:text-red-700"
        >
          <RotateCcw className="w-4 h-4" />
        </Button>
      </div>

      {/* Active Tool Indicator */}
      <div className="mt-3 pt-3 border-t border-gray-200">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
          <span className="text-sm text-gray-600">
            {TOOLS.find(tool => tool.id === activeTool)?.name} tool active
          </span>
        </div>
      </div>
    </div>
  );
};
