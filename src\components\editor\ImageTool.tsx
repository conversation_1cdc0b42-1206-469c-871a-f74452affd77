'use client';

import React, { useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { Upload, Image as ImageIcon, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { useEditorStore } from '@/stores/editorStore';
import { cn } from '@/lib/utils';

interface ImageToolProps {
  className?: string;
}

export const ImageTool: React.FC<ImageToolProps> = ({ className }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  const { addImage, canvas, setActiveTool } = useEditorStore();

  const handleFileSelect = (file: File) => {
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    setIsUploading(true);
    
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      if (result) {
        // Add image to center of canvas
        const centerX = canvas.width / 2 - 100;
        const centerY = canvas.height / 2 - 100;
        
        addImage(result, centerX, centerY);
        setActiveTool('select');
      }
      setIsUploading(false);
    };
    reader.readAsDataURL(file);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const file = e.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <Card className={cn('w-full', className)}>
      <CardContent className="p-4 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg">
            <ImageIcon className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Add your vision</h3>
            <p className="text-sm text-gray-600">Upload images that speak to you</p>
          </div>
        </div>

        {/* Upload Area */}
        <div
          className={cn(
            'relative border-2 border-dashed rounded-xl p-8 text-center transition-all cursor-pointer',
            isDragging
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={openFileDialog}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileInputChange}
            className="hidden"
          />
          
          {isUploading ? (
            <div className="space-y-3">
              <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto" />
              <p className="text-gray-600">Uploading your image...</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto">
                <Upload className="w-8 h-8 text-white" />
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-1">
                  Drop your image here, or click to browse
                </h4>
                <p className="text-sm text-gray-600">
                  PNG, JPG, WEBP up to 10MB
                </p>
              </div>
              
              <Button 
                variant="outline" 
                size="lg"
                className="mt-4"
              >
                <Upload className="w-4 h-4 mr-2" />
                Choose Image
              </Button>
            </div>
          )}
        </div>

        {/* Quick Tips */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">💡 Pro tips for amazing results</h4>
          <div className="space-y-2 text-sm text-gray-600">
            <div className="flex items-start gap-2">
              <span className="text-blue-500">•</span>
              <span>High-resolution images (300+ DPI) look best when printed</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-blue-500">•</span>
              <span>PNG files with transparent backgrounds work great for logos</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-blue-500">•</span>
              <span>Square images are perfect for centered designs</span>
            </div>
          </div>
        </div>

        {/* Sample Images */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">✨ Or try these sample designs</h4>
          <div className="grid grid-cols-3 gap-2">
            {[
              { name: 'Geometric', url: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=200&h=200&fit=crop' },
              { name: 'Nature', url: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=200&h=200&fit=crop' },
              { name: 'Abstract', url: 'https://images.unsplash.com/photo-1558591710-4b4a1ae0f04d?w=200&h=200&fit=crop' }
            ].map((sample, index) => (
              <motion.button
                key={index}
                onClick={() => {
                  const centerX = canvas.width / 2 - 100;
                  const centerY = canvas.height / 2 - 100;
                  addImage(sample.url, centerX, centerY);
                  setActiveTool('select');
                }}
                className="aspect-square rounded-lg overflow-hidden border-2 border-gray-200 hover:border-blue-500 transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <img
                  src={sample.url}
                  alt={sample.name}
                  className="w-full h-full object-cover"
                />
              </motion.button>
            ))}
          </div>
        </div>

        {/* Inspirational Message */}
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-lg">
          <p className="text-sm text-blue-800 font-medium text-center">
            🎨 Your creativity has no limits. Upload what inspires you!
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
