'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence, PanInfo } from 'framer-motion';
import { 
  ZoomIn, 
  ZoomOut, 
  RotateCw, 
  Move, 
  Square, 
  Circle,
  Maximize2,
  Minimize2,
  Grid3X3,
  <PERSON>ers,
  Eye,
  EyeOff,
  Lock,
  Unlock
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { useEditorStore } from '@/stores/editorStore';
import { useResponsive } from '@/components/ui/ResponsiveContainer';
import { triggerHapticFeedback } from '@/utils/touchGestures';
import { cn } from '@/lib/utils';

interface MobileCanvasControlsProps {
  className?: string;
  position?: 'bottom' | 'side' | 'floating';
  showLabels?: boolean;
}

export const MobileCanvasControls: React.FC<MobileCanvasControlsProps> = ({
  className,
  position = 'bottom',
  showLabels = false
}) => {
  const { isMobile, isTouchDevice } = useResponsive();
  const [isExpanded, setIsExpanded] = useState(false);
  const [activePanel, setActivePanel] = useState<'zoom' | 'transform' | 'layers' | null>(null);
  
  const {
    zoom,
    setZoom,
    selectedElementId,
    updateElement,
    canvas,
    selectElement
  } = useEditorStore();

  // Auto-collapse on mobile after inactivity
  useEffect(() => {
    if (!isMobile) return;
    
    let timeout: NodeJS.Timeout;
    if (isExpanded) {
      timeout = setTimeout(() => {
        setIsExpanded(false);
        setActivePanel(null);
      }, 5000);
    }
    
    return () => clearTimeout(timeout);
  }, [isExpanded, isMobile]);

  const selectedElement = canvas.elements.find(el => el.id === selectedElementId);

  const handleZoomIn = () => {
    const newZoom = Math.min(zoom + 0.25, 3);
    setZoom(newZoom);
    triggerHapticFeedback('light');
  };

  const handleZoomOut = () => {
    const newZoom = Math.max(zoom - 0.25, 0.1);
    setZoom(newZoom);
    triggerHapticFeedback('light');
  };

  const handleZoomReset = () => {
    setZoom(1);
    triggerHapticFeedback('medium');
  };

  const handleRotateElement = () => {
    if (!selectedElement) return;
    
    const newRotation = (selectedElement.rotation + 15) % 360;
    updateElement(selectedElement.id, { rotation: newRotation });
    triggerHapticFeedback('selection');
  };

  const handleToggleVisibility = () => {
    if (!selectedElement) return;
    
    updateElement(selectedElement.id, { visible: !selectedElement.visible });
    triggerHapticFeedback('selection');
  };

  const handleToggleLock = () => {
    if (!selectedElement) return;
    
    updateElement(selectedElement.id, { locked: !selectedElement.locked });
    triggerHapticFeedback('medium');
  };

  const handlePanelToggle = (panel: typeof activePanel) => {
    if (activePanel === panel) {
      setActivePanel(null);
      setIsExpanded(false);
    } else {
      setActivePanel(panel);
      setIsExpanded(true);
    }
    triggerHapticFeedback('selection');
  };

  // Quick action buttons
  const quickActions = [
    {
      id: 'zoom',
      icon: ZoomIn,
      label: 'Zoom',
      action: () => handlePanelToggle('zoom'),
      active: activePanel === 'zoom'
    },
    {
      id: 'transform',
      icon: Move,
      label: 'Transform',
      action: () => handlePanelToggle('transform'),
      active: activePanel === 'transform',
      disabled: !selectedElement
    },
    {
      id: 'layers',
      icon: Layers,
      label: 'Layers',
      action: () => handlePanelToggle('layers'),
      active: activePanel === 'layers'
    }
  ];

  // Zoom panel content
  const ZoomPanel = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="bg-white rounded-xl shadow-lg border p-4 space-y-3"
    >
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-gray-700">Zoom: {Math.round(zoom * 100)}%</span>
        <Button
          size="sm"
          variant="ghost"
          onClick={handleZoomReset}
          className="text-xs"
        >
          Reset
        </Button>
      </div>
      
      <div className="flex items-center gap-2">
        <Button
          size="sm"
          onClick={handleZoomOut}
          disabled={zoom <= 0.1}
          className="flex-1"
        >
          <ZoomOut className="w-4 h-4" />
          {showLabels && <span className="ml-1">Out</span>}
        </Button>
        
        <div className="flex-2 px-2">
          <input
            type="range"
            min="0.1"
            max="3"
            step="0.1"
            value={zoom}
            onChange={(e) => setZoom(Number(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
        </div>
        
        <Button
          size="sm"
          onClick={handleZoomIn}
          disabled={zoom >= 3}
          className="flex-1"
        >
          <ZoomIn className="w-4 h-4" />
          {showLabels && <span className="ml-1">In</span>}
        </Button>
      </div>
    </motion.div>
  );

  // Transform panel content
  const TransformPanel = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="bg-white rounded-xl shadow-lg border p-4 space-y-3"
    >
      <h3 className="text-sm font-medium text-gray-900">Transform Element</h3>
      
      <div className="grid grid-cols-2 gap-2">
        <Button
          size="sm"
          onClick={handleRotateElement}
          className="flex items-center gap-2"
        >
          <RotateCw className="w-4 h-4" />
          Rotate
        </Button>
        
        <Button
          size="sm"
          onClick={handleToggleVisibility}
          variant={selectedElement?.visible ? 'default' : 'outline'}
          className="flex items-center gap-2"
        >
          {selectedElement?.visible ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
          {selectedElement?.visible ? 'Hide' : 'Show'}
        </Button>
        
        <Button
          size="sm"
          onClick={handleToggleLock}
          variant={selectedElement?.locked ? 'default' : 'outline'}
          className="flex items-center gap-2"
        >
          {selectedElement?.locked ? <Lock className="w-4 h-4" /> : <Unlock className="w-4 h-4" />}
          {selectedElement?.locked ? 'Unlock' : 'Lock'}
        </Button>
        
        <Button
          size="sm"
          onClick={() => {
            if (selectedElement) {
              updateElement(selectedElement.id, { 
                x: canvas.width / 2 - selectedElement.width / 2,
                y: canvas.height / 2 - selectedElement.height / 2
              });
            }
          }}
          className="flex items-center gap-2"
        >
          <Square className="w-4 h-4" />
          Center
        </Button>
      </div>
    </motion.div>
  );

  // Layers panel content
  const LayersPanel = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="bg-white rounded-xl shadow-lg border p-4 space-y-3 max-h-60 overflow-y-auto"
    >
      <h3 className="text-sm font-medium text-gray-900">Layers</h3>
      
      <div className="space-y-2">
        {canvas.elements.map((element, index) => (
          <motion.div
            key={element.id}
            className={cn(
              'flex items-center gap-3 p-2 rounded-lg cursor-pointer transition-colors',
              selectedElementId === element.id 
                ? 'bg-blue-50 border border-blue-200' 
                : 'bg-gray-50 hover:bg-gray-100'
            )}
            onClick={() => selectElement(element.id)}
            whileTap={{ scale: 0.98 }}
          >
            <div className="flex items-center gap-2 flex-1">
              <div className="w-3 h-3 rounded bg-blue-500" />
              <span className="text-sm font-medium">
                {element.type === 'text' ? 'Text' : 'Image'} {index + 1}
              </span>
            </div>
            
            <div className="flex gap-1">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  updateElement(element.id, { visible: !element.visible });
                }}
                className="p-1 hover:bg-gray-200 rounded"
              >
                {element.visible ? 
                  <Eye className="w-3 h-3 text-gray-600" /> : 
                  <EyeOff className="w-3 h-3 text-gray-400" />
                }
              </button>
              
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  updateElement(element.id, { locked: !element.locked });
                }}
                className="p-1 hover:bg-gray-200 rounded"
              >
                {element.locked ? 
                  <Lock className="w-3 h-3 text-gray-600" /> : 
                  <Unlock className="w-3 h-3 text-gray-400" />
                }
              </button>
            </div>
          </motion.div>
        ))}
        
        {canvas.elements.length === 0 && (
          <div className="text-center py-4 text-gray-500 text-sm">
            No elements yet
          </div>
        )}
      </div>
    </motion.div>
  );

  const containerClasses = cn(
    'fixed z-40 transition-all duration-300',
    {
      'bottom-4 left-4 right-4': position === 'bottom',
      'right-4 top-1/2 transform -translate-y-1/2': position === 'side',
      'bottom-20 right-4': position === 'floating'
    },
    className
  );

  if (!isTouchDevice) return null;

  return (
    <div className={containerClasses}>
      {/* Expanded Panels */}
      <AnimatePresence>
        {isExpanded && activePanel && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="mb-4"
          >
            {activePanel === 'zoom' && <ZoomPanel />}
            {activePanel === 'transform' && <TransformPanel />}
            {activePanel === 'layers' && <LayersPanel />}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Control Bar */}
      <motion.div
        className="bg-white/90 backdrop-blur-sm rounded-full shadow-lg border border-gray-200 p-2"
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
      >
        <div className="flex items-center gap-1">
          {quickActions.map(action => {
            const Icon = action.icon;
            
            return (
              <motion.button
                key={action.id}
                onClick={action.action}
                disabled={action.disabled}
                className={cn(
                  'p-3 rounded-full transition-all duration-200 touch-target',
                  action.active 
                    ? 'bg-blue-500 text-white shadow-lg' 
                    : 'text-gray-600 hover:bg-gray-100',
                  action.disabled && 'opacity-40 cursor-not-allowed'
                )}
                whileHover={{ scale: action.disabled ? 1 : 1.1 }}
                whileTap={{ scale: action.disabled ? 1 : 0.9 }}
                title={action.label}
              >
                <Icon className="w-5 h-5" />
              </motion.button>
            );
          })}
          
          {/* Collapse/Expand Toggle */}
          <motion.button
            onClick={() => {
              setIsExpanded(!isExpanded);
              if (isExpanded) setActivePanel(null);
            }}
            className="p-3 rounded-full text-gray-600 hover:bg-gray-100 transition-colors touch-target"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            {isExpanded ? <Minimize2 className="w-5 h-5" /> : <Maximize2 className="w-5 h-5" />}
          </motion.button>
        </div>
      </motion.div>
    </div>
  );
};
