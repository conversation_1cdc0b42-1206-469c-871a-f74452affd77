'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { RotateCcw, RotateCw } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';
import { useEditorStore } from '@/stores/editorStore';
import { PlacementSide } from '@/types';
import { cn } from '@/lib/utils';

interface PlacementSelectorProps {
  className?: string;
}

const PLACEMENT_OPTIONS = [
  {
    id: 'front' as PlacementSide,
    name: 'Front',
    description: 'Center stage',
    icon: '👕',
    preview: 'The classic choice - your design takes center stage',
    gradient: 'from-purple-500 to-pink-500'
  },
  {
    id: 'back' as PlacementSide,
    name: 'Back',
    description: 'Bold statement',
    icon: '🔄',
    preview: 'Make an impact when you walk away',
    gradient: 'from-blue-500 to-cyan-500'
  },
  {
    id: 'left' as PlacementSide,
    name: 'Left Side',
    description: 'Subtle style',
    icon: '⬅️',
    preview: 'Understated elegance on the left',
    gradient: 'from-green-500 to-teal-500'
  },
  {
    id: 'right' as PlacementSide,
    name: 'Right Side',
    description: 'Unique touch',
    icon: '➡️',
    preview: 'A distinctive detail on the right',
    gradient: 'from-orange-500 to-red-500'
  }
];

export const PlacementSelector: React.FC<PlacementSelectorProps> = ({ className }) => {
  const { placement, setPlacement } = useEditorStore();

  const selectedOption = PLACEMENT_OPTIONS.find(option => option.id === placement);

  return (
    <Card className={cn('w-full', className)}>
      <CardContent className="p-4 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3">
          <div className={cn(
            'p-2 rounded-lg bg-gradient-to-r',
            selectedOption?.gradient || 'from-purple-500 to-pink-500'
          )}>
            <span className="text-lg">{selectedOption?.icon || '👕'}</span>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Choose your canvas</h3>
            <p className="text-sm text-gray-600">Where will your design live?</p>
          </div>
        </div>

        {/* Current Selection Preview */}
        <div className={cn(
          'p-4 rounded-xl bg-gradient-to-r text-white',
          selectedOption?.gradient || 'from-purple-500 to-pink-500'
        )}>
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-semibold text-lg">{selectedOption?.name}</h4>
              <p className="text-white/90 text-sm">{selectedOption?.description}</p>
            </div>
            <span className="text-3xl">{selectedOption?.icon}</span>
          </div>
          <p className="text-white/80 text-sm mt-2">
            {selectedOption?.preview}
          </p>
        </div>

        {/* Placement Options */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">Select placement</h4>
          <div className="grid grid-cols-2 gap-3">
            {PLACEMENT_OPTIONS.map((option) => (
              <motion.button
                key={option.id}
                onClick={() => setPlacement(option.id)}
                className={cn(
                  'p-4 rounded-xl border-2 text-left transition-all',
                  placement === option.id
                    ? 'border-purple-500 bg-purple-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                )}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-center gap-3">
                  <div className={cn(
                    'w-10 h-10 rounded-lg bg-gradient-to-r flex items-center justify-center',
                    option.gradient
                  )}>
                    <span className="text-lg">{option.icon}</span>
                  </div>
                  <div className="flex-1">
                    <h5 className="font-medium text-gray-900">{option.name}</h5>
                    <p className="text-sm text-gray-600">{option.description}</p>
                  </div>
                </div>
              </motion.button>
            ))}
          </div>
        </div>

        {/* 3D Preview Mockup */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">Preview your design</h4>
          <div className="relative bg-gray-100 rounded-xl p-6 min-h-[200px] flex items-center justify-center">
            {/* T-shirt mockup */}
            <div className="relative">
              <svg
                width="120"
                height="140"
                viewBox="0 0 120 140"
                className="text-gray-300"
                fill="currentColor"
              >
                {/* T-shirt shape */}
                <path d="M30 40 L30 25 Q30 20 35 20 L45 20 Q50 15 70 15 Q90 15 95 20 L105 20 Q110 20 110 25 L110 40 L120 50 L120 70 Q120 75 115 75 L110 75 L110 130 Q110 135 105 135 L35 135 Q30 135 30 130 L30 75 L25 75 Q20 75 20 70 L20 50 L30 40 Z" />
              </svg>
              
              {/* Design placement indicator */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className={cn(
                  'w-8 h-8 rounded border-2 border-dashed flex items-center justify-center text-xs font-medium',
                  placement === 'front' && 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 border-purple-500 text-purple-600',
                  placement === 'back' && 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 border-blue-500 text-blue-600',
                  placement === 'left' && 'top-1/2 left-1/4 transform -translate-x-1/2 -translate-y-1/2 border-green-500 text-green-600',
                  placement === 'right' && 'top-1/2 right-1/4 transform translate-x-1/2 -translate-y-1/2 border-orange-500 text-orange-600'
                )}>
                  {selectedOption?.icon}
                </div>
              </div>
            </div>
          </div>
          
          <p className="text-center text-sm text-gray-600">
            Your design will appear on the <span className="font-medium text-gray-900">{selectedOption?.name.toLowerCase()}</span>
          </p>
        </div>

        {/* Quick Actions */}
        <div className="flex gap-2">
          <motion.button
            onClick={() => {
              const currentIndex = PLACEMENT_OPTIONS.findIndex(opt => opt.id === placement);
              const prevIndex = currentIndex > 0 ? currentIndex - 1 : PLACEMENT_OPTIONS.length - 1;
              setPlacement(PLACEMENT_OPTIONS[prevIndex].id);
            }}
            className="flex-1 p-3 border-2 border-gray-200 rounded-lg hover:border-gray-300 transition-colors flex items-center justify-center gap-2"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <RotateCcw className="w-4 h-4" />
            <span className="text-sm font-medium">Previous</span>
          </motion.button>
          
          <motion.button
            onClick={() => {
              const currentIndex = PLACEMENT_OPTIONS.findIndex(opt => opt.id === placement);
              const nextIndex = currentIndex < PLACEMENT_OPTIONS.length - 1 ? currentIndex + 1 : 0;
              setPlacement(PLACEMENT_OPTIONS[nextIndex].id);
            }}
            className="flex-1 p-3 border-2 border-gray-200 rounded-lg hover:border-gray-300 transition-colors flex items-center justify-center gap-2"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <span className="text-sm font-medium">Next</span>
            <RotateCw className="w-4 h-4" />
          </motion.button>
        </div>

        {/* Inspirational Message */}
        <div className={cn(
          'p-4 rounded-lg bg-gradient-to-r',
          selectedOption?.gradient.replace('500', '50') || 'from-purple-50 to-pink-50'
        )}>
          <p className="text-sm font-medium text-center text-gray-800">
            ✨ Perfect placement makes your design unforgettable
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
