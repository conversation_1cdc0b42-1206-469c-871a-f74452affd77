'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Sparkles, 
  Heart,
  Star,
  Zap,
  X
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { 
  PRINT_SIZE_CATEGORIES, 
  getPricingImpactMessage,
  type PrintSizeCategory 
} from '@/lib/utils/areaCalculation';
import { cn } from '@/lib/utils';

interface PricingImpactIndicatorProps {
  currentArea: number;
  previousArea: number;
  currentCategory: PrintSizeCategory;
  className?: string;
  onDismiss?: () => void;
  autoHide?: boolean;
  autoHideDelay?: number;
}

export const PricingImpactIndicator: React.FC<PricingImpactIndicatorProps> = ({
  currentArea,
  previousArea,
  currentCategory,
  className,
  onDismiss,
  autoHide = true,
  autoHideDelay = 5000
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  // Calculate impact
  const impact = getPricingImpactMessage(previousArea, currentArea);
  const categoryInfo = PRINT_SIZE_CATEGORIES[currentCategory];

  // Show indicator when there's a pricing impact
  useEffect(() => {
    if (impact.hasImpact && previousArea > 0) {
      setIsVisible(true);
      
      if (autoHide) {
        const timer = setTimeout(() => {
          setIsVisible(false);
        }, autoHideDelay);
        
        return () => clearTimeout(timer);
      }
    }
  }, [impact.hasImpact, previousArea, autoHide, autoHideDelay]);

  const handleDismiss = () => {
    setIsVisible(false);
    onDismiss?.();
  };

  // Don't render if no impact or not visible
  if (!impact.hasImpact || !isVisible) {
    return null;
  }

  // Get styling based on impact type
  const getImpactStyling = () => {
    switch (impact.type) {
      case 'positive':
        return {
          bgColor: 'bg-gradient-to-r from-purple-500 to-pink-500',
          textColor: 'text-white',
          borderColor: 'border-purple-200',
          icon: TrendingUp,
          accentColor: 'text-purple-100',
        };
      case 'negative':
        return {
          bgColor: 'bg-gradient-to-r from-blue-500 to-cyan-500',
          textColor: 'text-white',
          borderColor: 'border-blue-200',
          icon: TrendingDown,
          accentColor: 'text-blue-100',
        };
      default:
        return {
          bgColor: 'bg-gradient-to-r from-gray-500 to-gray-600',
          textColor: 'text-white',
          borderColor: 'border-gray-200',
          icon: DollarSign,
          accentColor: 'text-gray-100',
        };
    }
  };

  const styling = getImpactStyling();
  const Icon = styling.icon;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -50, scale: 0.9 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -50, scale: 0.9 }}
        transition={{ 
          type: "spring", 
          stiffness: 300, 
          damping: 30 
        }}
        className={cn('fixed top-4 right-4 z-50 max-w-sm', className)}
      >
        <Card className={cn('overflow-hidden shadow-lg', styling.borderColor)}>
          <div className={cn('p-1', styling.bgColor)}>
            <CardContent className="p-4 space-y-3">
              {/* Header */}
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-white/20 rounded-lg">
                    <Icon className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h4 className={cn('font-semibold', styling.textColor)}>
                      {impact.type === 'positive' ? 'Bigger Impact!' : 'Smart Sizing!'}
                    </h4>
                    <p className={cn('text-sm', styling.accentColor)}>
                      {impact.message}
                    </p>
                  </div>
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDismiss}
                  className="text-white/80 hover:text-white hover:bg-white/20 p-1 h-auto"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              {/* Emotional Message */}
              <div className="bg-white/10 p-3 rounded-lg">
                <p className={cn('text-sm font-medium', styling.textColor)}>
                  {impact.emotionalMessage}
                </p>
              </div>

              {/* Category Info */}
              <div className="flex items-center justify-between">
                <div>
                  <p className={cn('text-xs', styling.accentColor)}>
                    Print Category
                  </p>
                  <p className={cn('font-medium', styling.textColor)}>
                    {categoryInfo.label}
                  </p>
                </div>
                
                <div className="text-right">
                  <p className={cn('text-xs', styling.accentColor)}>
                    Print Area
                  </p>
                  <p className={cn('font-medium', styling.textColor)}>
                    {currentArea.toFixed(1)} cm²
                  </p>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowDetails(!showDetails)}
                  className="flex-1 text-white/90 hover:text-white hover:bg-white/20 text-xs"
                >
                  <Sparkles className="w-3 h-3 mr-1" />
                  {showDetails ? 'Hide' : 'Show'} Details
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    // This would trigger pricing calculation
                    console.log('Calculate pricing for:', currentCategory);
                  }}
                  className="flex-1 text-white/90 hover:text-white hover:bg-white/20 text-xs"
                >
                  <DollarSign className="w-3 h-3 mr-1" />
                  See Pricing
                </Button>
              </div>

              {/* Detailed Information */}
              <AnimatePresence>
                {showDetails && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="border-t border-white/20 pt-3 space-y-2"
                  >
                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div>
                        <p className={cn('font-medium', styling.accentColor)}>
                          Previous Size
                        </p>
                        <p className={styling.textColor}>
                          {previousArea.toFixed(1)} cm²
                        </p>
                      </div>
                      <div>
                        <p className={cn('font-medium', styling.accentColor)}>
                          Change
                        </p>
                        <p className={styling.textColor}>
                          {impact.type === 'positive' ? '+' : ''}
                          {(currentArea - previousArea).toFixed(1)} cm²
                        </p>
                      </div>
                    </div>
                    
                    <div className="bg-white/10 p-2 rounded text-xs">
                      <p className={cn('font-medium mb-1', styling.textColor)}>
                        {categoryInfo.description}
                      </p>
                      <p className={styling.accentColor}>
                        Perfect for creating {impact.type === 'positive' ? 'bold statements' : 'refined details'} 
                        that express your unique style.
                      </p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </CardContent>
          </div>
        </Card>

        {/* Floating Action Indicators */}
        <AnimatePresence>
          {impact.type === 'positive' && (
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
              transition={{ delay: 0.5 }}
              className="absolute -top-2 -right-2"
            >
              <div className="bg-yellow-400 text-yellow-900 p-1 rounded-full">
                <Star className="w-4 h-4" />
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Pulse Effect for High Impact */}
        {currentArea > PRINT_SIZE_CATEGORIES.large.maxArea && (
          <motion.div
            className="absolute inset-0 rounded-lg border-2 border-white/50"
            animate={{
              scale: [1, 1.05, 1],
              opacity: [0.5, 0.8, 0.5],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        )}
      </motion.div>
    </AnimatePresence>
  );
};

// Hook for managing pricing impact state
export const usePricingImpact = () => {
  const [impactHistory, setImpactHistory] = useState<Array<{
    timestamp: number;
    previousArea: number;
    currentArea: number;
    impact: any;
  }>>([]);

  const addImpact = (previousArea: number, currentArea: number, impact: any) => {
    setImpactHistory(prev => [
      ...prev.slice(-4), // Keep last 5 impacts
      {
        timestamp: Date.now(),
        previousArea,
        currentArea,
        impact,
      }
    ]);
  };

  const clearHistory = () => {
    setImpactHistory([]);
  };

  return {
    impactHistory,
    addImpact,
    clearHistory,
    hasRecentImpacts: impactHistory.length > 0,
  };
};
