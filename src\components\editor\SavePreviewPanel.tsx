'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Save, 
  Eye, 
  Download, 
  Share2, 
  Heart,
  Sparkles,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useEditorStore } from '@/stores/editorStore';
import { useCustomizationAPI } from '@/hooks/useCustomizationAPI';
import { cn } from '@/lib/utils';

interface SavePreviewPanelProps {
  className?: string;
  productId?: string;
  userId?: string;
  onSaveSuccess?: (data: any) => void;
  onPreviewGenerated?: (data: any) => void;
}

export const SavePreviewPanel: React.FC<SavePreviewPanelProps> = ({
  className,
  productId = 'default-product',
  userId = 'default-user',
  onSaveSuccess,
  onPreviewGenerated
}) => {
  const { canvas, placement } = useEditorStore();
  const [customizationName, setCustomizationName] = useState('');
  const [showSaveForm, setShowSaveForm] = useState(false);
  
  const {
    isGeneratingPreview,
    isSaving,
    previewData,
    saveData,
    error,
    lastPreviewUrl,
    generatePreview,
    saveCustomization,
    clearError,
    hasPreview,
    hasSaved,
    isLoading
  } = useCustomizationAPI();

  const hasElements = canvas.elements.length > 0;

  const handleGeneratePreview = async () => {
    if (!hasElements) {
      alert('Please add some design elements first');
      return;
    }

    const result = await generatePreview(canvas, {
      format: 'png',
      quality: 85,
      includePricing: true,
    });

    if (result) {
      onPreviewGenerated?.(result);
    }
  };

  const handleSave = async () => {
    if (!hasElements) {
      alert('Please add some design elements first');
      return;
    }

    if (!customizationName.trim()) {
      alert('Please enter a name for your design');
      return;
    }

    const result = await saveCustomization(canvas, {
      name: customizationName.trim(),
      description: `Custom design with ${canvas.elements.length} elements`,
      productId,
      userId,
      placement,
      status: 'DRAFT',
      generatePreview: true,
    });

    if (result) {
      onSaveSuccess?.(result);
      setShowSaveForm(false);
      setCustomizationName('');
    }
  };

  const getStatusMessage = () => {
    if (error) {
      return {
        type: 'error' as const,
        message: error,
        icon: AlertCircle,
      };
    }

    if (hasSaved) {
      return {
        type: 'success' as const,
        message: 'Your design has been saved! ✨',
        icon: CheckCircle,
      };
    }

    if (hasPreview) {
      return {
        type: 'info' as const,
        message: 'Preview ready! Looking amazing! 🎨',
        icon: Eye,
      };
    }

    return null;
  };

  const statusMessage = getStatusMessage();

  return (
    <Card className={cn('overflow-hidden', className)}>
      <CardContent className="p-4 space-y-4">
        {/* Header */}
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
            <Sparkles className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">
              Ready to make it yours?
            </h3>
            <p className="text-sm text-gray-600">
              Preview and save your unique design
            </p>
          </div>
        </div>

        {/* Status Message */}
        <AnimatePresence>
          {statusMessage && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className={cn(
                'p-3 rounded-lg flex items-center gap-2',
                statusMessage.type === 'error' && 'bg-red-50 text-red-700 border border-red-200',
                statusMessage.type === 'success' && 'bg-green-50 text-green-700 border border-green-200',
                statusMessage.type === 'info' && 'bg-blue-50 text-blue-700 border border-blue-200'
              )}
            >
              <statusMessage.icon className="w-4 h-4" />
              <span className="text-sm font-medium">{statusMessage.message}</span>
              {error && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearError}
                  className="ml-auto text-xs"
                >
                  Dismiss
                </Button>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Preview Image */}
        <AnimatePresence>
          {lastPreviewUrl && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="relative"
            >
              <img
                src={lastPreviewUrl}
                alt="Design Preview"
                className="w-full h-32 object-contain bg-gray-50 rounded-lg border"
              />
              <div className="absolute top-2 right-2 bg-white/90 backdrop-blur-sm px-2 py-1 rounded text-xs font-medium text-gray-700">
                Preview
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Action Buttons */}
        <div className="space-y-2">
          {/* Preview Button */}
          <Button
            onClick={handleGeneratePreview}
            disabled={!hasElements || isLoading}
            className="w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600"
          >
            {isGeneratingPreview ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Creating preview...
              </>
            ) : (
              <>
                <Eye className="w-4 h-4 mr-2" />
                Generate Preview
              </>
            )}
          </Button>

          {/* Save Button */}
          <Button
            onClick={() => setShowSaveForm(true)}
            disabled={!hasElements || isLoading}
            variant="outline"
            className="w-full border-purple-200 text-purple-700 hover:bg-purple-50"
          >
            <Save className="w-4 h-4 mr-2" />
            Save Design
          </Button>
        </div>

        {/* Additional Actions */}
        {hasPreview && (
          <div className="flex gap-2 pt-2 border-t">
            <Button
              variant="outline"
              size="sm"
              className="flex-1 text-xs"
              onClick={() => {
                // Download functionality
                if (lastPreviewUrl) {
                  const link = document.createElement('a');
                  link.href = lastPreviewUrl;
                  link.download = 'my-design-preview.png';
                  link.click();
                }
              }}
            >
              <Download className="w-3 h-3 mr-1" />
              Download
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              className="flex-1 text-xs"
              onClick={() => {
                // Share functionality
                if (navigator.share && lastPreviewUrl) {
                  navigator.share({
                    title: 'My Custom Design',
                    text: 'Check out my custom design!',
                    url: lastPreviewUrl,
                  });
                }
              }}
            >
              <Share2 className="w-3 h-3 mr-1" />
              Share
            </Button>
          </div>
        )}

        {/* Design Stats */}
        {previewData && (
          <div className="bg-gray-50 p-3 rounded-lg text-xs space-y-1">
            <div className="flex justify-between">
              <span className="text-gray-600">Elements:</span>
              <span className="font-medium">{previewData.canvas.elementCount}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Print Area:</span>
              <span className="font-medium">{previewData.area.totalArea.toFixed(1)} cm²</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Category:</span>
              <span className="font-medium capitalize">{previewData.area.printSizeCategory}</span>
            </div>
          </div>
        )}
      </CardContent>

      {/* Save Form Modal */}
      <AnimatePresence>
        {showSaveForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowSaveForm(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-xl p-6 max-w-md w-full space-y-4"
            >
              <div className="text-center">
                <Heart className="w-12 h-12 text-pink-500 mx-auto mb-3" />
                <h3 className="text-xl font-bold text-gray-900">
                  Save Your Creation
                </h3>
                <p className="text-gray-600">
                  Give your design a name that captures its essence
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Design Name
                </label>
                <input
                  type="text"
                  value={customizationName}
                  onChange={(e) => setCustomizationName(e.target.value)}
                  placeholder="My Amazing Design"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  maxLength={100}
                />
                <p className="text-xs text-gray-500 mt-1">
                  {customizationName.length}/100 characters
                </p>
              </div>

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => setShowSaveForm(false)}
                  className="flex-1"
                  disabled={isSaving}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={!customizationName.trim() || isSaving}
                  className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Save Design
                    </>
                  )}
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  );
};
