'use client';

import React, { useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { SnapGuide, DesignElement, DesignCanvas } from '@/types';
import { generateSnapGuides, detectAlignment } from '@/utils/snapGuides';
import { cn } from '@/lib/utils';

interface SnapGuidesOverlayProps {
  canvas: DesignCanvas;
  elements: DesignElement[];
  selectedElementId: string | null;
  activeSnapGuides: SnapGuide[];
  zoom: number;
  showGrid?: boolean;
  gridSize?: number;
  className?: string;
}

export const SnapGuidesOverlay: React.FC<SnapGuidesOverlayProps> = ({
  canvas,
  elements,
  selectedElementId,
  activeSnapGuides,
  zoom,
  showGrid = true,
  gridSize = 20,
  className
}) => {
  // Generate all available snap guides
  const allSnapGuides = useMemo(() => {
    return generateSnapGuides(canvas, elements, selectedElementId || undefined);
  }, [canvas, elements, selectedElementId]);

  // Detect element alignments
  const alignments = useMemo(() => {
    return detectAlignment(elements.filter(el => el.visible));
  }, [elements]);

  // Grid pattern for background
  const gridPattern = useMemo(() => {
    if (!showGrid) return null;

    const lines = [];
    const scaledGridSize = gridSize * zoom;
    const canvasWidth = canvas.width * zoom;
    const canvasHeight = canvas.height * zoom;

    // Vertical lines
    for (let x = 0; x <= canvasWidth; x += scaledGridSize) {
      lines.push(
        <line
          key={`v-${x}`}
          x1={x}
          y1={0}
          x2={x}
          y2={canvasHeight}
          stroke="#f0f0f0"
          strokeWidth={0.5}
          opacity={0.5}
        />
      );
    }

    // Horizontal lines
    for (let y = 0; y <= canvasHeight; y += scaledGridSize) {
      lines.push(
        <line
          key={`h-${y}`}
          x1={0}
          y1={y}
          x2={canvasWidth}
          y2={y}
          stroke="#f0f0f0"
          strokeWidth={0.5}
          opacity={0.5}
        />
      );
    }

    return lines;
  }, [showGrid, gridSize, zoom, canvas.width, canvas.height]);

  // Render active snap guides
  const renderSnapGuides = () => {
    return activeSnapGuides.map(guide => {
      const isHorizontal = guide.type === 'horizontal';
      const position = guide.position * zoom;
      
      return (
        <motion.g
          key={guide.id}
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.8 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          {/* Main guide line */}
          <line
            x1={isHorizontal ? 0 : position}
            y1={isHorizontal ? position : 0}
            x2={isHorizontal ? canvas.width * zoom : position}
            y2={isHorizontal ? position : canvas.height * zoom}
            stroke={guide.color}
            strokeWidth={2}
            strokeDasharray="8,4"
            opacity={0.8}
          />
          
          {/* Guide label */}
          <motion.g
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.1, type: 'spring', stiffness: 300 }}
          >
            <rect
              x={isHorizontal ? 10 : position + 10}
              y={isHorizontal ? position - 15 : 10}
              width={60}
              height={20}
              fill={guide.color}
              rx={4}
              opacity={0.9}
            />
            <text
              x={isHorizontal ? 40 : position + 40}
              y={isHorizontal ? position - 2 : 23}
              fill="white"
              fontSize="10"
              textAnchor="middle"
              fontWeight="500"
            >
              {guide.type === 'center' ? 'CENTER' : 
               guide.type === 'edge' ? 'EDGE' : 
               guide.type.toUpperCase()}
            </text>
          </motion.g>
        </motion.g>
      );
    });
  };

  // Render alignment indicators
  const renderAlignmentIndicators = () => {
    const indicators = [];

    // Horizontal alignments
    alignments.horizontal.forEach((group, index) => {
      if (group.length < 2) return;
      
      const centerY = (group[0].y + group[0].height / 2) * zoom;
      const leftMost = Math.min(...group.map(el => el.x)) * zoom;
      const rightMost = Math.max(...group.map(el => el.x + el.width)) * zoom;
      
      indicators.push(
        <motion.g
          key={`h-align-${index}`}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 0.6, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <line
            x1={leftMost - 10}
            y1={centerY}
            x2={rightMost + 10}
            y2={centerY}
            stroke="#10B981"
            strokeWidth={1}
            strokeDasharray="4,2"
          />
          {group.map(element => (
            <circle
              key={element.id}
              cx={(element.x + element.width / 2) * zoom}
              cy={centerY}
              r={3}
              fill="#10B981"
              opacity={0.8}
            />
          ))}
        </motion.g>
      );
    });

    // Vertical alignments
    alignments.vertical.forEach((group, index) => {
      if (group.length < 2) return;
      
      const centerX = (group[0].x + group[0].width / 2) * zoom;
      const topMost = Math.min(...group.map(el => el.y)) * zoom;
      const bottomMost = Math.max(...group.map(el => el.y + el.height)) * zoom;
      
      indicators.push(
        <motion.g
          key={`v-align-${index}`}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 0.6, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <line
            x1={centerX}
            y1={topMost - 10}
            x2={centerX}
            y2={bottomMost + 10}
            stroke="#10B981"
            strokeWidth={1}
            strokeDasharray="4,2"
          />
          {group.map(element => (
            <circle
              key={element.id}
              cx={centerX}
              cy={(element.y + element.height / 2) * zoom}
              r={3}
              fill="#10B981"
              opacity={0.8}
            />
          ))}
        </motion.g>
      );
    });

    return indicators;
  };

  return (
    <div 
      className={cn(
        'absolute inset-0 pointer-events-none overflow-hidden',
        className
      )}
      style={{ 
        width: canvas.width * zoom, 
        height: canvas.height * zoom 
      }}
    >
      <svg
        width={canvas.width * zoom}
        height={canvas.height * zoom}
        className="absolute inset-0"
      >
        {/* Grid pattern */}
        {gridPattern}
        
        {/* Alignment indicators */}
        {renderAlignmentIndicators()}
        
        {/* Active snap guides */}
        <AnimatePresence>
          {renderSnapGuides()}
        </AnimatePresence>
      </svg>
      
      {/* Snap feedback indicator */}
      <AnimatePresence>
        {activeSnapGuides.length > 0 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: -10 }}
            className="absolute top-4 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg"
          >
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
              Snapped to guide
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Snap settings panel component
export const SnapSettingsPanel: React.FC<{
  snapEnabled: boolean;
  onSnapToggle: (enabled: boolean) => void;
  gridEnabled: boolean;
  onGridToggle: (enabled: boolean) => void;
  gridSize: number;
  onGridSizeChange: (size: number) => void;
  snapThreshold: number;
  onSnapThresholdChange: (threshold: number) => void;
  className?: string;
}> = ({
  snapEnabled,
  onSnapToggle,
  gridEnabled,
  onGridToggle,
  gridSize,
  onGridSizeChange,
  snapThreshold,
  onSnapThresholdChange,
  className
}) => {
  return (
    <div className={cn('bg-white p-4 rounded-lg shadow-lg border', className)}>
      <h3 className="text-sm font-semibold text-gray-900 mb-3">Snap Settings</h3>
      
      <div className="space-y-3">
        {/* Snap to guides toggle */}
        <div className="flex items-center justify-between">
          <label className="text-sm text-gray-700">Snap to Guides</label>
          <button
            onClick={() => onSnapToggle(!snapEnabled)}
            className={cn(
              'relative inline-flex h-5 w-9 items-center rounded-full transition-colors',
              snapEnabled ? 'bg-blue-500' : 'bg-gray-300'
            )}
          >
            <span
              className={cn(
                'inline-block h-3 w-3 transform rounded-full bg-white transition-transform',
                snapEnabled ? 'translate-x-5' : 'translate-x-1'
              )}
            />
          </button>
        </div>
        
        {/* Grid toggle */}
        <div className="flex items-center justify-between">
          <label className="text-sm text-gray-700">Show Grid</label>
          <button
            onClick={() => onGridToggle(!gridEnabled)}
            className={cn(
              'relative inline-flex h-5 w-9 items-center rounded-full transition-colors',
              gridEnabled ? 'bg-blue-500' : 'bg-gray-300'
            )}
          >
            <span
              className={cn(
                'inline-block h-3 w-3 transform rounded-full bg-white transition-transform',
                gridEnabled ? 'translate-x-5' : 'translate-x-1'
              )}
            />
          </button>
        </div>
        
        {/* Grid size */}
        <div>
          <label className="text-sm text-gray-700 block mb-1">Grid Size: {gridSize}px</label>
          <input
            type="range"
            min="10"
            max="50"
            value={gridSize}
            onChange={(e) => onGridSizeChange(Number(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
        </div>
        
        {/* Snap threshold */}
        <div>
          <label className="text-sm text-gray-700 block mb-1">Snap Sensitivity: {snapThreshold}px</label>
          <input
            type="range"
            min="5"
            max="25"
            value={snapThreshold}
            onChange={(e) => onSnapThresholdChange(Number(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
        </div>
      </div>
    </div>
  );
};
