'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, Filter, Heart, Sparkles, Zap, Star, Download } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { useEditorStore } from '@/stores/editorStore';
import { Template, TemplatePreview } from '@/types';
import { triggerHapticFeedback } from '@/utils/touchGestures';
import { cn } from '@/lib/utils';

interface TemplateGalleryProps {
  className?: string;
  onTemplateSelect?: (template: Template) => void;
  showCategories?: boolean;
  maxColumns?: number;
  compact?: boolean; // For use in editor sidebar
  limit?: number; // Limit number of templates shown
}

// Template data interface for API
interface ApiTemplate {
  id: string;
  name: string;
  description: string;
  moodTag: string;
  styleKeywords: string[];
  targetAudience: string;
  designData: any;
  previewImage: string;
  lifestyleContext: string[];
  usageCount: number;
  isFeatured: boolean;
  createdAt: string;
  product: {
    id: string;
    name: string;
    category: string;
    subcategory?: string;
    basePrice: number;
    heroImage: string;
    moodTags: string[];
    lifestyleTags: string[];
  };
}

// Fallback templates for offline/error states
const FALLBACK_TEMPLATES: Template[] = [
  {
    id: 'template-1',
    name: 'Bold Statement',
    description: 'Make a powerful impression with bold typography and striking colors',
    category: 'Typography',
    moodTag: 'Confident',
    styleKeywords: ['bold', 'modern', 'impactful'],
    targetAudience: 'young professionals',
    designData: {
      width: 400,
      height: 400,
      backgroundColor: '#000000',
      elements: [
        {
          id: 'text-1',
          type: 'text',
          x: 50,
          y: 150,
          width: 300,
          height: 100,
          rotation: 0,
          opacity: 1,
          visible: true,
          locked: false,
          data: {
            text: 'FEARLESS',
            fontSize: 48,
            fontFamily: 'Inter',
            fontWeight: 'bold',
            fontStyle: 'normal',
            color: '#FFFFFF',
            align: 'center',
            mood: 'bold'
          }
        }
      ],
      layers: ['text-1']
    },
    previewImage: '/templates/bold-statement.jpg',
    lifestyleContext: ['work', 'networking', 'presentations'],
    emotionalHook: 'Command attention and respect',
    isActive: true,
    isPremium: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'template-2',
    name: 'Elegant Minimalist',
    description: 'Sophisticated simplicity that speaks volumes',
    category: 'Minimalist',
    moodTag: 'Sophisticated',
    styleKeywords: ['elegant', 'clean', 'refined'],
    targetAudience: 'creative professionals',
    designData: {
      width: 400,
      height: 400,
      backgroundColor: '#F8F9FA',
      elements: [
        {
          id: 'text-2',
          type: 'text',
          x: 100,
          y: 180,
          width: 200,
          height: 40,
          rotation: 0,
          opacity: 1,
          visible: true,
          locked: false,
          data: {
            text: 'Refined',
            fontSize: 32,
            fontFamily: 'Playfair Display',
            fontWeight: 'normal',
            fontStyle: 'italic',
            color: '#2D3748',
            align: 'center',
            mood: 'elegant'
          }
        }
      ],
      layers: ['text-2']
    },
    previewImage: '/templates/elegant-minimalist.jpg',
    lifestyleContext: ['gallery', 'dinner', 'cultural events'],
    emotionalHook: 'Effortless sophistication',
    isActive: true,
    isPremium: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'template-3',
    name: 'Playful Energy',
    description: 'Vibrant and fun design that radiates positive energy',
    category: 'Playful',
    moodTag: 'Energetic',
    styleKeywords: ['fun', 'colorful', 'dynamic'],
    targetAudience: 'young creatives',
    designData: {
      width: 400,
      height: 400,
      backgroundColor: '#FF6B6B',
      elements: [
        {
          id: 'text-3',
          type: 'text',
          x: 80,
          y: 160,
          width: 240,
          height: 80,
          rotation: -5,
          opacity: 1,
          visible: true,
          locked: false,
          data: {
            text: 'SPARK JOY',
            fontSize: 36,
            fontFamily: 'Poppins',
            fontWeight: 'bold',
            fontStyle: 'normal',
            color: '#FFFFFF',
            align: 'center',
            mood: 'playful'
          }
        }
      ],
      layers: ['text-3']
    },
    previewImage: '/templates/playful-energy.jpg',
    lifestyleContext: ['festivals', 'parties', 'casual outings'],
    emotionalHook: 'Spread happiness wherever you go',
    isActive: true,
    isPremium: false,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

const CATEGORIES = ['All', 'Typography', 'Minimalist', 'Playful', 'Vintage', 'Modern'];
const MOOD_TAGS = ['All', 'Confident', 'Sophisticated', 'Energetic', 'Calm', 'Bold'];

// Template Preview Component with Emotional Context
export const TemplatePreviewCard: React.FC<{
  template: Template;
  onApply: (template: Template) => void;
  onFavorite: (templateId: string) => void;
  isFavorite: boolean;
  className?: string;
}> = ({ template, onApply, onFavorite, isFavorite, className }) => {
  const [isHovered, setIsHovered] = useState(false);
  const MoodIcon = getMoodIcon(template.moodTag);

  return (
    <motion.div
      className={cn('group', className)}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
        <CardContent className="p-0">
          {/* Emotional Preview */}
          <div className="relative aspect-square overflow-hidden">
            {/* Template Visual */}
            <div
              className="w-full h-full flex items-center justify-center text-2xl font-bold transition-transform duration-300 group-hover:scale-105"
              style={{
                backgroundColor: template.designData.backgroundColor,
                color: template.designData.elements[0]?.data?.color || '#000'
              }}
            >
              {template.designData.elements[0]?.data?.text || template.name}
            </div>

            {/* Emotional Overlay */}
            <AnimatePresence>
              {isHovered && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent flex flex-col justify-end p-4"
                >
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.1 }}
                    className="text-white space-y-2"
                  >
                    <p className="text-sm font-medium">Perfect for:</p>
                    <div className="flex flex-wrap gap-1">
                      {template.lifestyleContext.slice(0, 3).map(context => (
                        <span key={context} className="px-2 py-1 bg-white/20 rounded-full text-xs">
                          {context}
                        </span>
                      ))}
                    </div>
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Action Buttons */}
            <div className="absolute top-3 right-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => onFavorite(template.id)}
                className="p-2 bg-white/90 rounded-full shadow-lg"
              >
                <Heart
                  className={cn(
                    'w-4 h-4',
                    isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-600'
                  )}
                />
              </motion.button>
            </div>

            {/* Premium Badge */}
            {template.isPremium && (
              <div className="absolute top-3 left-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                ✨ Premium
              </div>
            )}
          </div>

          {/* Template Details */}
          <div className="p-4 space-y-3">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h3 className="font-bold text-gray-900 text-lg">{template.name}</h3>
                <p className="text-sm text-gray-600 mt-1">{template.description}</p>
              </div>
              <MoodIcon className="w-6 h-6 text-blue-500 flex-shrink-0 ml-2" />
            </div>

            {/* Emotional Hook */}
            <motion.div
              className="bg-gradient-to-r from-blue-50 to-purple-50 p-3 rounded-xl border border-blue-100"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <p className="text-blue-800 font-medium text-sm">
                💫 {template.emotionalHook}
              </p>
            </motion.div>

            {/* Style Tags */}
            <div className="flex flex-wrap gap-2">
              {template.styleKeywords.map(keyword => (
                <span
                  key={keyword}
                  className="px-3 py-1 bg-gray-100 text-gray-700 text-xs rounded-full font-medium"
                >
                  #{keyword}
                </span>
              ))}
            </div>

            {/* Apply Button */}
            <motion.button
              onClick={() => onApply(template)}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-3 px-4 rounded-xl shadow-lg transition-all duration-300"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <span className="flex items-center justify-center gap-2">
                <Sparkles className="w-4 h-4" />
                Make It Mine
              </span>
            </motion.button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export const TemplateGallery: React.FC<TemplateGalleryProps> = ({
  className,
  onTemplateSelect,
  showCategories = true,
  maxColumns = 3,
  compact = false,
  limit = compact ? 6 : 12
}) => {
  // State for API integration
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filter states
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedMood, setSelectedMood] = useState('All');
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [previewTemplate, setPreviewTemplate] = useState<Template | null>(null);

  const { loadCanvas, saveToHistory } = useEditorStore();

  // Fetch templates from API
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams({
          limit: limit.toString(),
          sortBy: 'popular',
          featured: compact ? 'true' : 'false', // Show featured in compact mode
        });

        const response = await fetch(`/api/templates?${params}`);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch templates');
        }

        if (data.success) {
          // Convert API templates to component format
          const convertedTemplates: Template[] = data.data.templates.map((apiTemplate: ApiTemplate) => ({
            id: apiTemplate.id,
            name: apiTemplate.name,
            description: apiTemplate.description,
            category: apiTemplate.product.category,
            moodTag: apiTemplate.moodTag,
            styleKeywords: apiTemplate.styleKeywords,
            targetAudience: apiTemplate.targetAudience,
            designData: apiTemplate.designData,
            previewImage: apiTemplate.previewImage,
            lifestyleContext: apiTemplate.lifestyleContext,
            emotionalHook: `Perfect for ${apiTemplate.targetAudience} who want to feel ${apiTemplate.moodTag.toLowerCase()}`,
            isActive: true,
            isPremium: false,
            createdAt: new Date(apiTemplate.createdAt),
            updatedAt: new Date(apiTemplate.createdAt),
          }));

          setTemplates(convertedTemplates);
        } else {
          throw new Error(data.error || 'Failed to fetch templates');
        }
      } catch (err) {
        console.error('Error fetching templates:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch templates');
        // Use fallback templates on error
        setTemplates(FALLBACK_TEMPLATES.slice(0, limit));
      } finally {
        setLoading(false);
      }
    };

    fetchTemplates();
  }, [limit, compact]);

  // Filter templates based on search and filters
  const filteredTemplates = useMemo(() => {
    return templates.filter(template => {
      const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           template.styleKeywords.some(keyword =>
                             keyword.toLowerCase().includes(searchQuery.toLowerCase())
                           );

      const matchesCategory = selectedCategory === 'All' || template.category === selectedCategory;
      const matchesMood = selectedMood === 'All' || template.moodTag === selectedMood;

      return matchesSearch && matchesCategory && matchesMood && template.isActive;
    });
  }, [templates, searchQuery, selectedCategory, selectedMood]);

  const handleTemplateApply = async (template: Template) => {
    try {
      // Apply template to canvas
      loadCanvas(template.designData);
      saveToHistory();
      triggerHapticFeedback('medium');
      onTemplateSelect?.(template);

      // Track template usage via API (fire and forget)
      if (!compact) { // Only track in full gallery, not in compact editor mode
        fetch(`/api/templates/${template.id}/apply`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: 'current-user-id', // Replace with actual user ID
            customizationName: `${template.name} - ${new Date().toLocaleDateString()}`,
          }),
        }).catch(err => {
          console.warn('Failed to track template usage:', err);
        });
      }
    } catch (error) {
      console.error('Error applying template:', error);
      // Still apply the template even if tracking fails
      loadCanvas(template.designData);
      saveToHistory();
      triggerHapticFeedback('medium');
      onTemplateSelect?.(template);
    }
  };

  const handleTemplatePreview = (template: Template) => {
    setPreviewTemplate(template);
    triggerHapticFeedback('light');
  };

  const toggleFavorite = (templateId: string) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(templateId)) {
      newFavorites.delete(templateId);
    } else {
      newFavorites.add(templateId);
    }
    setFavorites(newFavorites);
    triggerHapticFeedback('selection');
  };

  const getMoodIcon = (mood: string) => {
    switch (mood.toLowerCase()) {
      case 'confident': return Zap;
      case 'sophisticated': return Star;
      case 'energetic': return Sparkles;
      default: return Heart;
    }
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      {!compact && (
        <div className="text-center space-y-2">
          <h2 className="text-2xl font-bold text-gray-900">
            ✨ Ready-Made Templates
          </h2>
          <p className="text-gray-600">
            Start with inspiration, make it uniquely yours
          </p>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <motion.div
            className="w-8 h-8 border-4 border-primary-200 border-t-primary-500 rounded-full"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          />
          <span className="ml-3 text-gray-600">Loading templates...</span>
        </div>
      )}

      {/* Error State */}
      {error && !loading && (
        <div className="text-center py-8">
          <div className="text-red-600 mb-4">⚠️ {error}</div>
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            size="sm"
          >
            Try Again
          </Button>
        </div>
      )}

      {/* Search and Filters */}
      {!loading && !error && (
        <div className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Filters */}
        {showCategories && (
          <div className="flex flex-wrap gap-2">
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-700">Category:</span>
            </div>
            {CATEGORIES.map(category => (
              <Button
                key={category}
                variant={selectedCategory === category ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="text-xs"
              >
                {category}
              </Button>
            ))}
          </div>
        )}

        {/* Mood Filter */}
        <div className="flex flex-wrap gap-2">
          <div className="flex items-center gap-2">
            <Heart className="w-4 h-4 text-gray-500" />
            <span className="text-sm text-gray-700">Mood:</span>
          </div>
          {MOOD_TAGS.map(mood => (
            <Button
              key={mood}
              variant={selectedMood === mood ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedMood(mood)}
              className="text-xs"
            >
              {mood}
            </Button>
          ))}
        </div>
        </div>
      )}

      {/* Templates Grid */}
      {!loading && !error && (
      <div className={cn(
        'grid gap-6',
        `grid-cols-1 md:grid-cols-2 lg:grid-cols-${Math.min(maxColumns, 3)}`
      )}>
        <AnimatePresence>
          {filteredTemplates.map((template, index) => (
            <motion.div
              key={template.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.1 }}
              layout
            >
              <TemplatePreviewCard
                template={template}
                onApply={handleTemplateApply}
                onFavorite={toggleFavorite}
                isFavorite={favorites.has(template.id)}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

        {/* Empty State */}
        {filteredTemplates.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🎨</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No templates found</h3>
            <p className="text-gray-600">Try adjusting your search or filters</p>
          </div>
        )}
      )}

      {/* Template Preview Modal */}
      <AnimatePresence>
        {previewTemplate && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setPreviewTemplate(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-xl p-6 max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-xl font-bold mb-4">{previewTemplate.name}</h3>
              <p className="text-gray-600 mb-4">{previewTemplate.description}</p>
              <div className="flex gap-3">
                <Button
                  onClick={() => {
                    handleTemplateApply(previewTemplate);
                    setPreviewTemplate(null);
                  }}
                  className="flex-1"
                >
                  Apply Template
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setPreviewTemplate(null)}
                >
                  Cancel
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
