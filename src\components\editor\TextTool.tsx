'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Type, Bold, Italic, AlignLeft, AlignCenter, AlignRight, Palette } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent } from '@/components/ui/Card';
import { useEditorStore } from '@/stores/editorStore';
import { TextElementData } from '@/types';
import { cn } from '@/lib/utils';

interface TextToolProps {
  className?: string;
}

const FONT_MOODS = [
  {
    id: 'bold',
    name: 'Bold & Confident',
    description: 'Make a statement',
    fontFamily: 'Inter',
    preview: 'STRONG',
    emotion: '💪'
  },
  {
    id: 'elegant',
    name: 'Elegant & Refined',
    description: 'Timeless sophistication',
    fontFamily: 'Playfair Display',
    preview: 'Elegant',
    emotion: '✨'
  },
  {
    id: 'playful',
    name: 'Playful & Fun',
    description: 'Express your joy',
    fontFamily: 'Poppins',
    preview: 'Playful!',
    emotion: '🎉'
  }
];

const COLORS = [
  '#000000', '#FFFFFF', '#FF6B6B', '#4ECDC4', '#45B7D1', 
  '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'
];

export const TextTool: React.FC<TextToolProps> = ({ className }) => {
  const [textInput, setTextInput] = useState('Your text here');
  const [selectedMood, setSelectedMood] = useState<'bold' | 'elegant' | 'playful'>('bold');
  const [selectedColor, setSelectedColor] = useState('#000000');
  const [fontSize, setFontSize] = useState(24);
  const [alignment, setAlignment] = useState<'left' | 'center' | 'right'>('left');
  const [fontWeight, setFontWeight] = useState<'normal' | 'bold'>('normal');
  const [fontStyle, setFontStyle] = useState<'normal' | 'italic'>('normal');

  const { 
    addText, 
    selectedElementId, 
    updateTextData, 
    canvas,
    setActiveTool 
  } = useEditorStore();

  const selectedElement = canvas.elements.find(el => el.id === selectedElementId);
  const isTextSelected = selectedElement?.type === 'text';

  // Update selected text element when properties change
  React.useEffect(() => {
    if (isTextSelected && selectedElementId) {
      updateTextData(selectedElementId, {
        fontSize,
        color: selectedColor,
        align: alignment,
        fontWeight,
        fontStyle,
        mood: selectedMood
      });
    }
  }, [fontSize, selectedColor, alignment, fontWeight, fontStyle, selectedMood, isTextSelected, selectedElementId, updateTextData]);

  const handleAddText = () => {
    const centerX = canvas.width / 2 - 100;
    const centerY = canvas.height / 2 - 15;
    
    addText(textInput, centerX, centerY);
    setActiveTool('select');
  };

  const handleTextChange = (newText: string) => {
    setTextInput(newText);
    if (isTextSelected && selectedElementId) {
      updateTextData(selectedElementId, { text: newText });
    }
  };

  return (
    <Card className={cn('w-full', className)}>
      <CardContent className="p-4 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
            <Type className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Place your mark</h3>
            <p className="text-sm text-gray-600">Express yourself with words</p>
          </div>
        </div>

        {/* Text Input */}
        <div className="space-y-2">
          <Input
            value={textInput}
            onChange={(e) => handleTextChange(e.target.value)}
            placeholder="What's your message?"
            className="text-lg"
          />
          {!isTextSelected && (
            <Button 
              onClick={handleAddText}
              className="w-full"
              size="lg"
            >
              ✨ Add Text to Canvas
            </Button>
          )}
        </div>

        {/* Font Moods */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">Choose your vibe</h4>
          <div className="grid grid-cols-1 gap-2">
            {FONT_MOODS.map((mood) => (
              <motion.button
                key={mood.id}
                onClick={() => setSelectedMood(mood.id as any)}
                className={cn(
                  'p-3 rounded-lg border-2 text-left transition-all',
                  selectedMood === mood.id
                    ? 'border-purple-500 bg-purple-50'
                    : 'border-gray-200 hover:border-gray-300'
                )}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="text-lg">{mood.emotion}</span>
                      <span className="font-medium text-gray-900">{mood.name}</span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{mood.description}</p>
                  </div>
                  <div 
                    className="text-xl font-medium"
                    style={{ fontFamily: mood.fontFamily }}
                  >
                    {mood.preview}
                  </div>
                </div>
              </motion.button>
            ))}
          </div>
        </div>

        {/* Text Controls */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900">Fine-tune your style</h4>
          
          {/* Size and Style */}
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Size</label>
              <Input
                type="number"
                value={fontSize}
                onChange={(e) => setFontSize(Number(e.target.value))}
                min="8"
                max="120"
                className="text-center"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Style</label>
              <div className="flex gap-1">
                <Button
                  variant={fontWeight === 'bold' ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setFontWeight(fontWeight === 'bold' ? 'normal' : 'bold')}
                  className="flex-1"
                >
                  <Bold className="w-4 h-4" />
                </Button>
                <Button
                  variant={fontStyle === 'italic' ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setFontStyle(fontStyle === 'italic' ? 'normal' : 'italic')}
                  className="flex-1"
                >
                  <Italic className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Alignment */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Alignment</label>
            <div className="flex gap-1">
              <Button
                variant={alignment === 'left' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setAlignment('left')}
                className="flex-1"
              >
                <AlignLeft className="w-4 h-4" />
              </Button>
              <Button
                variant={alignment === 'center' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setAlignment('center')}
                className="flex-1"
              >
                <AlignCenter className="w-4 h-4" />
              </Button>
              <Button
                variant={alignment === 'right' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setAlignment('right')}
                className="flex-1"
              >
                <AlignRight className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Colors */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Color</label>
            <div className="grid grid-cols-5 gap-2">
              {COLORS.map((color) => (
                <button
                  key={color}
                  onClick={() => setSelectedColor(color)}
                  className={cn(
                    'w-10 h-10 rounded-lg border-2 transition-all',
                    selectedColor === color
                      ? 'border-purple-500 scale-110'
                      : 'border-gray-200 hover:border-gray-300'
                  )}
                  style={{ backgroundColor: color }}
                >
                  {color === '#FFFFFF' && (
                    <div className="w-full h-full rounded-md border border-gray-300" />
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Inspirational Message */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg">
          <p className="text-sm text-purple-800 font-medium text-center">
            💫 Every word tells your story. Make it unforgettable.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
