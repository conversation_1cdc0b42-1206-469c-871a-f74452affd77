'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence, PanInfo } from 'framer-motion';
import { MoreHorizontal, Copy, Trash2, Eye, EyeOff, Lock, Unlock, RotateCw } from 'lucide-react';
import { useEditorStore } from '@/stores/editorStore';
import { DesignElement } from '@/types';
import { triggerHapticFeedback } from '@/utils/touchGestures';
import { cn } from '@/lib/utils';

interface TouchElementSelectorProps {
  element: DesignElement;
  isSelected: boolean;
  onSelect: () => void;
  onDeselect: () => void;
  zoom: number;
  className?: string;
}

export const TouchElementSelector: React.FC<TouchElementSelectorProps> = ({
  element,
  isSelected,
  onSelect,
  onDeselect,
  zoom,
  className
}) => {
  const [showActions, setShowActions] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null);
  const actionsPanelRef = useRef<HTMLDivElement>(null);

  const {
    updateElement,
    deleteElement,
    duplicateElement,
    saveToHistory
  } = useEditorStore();

  // Handle long press for context menu
  const handleTouchStart = () => {
    const timer = setTimeout(() => {
      setShowActions(true);
      triggerHapticFeedback('medium');
    }, 500); // 500ms long press
    
    setLongPressTimer(timer);
  };

  const handleTouchEnd = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
  };

  // Close actions panel when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (actionsPanelRef.current && !actionsPanelRef.current.contains(event.target as Node)) {
        setShowActions(false);
      }
    };

    if (showActions) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [showActions]);

  const handleDuplicate = () => {
    duplicateElement(element.id);
    setShowActions(false);
    triggerHapticFeedback('selection');
    saveToHistory();
  };

  const handleDelete = () => {
    deleteElement(element.id);
    setShowActions(false);
    triggerHapticFeedback('heavy');
    saveToHistory();
  };

  const handleToggleVisibility = () => {
    updateElement(element.id, { visible: !element.visible });
    setShowActions(false);
    triggerHapticFeedback('selection');
    saveToHistory();
  };

  const handleToggleLock = () => {
    updateElement(element.id, { locked: !element.locked });
    setShowActions(false);
    triggerHapticFeedback('medium');
    saveToHistory();
  };

  const handleRotate = () => {
    const newRotation = (element.rotation + 15) % 360;
    updateElement(element.id, { rotation: newRotation });
    setShowActions(false);
    triggerHapticFeedback('selection');
    saveToHistory();
  };

  const actions = [
    {
      id: 'duplicate',
      icon: Copy,
      label: 'Duplicate',
      action: handleDuplicate,
      color: 'text-blue-600'
    },
    {
      id: 'rotate',
      icon: RotateCw,
      label: 'Rotate',
      action: handleRotate,
      color: 'text-green-600'
    },
    {
      id: 'visibility',
      icon: element.visible ? Eye : EyeOff,
      label: element.visible ? 'Hide' : 'Show',
      action: handleToggleVisibility,
      color: element.visible ? 'text-yellow-600' : 'text-gray-600'
    },
    {
      id: 'lock',
      icon: element.locked ? Unlock : Lock,
      label: element.locked ? 'Unlock' : 'Lock',
      action: handleToggleLock,
      color: element.locked ? 'text-orange-600' : 'text-purple-600'
    },
    {
      id: 'delete',
      icon: Trash2,
      label: 'Delete',
      action: handleDelete,
      color: 'text-red-600'
    }
  ];

  const elementStyle = {
    left: element.x * zoom,
    top: element.y * zoom,
    width: element.width * zoom,
    height: element.height * zoom,
    transform: `rotate(${element.rotation}deg)`,
    opacity: element.visible ? element.opacity : 0.5,
  };

  return (
    <>
      {/* Element Overlay */}
      <motion.div
        className={cn(
          'absolute pointer-events-auto cursor-pointer',
          isSelected && 'ring-2 ring-blue-500 ring-offset-2',
          element.locked && 'cursor-not-allowed',
          className
        )}
        style={elementStyle}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        onMouseDown={handleTouchStart}
        onMouseUp={handleTouchEnd}
        onClick={isSelected ? onDeselect : onSelect}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        animate={{
          scale: isSelected ? 1.05 : 1,
          zIndex: isSelected ? 10 : 1
        }}
        transition={{ duration: 0.2 }}
      >
        {/* Selection Indicator */}
        {isSelected && (
          <motion.div
            className="absolute inset-0 border-2 border-blue-500 rounded-lg pointer-events-none"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
          >
            {/* Corner Handles */}
            <div className="absolute -top-1 -left-1 w-3 h-3 bg-blue-500 rounded-full" />
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full" />
            <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-blue-500 rounded-full" />
            <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 rounded-full" />
            
            {/* Element Info */}
            <div className="absolute -top-8 left-0 bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">
              {element.type === 'text' ? 'Text' : 'Image'}
              {element.locked && ' 🔒'}
            </div>
          </motion.div>
        )}

        {/* Quick Action Button */}
        {isSelected && !element.locked && (
          <motion.button
            className="absolute -top-3 -right-3 w-8 h-8 bg-white rounded-full shadow-lg border border-gray-200 flex items-center justify-center text-gray-600 hover:text-gray-900 z-20"
            onClick={(e) => {
              e.stopPropagation();
              setShowActions(!showActions);
              triggerHapticFeedback('selection');
            }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.1 }}
          >
            <MoreHorizontal className="w-4 h-4" />
          </motion.button>
        )}
      </motion.div>

      {/* Actions Panel */}
      <AnimatePresence>
        {showActions && isSelected && (
          <motion.div
            ref={actionsPanelRef}
            className="fixed z-50"
            style={{
              left: Math.min(
                (element.x + element.width) * zoom + 10,
                window.innerWidth - 200
              ),
              top: Math.max(
                element.y * zoom,
                10
              )
            }}
            initial={{ opacity: 0, scale: 0.8, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: -10 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          >
            <div className="bg-white rounded-xl shadow-xl border border-gray-200 p-2 min-w-[160px]">
              <div className="space-y-1">
                {actions.map((action, index) => {
                  const Icon = action.icon;
                  
                  return (
                    <motion.button
                      key={action.id}
                      onClick={action.action}
                      className={cn(
                        'w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors hover:bg-gray-50',
                        action.color
                      )}
                      whileHover={{ x: 4 }}
                      whileTap={{ scale: 0.98 }}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                    >
                      <Icon className="w-4 h-4 flex-shrink-0" />
                      <span className="text-sm font-medium">{action.label}</span>
                    </motion.button>
                  );
                })}
              </div>
              
              {/* Close Button */}
              <div className="border-t border-gray-100 mt-2 pt-2">
                <button
                  onClick={() => setShowActions(false)}
                  className="w-full text-center text-xs text-gray-500 py-1 hover:text-gray-700 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

// Touch-optimized multi-select component
export const TouchMultiSelector: React.FC<{
  elements: DesignElement[];
  selectedIds: string[];
  onSelectionChange: (ids: string[]) => void;
  zoom: number;
  className?: string;
}> = ({ elements, selectedIds, onSelectionChange, zoom, className }) => {
  const [selectionBox, setSelectionBox] = useState<{
    start: { x: number; y: number };
    end: { x: number; y: number };
    active: boolean;
  }>({ start: { x: 0, y: 0 }, end: { x: 0, y: 0 }, active: false });

  const handlePanStart = (event: any, info: PanInfo) => {
    setSelectionBox({
      start: { x: info.point.x, y: info.point.y },
      end: { x: info.point.x, y: info.point.y },
      active: true
    });
  };

  const handlePan = (event: any, info: PanInfo) => {
    setSelectionBox(prev => ({
      ...prev,
      end: { x: info.point.x, y: info.point.y }
    }));
  };

  const handlePanEnd = () => {
    // Calculate which elements are within selection box
    const box = selectionBox;
    const selectedElements = elements.filter(element => {
      const elementBounds = {
        left: element.x * zoom,
        top: element.y * zoom,
        right: (element.x + element.width) * zoom,
        bottom: (element.y + element.height) * zoom
      };
      
      const selectionBounds = {
        left: Math.min(box.start.x, box.end.x),
        top: Math.min(box.start.y, box.end.y),
        right: Math.max(box.start.x, box.end.x),
        bottom: Math.max(box.start.y, box.end.y)
      };
      
      return (
        elementBounds.left < selectionBounds.right &&
        elementBounds.right > selectionBounds.left &&
        elementBounds.top < selectionBounds.bottom &&
        elementBounds.bottom > selectionBounds.top
      );
    });
    
    onSelectionChange(selectedElements.map(el => el.id));
    setSelectionBox(prev => ({ ...prev, active: false }));
    
    if (selectedElements.length > 0) {
      triggerHapticFeedback('selection');
    }
  };

  return (
    <motion.div
      className={cn('absolute inset-0 pointer-events-auto', className)}
      onPanStart={handlePanStart}
      onPan={handlePan}
      onPanEnd={handlePanEnd}
    >
      {/* Selection Box */}
      {selectionBox.active && (
        <motion.div
          className="absolute border-2 border-blue-500 bg-blue-500/10 pointer-events-none"
          style={{
            left: Math.min(selectionBox.start.x, selectionBox.end.x),
            top: Math.min(selectionBox.start.y, selectionBox.end.y),
            width: Math.abs(selectionBox.end.x - selectionBox.start.x),
            height: Math.abs(selectionBox.end.y - selectionBox.start.y)
          }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        />
      )}
    </motion.div>
  );
};
