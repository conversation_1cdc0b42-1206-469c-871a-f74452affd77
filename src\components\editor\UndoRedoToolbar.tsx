'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Undo, Redo, History, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { useEditorStore } from '@/stores/editorStore';
import { cn } from '@/lib/utils';
import { triggerHapticFeedback } from '@/utils/touchGestures';

interface UndoRedoToolbarProps {
  className?: string;
  showLabels?: boolean;
  orientation?: 'horizontal' | 'vertical';
  size?: 'sm' | 'md' | 'lg';
}

export const UndoRedoToolbar: React.FC<UndoRedoToolbarProps> = ({
  className,
  showLabels = false,
  orientation = 'horizontal',
  size = 'md'
}) => {
  const {
    undo,
    redo,
    history,
    historyIndex,
    clearCanvas
  } = useEditorStore();

  const [showFeedback, setShowFeedback] = useState<{
    type: 'undo' | 'redo' | null;
    message: string;
  }>({ type: null, message: '' });

  const canUndo = historyIndex > 0;
  const canRedo = historyIndex < history.length - 1;

  // Auto-hide feedback after 2 seconds
  useEffect(() => {
    if (showFeedback.type) {
      const timer = setTimeout(() => {
        setShowFeedback({ type: null, message: '' });
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [showFeedback]);

  const handleUndo = () => {
    if (canUndo) {
      undo();
      triggerHapticFeedback('medium');
      setShowFeedback({
        type: 'undo',
        message: 'Undid last action'
      });
    }
  };

  const handleRedo = () => {
    if (canRedo) {
      redo();
      triggerHapticFeedback('medium');
      setShowFeedback({
        type: 'redo',
        message: 'Redid action'
      });
    }
  };

  const handleClear = () => {
    clearCanvas();
    triggerHapticFeedback('heavy');
    setShowFeedback({
      type: null,
      message: 'Canvas cleared'
    });
  };

  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12'
  };

  const iconSizes = {
    sm: 16,
    md: 20,
    lg: 24
  };

  const containerClasses = cn(
    'flex gap-1 p-1 bg-white/90 backdrop-blur-sm rounded-lg shadow-lg border border-gray-200',
    orientation === 'vertical' ? 'flex-col' : 'flex-row',
    className
  );

  return (
    <div className="relative">
      <div className={containerClasses}>
        {/* Undo Button */}
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Button
            variant={canUndo ? 'default' : 'ghost'}
            size="sm"
            onClick={handleUndo}
            disabled={!canUndo}
            className={cn(
              sizeClasses[size],
              'relative overflow-hidden',
              canUndo 
                ? 'bg-blue-500 hover:bg-blue-600 text-white' 
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            )}
            title={canUndo ? 'Undo last action' : 'Nothing to undo'}
          >
            <Undo size={iconSizes[size]} />
            {showLabels && (
              <span className="ml-2 text-xs">Undo</span>
            )}
            
            {/* Ripple effect for touch feedback */}
            {showFeedback.type === 'undo' && (
              <motion.div
                className="absolute inset-0 bg-white/30 rounded"
                initial={{ scale: 0, opacity: 1 }}
                animate={{ scale: 2, opacity: 0 }}
                transition={{ duration: 0.6 }}
              />
            )}
          </Button>
        </motion.div>

        {/* Redo Button */}
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Button
            variant={canRedo ? 'default' : 'ghost'}
            size="sm"
            onClick={handleRedo}
            disabled={!canRedo}
            className={cn(
              sizeClasses[size],
              'relative overflow-hidden',
              canRedo 
                ? 'bg-green-500 hover:bg-green-600 text-white' 
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            )}
            title={canRedo ? 'Redo last undone action' : 'Nothing to redo'}
          >
            <Redo size={iconSizes[size]} />
            {showLabels && (
              <span className="ml-2 text-xs">Redo</span>
            )}
            
            {/* Ripple effect for touch feedback */}
            {showFeedback.type === 'redo' && (
              <motion.div
                className="absolute inset-0 bg-white/30 rounded"
                initial={{ scale: 0, opacity: 1 }}
                animate={{ scale: 2, opacity: 0 }}
                transition={{ duration: 0.6 }}
              />
            )}
          </Button>
        </motion.div>

        {/* History Indicator */}
        <div className="flex items-center justify-center px-2">
          <div className="flex items-center gap-1">
            <History size={12} className="text-gray-400" />
            <span className="text-xs text-gray-500">
              {historyIndex + 1}/{history.length}
            </span>
          </div>
        </div>

        {/* Clear Canvas Button */}
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClear}
            className={cn(
              sizeClasses[size],
              'text-red-500 hover:bg-red-50 hover:text-red-600'
            )}
            title="Clear canvas"
          >
            <RotateCcw size={iconSizes[size]} />
            {showLabels && (
              <span className="ml-2 text-xs">Clear</span>
            )}
          </Button>
        </motion.div>
      </div>

      {/* Feedback Toast */}
      <AnimatePresence>
        {showFeedback.message && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.9 }}
            className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-black/80 text-white px-3 py-1 rounded-full text-xs whitespace-nowrap z-50"
          >
            {showFeedback.message}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black/80" />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Keyboard shortcut hook for undo/redo
export const useUndoRedoShortcuts = () => {
  const { undo, redo } = useEditorStore();

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+Z or Cmd+Z for undo
      if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        undo();
      }
      
      // Ctrl+Y or Cmd+Shift+Z for redo
      if (
        ((e.ctrlKey || e.metaKey) && e.key === 'y') ||
        ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'z')
      ) {
        e.preventDefault();
        redo();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [undo, redo]);
};

// History visualization component
export const HistoryTimeline: React.FC<{
  className?: string;
  maxVisible?: number;
}> = ({ className, maxVisible = 10 }) => {
  const { history, historyIndex } = useEditorStore();

  const visibleHistory = history.slice(
    Math.max(0, historyIndex - maxVisible + 1),
    historyIndex + 1
  );

  return (
    <div className={cn('flex items-center gap-1', className)}>
      {visibleHistory.map((_, index) => {
        const actualIndex = Math.max(0, historyIndex - maxVisible + 1) + index;
        const isCurrent = actualIndex === historyIndex;
        
        return (
          <motion.div
            key={actualIndex}
            className={cn(
              'w-2 h-2 rounded-full transition-colors',
              isCurrent ? 'bg-blue-500' : 'bg-gray-300'
            )}
            animate={{
              scale: isCurrent ? 1.2 : 1,
              opacity: isCurrent ? 1 : 0.6
            }}
            transition={{ duration: 0.2 }}
          />
        );
      })}
    </div>
  );
};
