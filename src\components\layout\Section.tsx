'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn, MotionProps } from '@/lib/utils';
import { Container } from './Container';

export interface SectionProps extends React.HTMLAttributes<HTMLElement> {
  variant?: 'default' | 'primary' | 'secondary' | 'glass';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  containerSize?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  animate?: boolean;
}

const sectionVariants = {
  default: 'bg-white',
  primary: 'bg-gradient-warm',
  secondary: 'bg-gray-50',
  glass: 'bg-white/20 backdrop-blur-md',
};

const sectionPadding = {
  none: '',
  sm: 'py-8',
  md: 'py-16',
  lg: 'py-24',
  xl: 'py-32',
};

export const Section: React.FC<SectionProps> = ({
  className,
  variant = 'default',
  padding = 'md',
  containerSize = 'lg',
  animate = true,
  children,
  ...props
}) => {
  return (
    <section
      className={cn(
        sectionVariants[variant],
        sectionPadding[padding],
        className
      )}
      {...props}
    >
      <Container size={containerSize}>
        {children}
      </Container>
    </section>
  );
};
