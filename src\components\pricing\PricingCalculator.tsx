'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { usePricing } from '@/hooks/usePricing';
import { PricingRequest, PricingResponse } from '@/types/pricing';
import { PricingFormatter } from '@/lib/pricing/utils';

interface PricingCalculatorProps {
  productId: string;
  initialQuantity?: number;
  onPriceCalculated?: (response: PricingResponse) => void;
  className?: string;
}

export function PricingCalculator({ 
  productId, 
  initialQuantity = 1, 
  onPriceCalculated,
  className 
}: PricingCalculatorProps) {
  const { calculatePrice, loading, error } = usePricing();
  const [request, setRequest] = useState<PricingRequest>({
    productId,
    quantity: initialQuantity,
    printSize: 'medium',
    qualityTier: 'standard'
  });
  const [result, setResult] = useState<PricingResponse | null>(null);

  // Auto-calculate when request changes
  useEffect(() => {
    const timer = setTimeout(() => {
      handleCalculate();
    }, 500); // Debounce for 500ms

    return () => clearTimeout(timer);
  }, [request]);

  const handleCalculate = async () => {
    const response = await calculatePrice(request);
    if (response) {
      setResult(response);
      onPriceCalculated?.(response);
    }
  };

  const updateRequest = (updates: Partial<PricingRequest>) => {
    setRequest(prev => ({ ...prev, ...updates }));
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            💰 Pricing Calculator
            {loading && <div className="w-4 h-4 border-2 border-primary-500 border-t-transparent rounded-full animate-spin" />}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Configuration Options */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input
              label="Quantity"
              type="number"
              min="1"
              max="100"
              value={request.quantity}
              onChange={(e) => updateRequest({ quantity: parseInt(e.target.value) || 1 })}
            />

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Print Size</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                value={request.printSize || 'medium'}
                onChange={(e) => updateRequest({ printSize: e.target.value as any })}
              >
                <option value="small">Small - Subtle Statement</option>
                <option value="medium">Medium - Balanced Expression</option>
                <option value="large">Large - Bold Statement</option>
                <option value="full_coverage">Full Coverage - Total Expression</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Quality Tier</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                value={request.qualityTier || 'standard'}
                onChange={(e) => updateRequest({ qualityTier: e.target.value as any })}
              >
                <option value="standard">Standard - Everyday Excellence</option>
                <option value="premium">Premium - Elevated Experience</option>
                <option value="luxury">Luxury - Uncompromising Excellence</option>
              </select>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2 text-red-800">
                <span>❌</span>
                <span className="font-medium">Pricing Error</span>
              </div>
              <p className="text-red-700 mt-1">{error}</p>
            </div>
          )}

          {/* Pricing Results */}
          {result?.success && result.data && (
            <div className="space-y-4">
              {/* Total Price Display */}
              <div className="text-center p-6 bg-gradient-to-r from-primary-50 to-warm-50 rounded-xl border border-primary-200">
                <div className="text-4xl font-bold text-primary-600 mb-2">
                  {PricingFormatter.formatCurrency(result.data.totalPrice)}
                </div>
                <p className="text-primary-700 text-lg font-medium mb-1">
                  {result.data.valueMessage}
                </p>
                <p className="text-primary-600 text-sm">
                  {result.data.qualityPromise}
                </p>
                {result.data.savingsMessage && (
                  <div className="mt-3 inline-block px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                    🎉 {result.data.savingsMessage}
                  </div>
                )}
              </div>

              {/* Price Breakdown */}
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                  📊 Price Breakdown
                </h4>
                
                {result.data.breakdown.map((component, index) => (
                  <div 
                    key={component.id || index}
                    className="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-xl">{component.icon || '✨'}</span>
                      <div>
                        <div className="font-medium text-gray-900">{component.name}</div>
                        <div className="text-sm text-gray-600">{component.valueMessage}</div>
                      </div>
                    </div>
                    <div className={`font-semibold ${
                      component.type === 'discount' 
                        ? 'text-green-600' 
                        : component.type === 'surcharge'
                        ? 'text-orange-600'
                        : 'text-gray-900'
                    }`}>
                      {component.type === 'discount' && '-'}
                      {PricingFormatter.formatCurrency(Math.abs(component.amount))}
                    </div>
                  </div>
                ))}
              </div>

              {/* Price Justification */}
              {result.data.priceJustification && result.data.priceJustification.length > 0 && (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
                    💎 Why This Price?
                  </h4>
                  <ul className="space-y-1">
                    {result.data.priceJustification.map((reason, index) => (
                      <li key={index} className="text-blue-800 text-sm flex items-center gap-2">
                        <span className="w-1.5 h-1.5 bg-blue-400 rounded-full"></span>
                        {reason}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Pricing Metadata */}
              <div className="text-xs text-gray-500 text-center pt-2 border-t border-gray-200">
                Price calculated at {new Date(result.data.calculatedAt).toLocaleTimeString()} • 
                Valid until {new Date(result.data.validUntil).toLocaleTimeString()}
              </div>
            </div>
          )}

          {/* Loading State */}
          {loading && !result && (
            <div className="text-center py-8">
              <div className="w-8 h-8 border-4 border-primary-200 border-t-primary-500 rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-gray-600">Calculating your perfect price...</p>
            </div>
          )}

          {/* Empty State */}
          {!loading && !result && !error && (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">🧮</div>
              <p>Adjust options above to see pricing</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Simplified pricing display component
export function PricingDisplay({ 
  price, 
  valueMessage, 
  className 
}: { 
  price: number; 
  valueMessage?: string; 
  className?: string; 
}) {
  return (
    <div className={`text-center ${className}`}>
      <div className="text-2xl font-bold text-primary-600">
        {PricingFormatter.formatCurrency(price)}
      </div>
      {valueMessage && (
        <p className="text-primary-700 text-sm mt-1">{valueMessage}</p>
      )}
    </div>
  );
}

// Quick pricing summary component
export function PricingSummary({ 
  basePrice, 
  totalPrice, 
  savings 
}: { 
  basePrice: number; 
  totalPrice: number; 
  savings?: number; 
}) {
  return (
    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
      <div className="text-sm text-gray-600">
        {savings && savings > 0 ? (
          <>
            <span className="line-through">{PricingFormatter.formatCurrency(basePrice)}</span>
            <span className="ml-2 text-green-600 font-medium">
              Save {PricingFormatter.formatCurrency(savings)}
            </span>
          </>
        ) : (
          <span>Total Price</span>
        )}
      </div>
      <div className="text-xl font-bold text-primary-600">
        {PricingFormatter.formatCurrency(totalPrice)}
      </div>
    </div>
  );
}
