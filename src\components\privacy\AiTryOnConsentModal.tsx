'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui';
import { useResponsive } from '@/hooks/useResponsive';

interface AiTryOnConsentModalProps {
  isOpen: boolean;
  onAccept: () => void;
  onDecline: () => void;
  onClose: () => void;
}

export const AiTryOnConsentModal: React.FC<AiTryOnConsentModalProps> = ({
  isOpen,
  onAccept,
  onDecline,
  onClose
}) => {
  const { isMobile } = useResponsive();
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      title: "✨ See Yourself in Style",
      subtitle: "Experience the magic of AI try-on",
      content: (
        <div className="space-y-4">
          <div className="text-center">
            <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-warm-400 to-amber-500 rounded-full flex items-center justify-center text-3xl">
              👗
            </div>
            <p className="text-gray-600 leading-relaxed">
              Imagine yourself wearing your custom design before you buy. Our AI creates a personalized preview just for you.
            </p>
          </div>
          
          <div className="bg-warm-50 rounded-lg p-4 border border-warm-200">
            <h4 className="font-semibold text-warm-800 mb-2">What makes this special:</h4>
            <ul className="space-y-2 text-sm text-warm-700">
              <li className="flex items-center space-x-2">
                <span className="text-green-500">✓</span>
                <span>See how your design looks on you</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="text-green-500">✓</span>
                <span>Perfect fit visualization</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="text-green-500">✓</span>
                <span>Confidence in your purchase</span>
              </li>
            </ul>
          </div>
        </div>
      )
    },
    {
      title: "🔒 Your Privacy Matters",
      subtitle: "We protect what's yours",
      content: (
        <div className="space-y-4">
          <div className="text-center">
            <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full flex items-center justify-center text-3xl">
              🛡️
            </div>
            <p className="text-gray-600 leading-relaxed">
              Your photos are precious to us. Here's how we keep them safe and respect your privacy.
            </p>
          </div>
          
          <div className="space-y-3">
            <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
              <h4 className="font-semibold text-blue-800 mb-2">🗓️ Automatic Cleanup</h4>
              <p className="text-sm text-blue-700">
                Your photos are automatically deleted after 30 days. No manual action needed.
              </p>
            </div>
            
            <div className="bg-green-50 rounded-lg p-4 border border-green-200">
              <h4 className="font-semibold text-green-800 mb-2">🔐 Secure Processing</h4>
              <p className="text-sm text-green-700">
                Photos are encrypted and processed securely. Never shared with third parties.
              </p>
            </div>
            
            <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
              <h4 className="font-semibold text-purple-800 mb-2">⚡ Limited Use</h4>
              <p className="text-sm text-purple-700">
                2 try-ons per day to ensure quality and protect your privacy.
              </p>
            </div>
          </div>
        </div>
      )
    },
    {
      title: "🎯 Your Control",
      subtitle: "Manage your experience",
      content: (
        <div className="space-y-4">
          <div className="text-center">
            <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-full flex items-center justify-center text-3xl">
              ⚙️
            </div>
            <p className="text-gray-600 leading-relaxed">
              You're in complete control. Change your mind anytime.
            </p>
          </div>
          
          <div className="space-y-3">
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <h4 className="font-semibold text-gray-800 mb-2">📱 Dashboard Access</h4>
              <p className="text-sm text-gray-700">
                View all your try-ons and manage privacy settings from your personal dashboard.
              </p>
            </div>
            
            <div className="bg-red-50 rounded-lg p-4 border border-red-200">
              <h4 className="font-semibold text-red-800 mb-2">🗑️ Delete Anytime</h4>
              <p className="text-sm text-red-700">
                Request immediate deletion of your data. We'll remove everything within 24 hours.
              </p>
            </div>
            
            <div className="bg-amber-50 rounded-lg p-4 border border-amber-200">
              <h4 className="font-semibold text-amber-800 mb-2">📤 Export Your Data</h4>
              <p className="text-sm text-amber-700">
                Download all your try-on history and data whenever you want.
              </p>
            </div>
          </div>
        </div>
      )
    }
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleAccept = () => {
    onAccept();
    setCurrentStep(0); // Reset for next time
  };

  const handleDecline = () => {
    onDecline();
    setCurrentStep(0); // Reset for next time
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.9, opacity: 0, y: 20 }}
            className={`bg-white rounded-2xl shadow-2xl w-full max-w-md ${isMobile ? 'max-h-[90vh]' : 'max-h-[80vh]'} overflow-hidden`}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-warm-500 to-amber-500 text-white p-6 text-center relative">
              <button
                onClick={onClose}
                className="absolute top-4 right-4 text-white/80 hover:text-white text-2xl"
                aria-label="Close modal"
              >
                ×
              </button>
              
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <h2 className="text-2xl font-bold mb-2">
                  {steps[currentStep].title}
                </h2>
                <p className="text-warm-100">
                  {steps[currentStep].subtitle}
                </p>
              </motion.div>
              
              {/* Progress indicator */}
              <div className="flex justify-center space-x-2 mt-4">
                {steps.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index === currentStep ? 'bg-white' : 'bg-white/40'
                    }`}
                  />
                ))}
              </div>
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto flex-1">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  {steps[currentStep].content}
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Footer */}
            <div className="p-6 border-t border-gray-100 bg-gray-50">
              <div className="flex justify-between items-center space-x-3">
                {currentStep > 0 ? (
                  <Button
                    onClick={handlePrevious}
                    variant="outline"
                    className="flex-1"
                  >
                    Previous
                  </Button>
                ) : (
                  <Button
                    onClick={handleDecline}
                    variant="outline"
                    className="flex-1 text-gray-600 border-gray-300"
                  >
                    Not Now
                  </Button>
                )}

                {currentStep < steps.length - 1 ? (
                  <Button
                    onClick={handleNext}
                    className="flex-1 bg-gradient-to-r from-warm-500 to-amber-500 hover:from-warm-600 hover:to-amber-600"
                  >
                    Continue
                  </Button>
                ) : (
                  <Button
                    onClick={handleAccept}
                    className="flex-1 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
                  >
                    ✨ Start Trying On
                  </Button>
                )}
              </div>
              
              {currentStep === steps.length - 1 && (
                <p className="text-xs text-gray-500 text-center mt-3">
                  By continuing, you agree to our{' '}
                  <a href="/privacy" className="text-warm-600 hover:underline">
                    Privacy Policy
                  </a>{' '}
                  and AI Try-On Terms
                </p>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
