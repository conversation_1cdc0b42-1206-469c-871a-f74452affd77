'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Color } from '@/types';
import { Card, CardContent } from '@/components/ui';

interface ColorSelectionProps {
  colors: Color[];
  selectedColor?: Color;
  onSelect: (color: Color, emotionalConnection: string, personalityExpression: string) => void;
  className?: string;
}

interface ColorCardProps {
  color: Color;
  isSelected: boolean;
  onSelect: () => void;
  index: number;
}

const ColorCard: React.FC<ColorCardProps> = ({
  color,
  isSelected,
  onSelect,
  index
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showInspiration, setShowInspiration] = useState(false);

  const getTrendIcon = (status: string) => {
    const icons: Record<string, string> = {
      classic: '👑',
      trending: '🔥',
      seasonal: '🌸',
      limited: '💎'
    };
    return icons[status] || '✨';
  };

  const getMoodEmoji = (moods: string[]) => {
    const moodEmojis: Record<string, string> = {
      sophisticated: '🎩',
      powerful: '⚡',
      timeless: '⏳',
      playful: '🎨',
      confident: '💪',
      elegant: '🌹',
      bold: '🔥',
      serene: '🌊',
      energetic: '⚡',
      romantic: '💕'
    };
    
    return moods.map(mood => moodEmojis[mood.toLowerCase()] || '✨').join('');
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, rotateY: -90 }}
      animate={{ opacity: 1, scale: 1, rotateY: 0 }}
      transition={{ 
        duration: 0.6, 
        delay: index * 0.1,
        ease: [0.25, 0.46, 0.45, 0.94]
      }}
      whileHover={{ scale: 1.05, rotateY: 5 }}
      whileTap={{ scale: 0.95 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className="w-full"
    >
      <Card 
        className={cn(
          "overflow-hidden cursor-pointer transition-all duration-500 group relative",
          "hover:shadow-2xl hover:shadow-accent-200/50",
          isSelected && "ring-4 ring-accent-400 shadow-2xl shadow-accent-200/50",
          color.trendStatus === 'limited' && "border-2 border-gradient-to-r from-accent-300 to-primary-300"
        )}
        onClick={onSelect}
      >
        {/* Trend Status Badge */}
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute top-3 right-3 z-10"
        >
          <span className={cn(
            "px-2 py-1 text-xs font-bold rounded-full",
            color.trendStatus === 'trending' && "bg-red-500 text-white",
            color.trendStatus === 'classic' && "bg-gray-800 text-white",
            color.trendStatus === 'seasonal' && "bg-green-500 text-white",
            color.trendStatus === 'limited' && "bg-gradient-to-r from-purple-500 to-pink-500 text-white"
          )}>
            {getTrendIcon(color.trendStatus)} {color.trendStatus.toUpperCase()}
          </span>
        </motion.div>

        <div className="relative">
          {/* Color Swatch with Gradient Effect */}
          <div className="relative aspect-square overflow-hidden">
            <motion.div
              className="w-full h-full"
              style={{ backgroundColor: color.hexCode }}
              animate={{
                scale: isHovered ? 1.1 : 1,
              }}
              transition={{ duration: 0.3 }}
            />
            
            {/* Shimmer Effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12"
              animate={{
                x: isHovered ? ['0%', '100%'] : '0%',
              }}
              transition={{ duration: 1.5, ease: "easeInOut" }}
            />

            {/* Inspiration Image Overlay */}
            <AnimatePresence>
              {color.inspirationImage && showInspiration && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="absolute inset-0"
                >
                  <img
                    src={color.inspirationImage}
                    alt={`${color.displayName} inspiration`}
                    className="w-full h-full object-cover mix-blend-overlay"
                  />
                </motion.div>
              )}
            </AnimatePresence>

            {/* Mood Indicator */}
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 }}
              className="absolute top-4 left-4 text-2xl"
            >
              {getMoodEmoji(color.mood)}
            </motion.div>

            {/* Stock Level Indicator */}
            <div className="absolute bottom-4 left-4">
              <div className={cn(
                "w-3 h-3 rounded-full",
                color.stockLevel === 'high' && "bg-green-400",
                color.stockLevel === 'medium' && "bg-yellow-400",
                color.stockLevel === 'low' && "bg-orange-400 animate-pulse",
                color.stockLevel === 'out-of-stock' && "bg-red-400"
              )} />
            </div>
          </div>

          {/* Inspiration Toggle Button */}
          {color.inspirationImage && (
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={(e) => {
                e.stopPropagation();
                setShowInspiration(!showInspiration);
              }}
              className="absolute bottom-4 right-4 bg-white/90 backdrop-blur-sm p-2 rounded-full shadow-lg"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
              </svg>
            </motion.button>
          )}
        </div>

        <CardContent className="p-4 space-y-3">
          {/* Color Name & Personality */}
          <div className="space-y-1">
            <motion.h3 
              className="text-lg font-bold text-gray-900 group-hover:text-accent-600 transition-colors"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              {color.displayName}
            </motion.h3>
            
            <motion.p 
              className="text-accent-600 font-medium text-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              {color.personality}
            </motion.p>
          </div>

          {/* Mood Tags */}
          <motion.div 
            className="flex flex-wrap gap-1"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            {color.mood.slice(0, 2).map((mood, moodIndex) => (
              <span
                key={mood}
                className="px-2 py-1 bg-accent-50 text-accent-700 rounded-full text-xs font-medium"
              >
                {mood}
              </span>
            ))}
          </motion.div>

          {/* Occasions */}
          <motion.div
            className="space-y-1"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            <p className="text-xs text-gray-500">Perfect for:</p>
            <div className="flex flex-wrap gap-1">
              {color.occasion.slice(0, 3).map((occasion) => (
                <span
                  key={occasion}
                  className="text-xs text-gray-600 capitalize"
                >
                  {occasion}
                  {color.occasion.indexOf(occasion) < Math.min(color.occasion.length - 1, 2) && ', '}
                </span>
              ))}
            </div>
          </motion.div>

          {/* Styling Tips Preview */}
          <AnimatePresence>
            {isHovered && color.stylingTips.length > 0 && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="p-2 bg-gray-50 rounded-lg"
              >
                <p className="text-xs text-gray-600 italic">
                  💡 {color.stylingTips[0]}
                </p>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Hex Code */}
          <motion.div 
            className="flex items-center justify-between text-xs text-gray-500"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
          >
            <span>{color.hexCode.toUpperCase()}</span>
            <span className="capitalize">{color.season.join(', ')}</span>
          </motion.div>

          {/* Selection Indicator */}
          <AnimatePresence>
            {isSelected && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="absolute inset-0 bg-accent-500/10 backdrop-blur-sm rounded-2xl flex items-center justify-center"
              >
                <div className="bg-accent-500 text-white p-3 rounded-full">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export const ColorSelection: React.FC<ColorSelectionProps> = ({
  colors,
  selectedColor,
  onSelect,
  className
}) => {
  const [colorFilter, setColorFilter] = useState<string>('all');
  
  const handleSelect = (color: Color) => {
    // Generate emotional connection based on color properties
    const connections = [
      `This ${color.displayName.toLowerCase()} makes me feel ${color.mood[0]}`,
      `Perfect for expressing my ${color.personality.toLowerCase()} side`,
      `I love how ${color.displayName.toLowerCase()} represents ${color.mood.join(' and ')}`,
    ];
    
    const selectedConnection = connections[Math.floor(Math.random() * connections.length)];
    const personalityExpression = color.personality.toLowerCase();
    
    onSelect(color, selectedConnection, personalityExpression);
  };

  const filteredColors = colorFilter === 'all' 
    ? colors 
    : colors.filter(color => color.mood.includes(colorFilter));

  const moodFilters = ['all', 'sophisticated', 'bold', 'playful', 'elegant', 'confident'];

  return (
    <div className={cn("space-y-8", className)}>
      {/* Header */}
      <motion.div 
        className="text-center space-y-4"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h2 className="text-3xl md:text-4xl font-bold text-gradient-primary">
          What color is your energy?
        </h2>
        <p className="text-lg text-gray-700 max-w-2xl mx-auto leading-relaxed">
          Colors speak the language of emotion. Choose the one that amplifies 
          your inner light and makes you feel unstoppable.
        </p>
      </motion.div>

      {/* Mood Filters */}
      <motion.div
        className="flex flex-wrap justify-center gap-2"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        {moodFilters.map((mood) => (
          <motion.button
            key={mood}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setColorFilter(mood)}
            className={cn(
              "px-4 py-2 rounded-full text-sm font-medium transition-all",
              colorFilter === mood
                ? "bg-accent-500 text-white shadow-lg"
                : "bg-gray-100 text-gray-700 hover:bg-accent-100"
            )}
          >
            {mood === 'all' ? 'All Colors' : mood.charAt(0).toUpperCase() + mood.slice(1)}
          </motion.button>
        ))}
      </motion.div>

      {/* Color Grid */}
      <motion.div 
        className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6"
        layout
      >
        <AnimatePresence>
          {filteredColors.map((color, index) => (
            <ColorCard
              key={color.id}
              color={color}
              isSelected={selectedColor?.id === color.id}
              onSelect={() => handleSelect(color)}
              index={index}
            />
          ))}
        </AnimatePresence>
      </motion.div>

      {/* Emotional Encouragement */}
      <AnimatePresence>
        {selectedColor && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="text-center p-6 bg-gradient-to-r from-accent-50 to-primary-50 rounded-2xl"
          >
            <p className="text-accent-700 font-medium">
              ✨ {selectedColor.displayName} is perfect! This color embodies {selectedColor.mood.join(', ')} energy.
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
