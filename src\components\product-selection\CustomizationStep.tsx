'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, ArrowRight, Palette } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { ResponsiveContainer } from '@/components/ui/ResponsiveContainer';
import { CustomizationEditor } from '@/components/editor';
import { useEditorStore } from '@/stores/editorStore';
import { SelectedCustomization } from '@/types/product-selection';

interface CustomizationStepProps {
  onNext?: (customization: SelectedCustomization) => void;
  onPrev?: () => void;
  className?: string;
}

export const CustomizationStep: React.FC<CustomizationStepProps> = ({
  onNext,
  onPrev,
  className
}) => {
  const [personalExpression, setPersonalExpression] = useState('');
  const [creativeSatisfaction, setCreativeSatisfaction] = useState(5);
  
  const { canvas, placement } = useEditorStore();
  
  const hasDesignElements = canvas.elements.length > 0;
  
  const calculateDesignComplexity = (): 'simple' | 'moderate' | 'complex' => {
    const elementCount = canvas.elements.length;
    if (elementCount <= 1) return 'simple';
    if (elementCount <= 3) return 'moderate';
    return 'complex';
  };
  
  const calculatePrintArea = (): number => {
    // Simple calculation based on elements - in real app this would be more sophisticated
    let totalArea = 0;
    canvas.elements.forEach(element => {
      const area = (element.width * element.height) / 10000; // Convert to cm²
      totalArea += area;
    });
    return Math.round(totalArea * 100) / 100;
  };

  const handleContinue = () => {
    if (!hasDesignElements) {
      alert('Please add at least one design element to continue');
      return;
    }

    const customization: SelectedCustomization = {
      id: `custom-${Date.now()}`,
      designData: canvas,
      placement,
      personalExpression: personalExpression || 'My unique design',
      creativeSatisfaction,
      designComplexity: calculateDesignComplexity(),
      printArea: calculatePrintArea()
    };

    onNext?.(customization);
  };

  return (
    <ResponsiveContainer className={className}>
      <div className="space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center space-y-4"
        >
          <div className="flex items-center justify-center gap-3">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                Make it yours
              </h2>
              <p className="text-gray-600 text-lg">
                Design something that speaks to your soul
              </p>
            </div>
          </div>
        </motion.div>

        {/* Main Editor */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
        >
          <CustomizationEditor />
        </motion.div>

        {/* Personal Expression Section */}
        {hasDesignElements && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card>
              <CardContent className="p-6 space-y-6">
                <div className="text-center">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Tell us about your creation
                  </h3>
                  <p className="text-gray-600">
                    What does this design mean to you?
                  </p>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      What inspired this design? (Optional)
                    </label>
                    <textarea
                      value={personalExpression}
                      onChange={(e) => setPersonalExpression(e.target.value)}
                      placeholder="Share the story behind your design..."
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                      rows={3}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      How satisfied are you with your creation?
                    </label>
                    <div className="flex gap-2">
                      {[1, 2, 3, 4, 5].map((rating) => (
                        <button
                          key={rating}
                          onClick={() => setCreativeSatisfaction(rating)}
                          className={`w-12 h-12 rounded-full border-2 transition-all ${
                            rating <= creativeSatisfaction
                              ? 'bg-purple-500 border-purple-500 text-white'
                              : 'border-gray-300 text-gray-400 hover:border-purple-300'
                          }`}
                        >
                          {rating === 1 && '😐'}
                          {rating === 2 && '🙂'}
                          {rating === 3 && '😊'}
                          {rating === 4 && '😍'}
                          {rating === 5 && '🤩'}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Design Summary */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">Design Summary</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Elements:</span>
                      <span className="ml-2 font-medium">{canvas.elements.length}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Placement:</span>
                      <span className="ml-2 font-medium capitalize">{placement}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Complexity:</span>
                      <span className="ml-2 font-medium capitalize">{calculateDesignComplexity()}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Print Area:</span>
                      <span className="ml-2 font-medium">{calculatePrintArea()} cm²</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Navigation */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="flex flex-col sm:flex-row gap-4 justify-between"
        >
          <Button
            variant="outline"
            onClick={onPrev}
            size="lg"
            className="order-2 sm:order-1"
          >
            ← Back to Size Selection
          </Button>

          <div className="order-1 sm:order-2 flex flex-col sm:flex-row gap-4">
            {!hasDesignElements && (
              <div className="text-center sm:text-right">
                <p className="text-sm text-gray-600 mb-2">
                  Add text or images to continue
                </p>
                <Button
                  variant="outline"
                  size="lg"
                  className="w-full sm:w-auto"
                  disabled
                >
                  <Palette className="w-4 h-4 mr-2" />
                  Start Designing
                </Button>
              </div>
            )}
            
            {hasDesignElements && (
              <Button
                onClick={handleContinue}
                size="lg"
                className="w-full sm:w-auto"
              >
                Continue to Review
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            )}
          </div>
        </motion.div>

        {/* Inspirational Message */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="text-center"
        >
          <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-xl">
            <p className="text-purple-800 font-medium">
              ✨ This is your moment to shine. Create something that's uniquely, authentically you.
            </p>
          </div>
        </motion.div>
      </div>
    </ResponsiveContainer>
  );
};
