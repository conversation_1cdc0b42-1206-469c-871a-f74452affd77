'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Fabric } from '@/types';
import { Card, CardContent } from '@/components/ui';

interface FabricSelectionProps {
  fabrics: Fabric[];
  selectedFabric?: Fabric;
  onSelect: (fabric: Fabric, sensoryAppeal: string, touchExperience: number) => void;
  className?: string;
}

interface FabricCardProps {
  fabric: Fabric;
  isSelected: boolean;
  onSelect: () => void;
  index: number;
}

const FabricCard: React.FC<FabricCardProps> = ({
  fabric,
  isSelected,
  onSelect,
  index
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [touchRating, setTouchRating] = useState(0);

  const handleTouchRating = (rating: number, event: React.MouseEvent) => {
    event.stopPropagation();
    setTouchRating(rating);
  };

  const getTextureIcon = (texture: string) => {
    const icons: Record<string, string> = {
      soft: '🤗',
      smooth: '✨',
      structured: '💪',
      breathable: '🌬️',
      luxurious: '👑',
      comfortable: '☁️',
      athletic: '⚡',
      cozy: '🔥'
    };
    return icons[texture.toLowerCase()] || '✨';
  };

  const getDurabilityStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i} className={i < rating ? 'text-yellow-400' : 'text-gray-300'}>
        ⭐
      </span>
    ));
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ 
        duration: 0.5, 
        delay: index * 0.1,
        ease: [0.25, 0.46, 0.45, 0.94]
      }}
      whileHover={{ scale: 1.03 }}
      whileTap={{ scale: 0.97 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className="w-full"
    >
      <Card 
        className={cn(
          "overflow-hidden cursor-pointer transition-all duration-500 group relative",
          "hover:shadow-xl hover:shadow-warm-200/50",
          isSelected && "ring-4 ring-warm-400 shadow-xl shadow-warm-200/50",
          fabric.isPremium && "border-2 border-gradient-to-r from-primary-300 to-warm-300"
        )}
        onClick={onSelect}
      >
        {/* Premium Badge */}
        {fabric.isPremium && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="absolute top-3 right-3 z-10"
          >
            <span className="px-2 py-1 bg-gradient-to-r from-primary-500 to-warm-500 text-white text-xs font-bold rounded-full">
              PREMIUM
            </span>
          </motion.div>
        )}

        <div className="relative">
          {/* Fabric Image with Texture Video Overlay */}
          <div className="relative aspect-[4/3] overflow-hidden">
            <img
              src={fabric.image}
              alt={`${fabric.name} fabric`}
              className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
            />
            
            {/* Texture Overlay Effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-br from-transparent via-white/10 to-transparent"
              animate={{
                opacity: isHovered ? [0, 0.3, 0] : 0,
                scale: isHovered ? [1, 1.1, 1] : 1
              }}
              transition={{ duration: 2, repeat: isHovered ? Infinity : 0 }}
            />

            {/* Texture Icon */}
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 }}
              className="absolute top-4 left-4 text-2xl"
            >
              {getTextureIcon(fabric.texture)}
            </motion.div>
          </div>

          {/* Fabric Swatch */}
          <div className="absolute bottom-4 right-4">
            <motion.div
              whileHover={{ scale: 1.2, rotate: 5 }}
              className="w-12 h-12 rounded-full border-4 border-white shadow-lg overflow-hidden"
            >
              <img
                src={fabric.swatchImage}
                alt={`${fabric.name} swatch`}
                className="w-full h-full object-cover"
              />
            </motion.div>
          </div>
        </div>

        <CardContent className="p-6 space-y-4">
          {/* Fabric Name & Feel */}
          <div className="space-y-2">
            <motion.h3 
              className="text-xl font-bold text-gray-900 group-hover:text-warm-600 transition-colors"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              {fabric.name}
            </motion.h3>
            
            <motion.p 
              className="text-warm-600 font-medium text-sm capitalize"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              {fabric.feel} • {fabric.weight}
            </motion.p>
          </div>

          {/* Emotional Benefit */}
          <motion.p 
            className="text-gray-700 text-sm leading-relaxed font-medium"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            {fabric.emotionalBenefit}
          </motion.p>

          {/* Comfort Promise */}
          <motion.div
            className="p-3 bg-warm-50 rounded-lg"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <p className="text-warm-700 text-sm italic">
              "{fabric.comfortPromise}"
            </p>
          </motion.div>

          {/* Sensory Details */}
          <motion.div 
            className="grid grid-cols-2 gap-4 text-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
          >
            <div>
              <span className="text-gray-500">Texture:</span>
              <p className="font-medium capitalize">{fabric.texture}</p>
            </div>
            <div>
              <span className="text-gray-500">Durability:</span>
              <div className="flex items-center gap-1">
                {getDurabilityStars(fabric.durabilityRating)}
              </div>
            </div>
          </motion.div>

          {/* Interactive Touch Rating */}
          <motion.div
            className="space-y-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.7 }}
          >
            <p className="text-sm text-gray-600">How does this feel to you?</p>
            <div className="flex gap-1">
              {[1, 2, 3, 4, 5].map((rating) => (
                <motion.button
                  key={rating}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={(e) => handleTouchRating(rating, e)}
                  className={cn(
                    "w-8 h-8 rounded-full transition-colors",
                    touchRating >= rating 
                      ? "bg-warm-400 text-white" 
                      : "bg-gray-200 hover:bg-warm-200"
                  )}
                >
                  {rating <= 2 ? '👍' : rating <= 4 ? '😍' : '🤩'}
                </motion.button>
              ))}
            </div>
          </motion.div>

          {/* Price Modifier */}
          <motion.div 
            className="flex items-center justify-between pt-2 border-t border-gray-100"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
          >
            <span className="text-sm text-gray-600">
              {fabric.priceModifier > 1 ? 'Premium upgrade' : 'Included'}
            </span>
            {fabric.priceModifier > 1 && (
              <span className="text-sm font-medium text-warm-600">
                +{Math.round((fabric.priceModifier - 1) * 100)}%
              </span>
            )}
          </motion.div>

          {/* Selection Indicator */}
          <AnimatePresence>
            {isSelected && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="absolute inset-0 bg-warm-500/10 backdrop-blur-sm rounded-2xl flex items-center justify-center"
              >
                <div className="bg-warm-500 text-white p-3 rounded-full">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export const FabricSelection: React.FC<FabricSelectionProps> = ({
  fabrics,
  selectedFabric,
  onSelect,
  className
}) => {
  const handleSelect = (fabric: Fabric) => {
    // Generate sensory appeal based on fabric properties
    const appeals = [
      `The ${fabric.texture} texture feels amazing`,
      `Perfect ${fabric.weight} weight for comfort`,
      `Love the ${fabric.feel} sensation`,
    ];
    
    const selectedAppeal = appeals[Math.floor(Math.random() * appeals.length)];
    const touchExperience = Math.floor(Math.random() * 2) + 4; // 4-5 rating
    
    onSelect(fabric, selectedAppeal, touchExperience);
  };

  return (
    <div className={cn("space-y-8", className)}>
      {/* Header */}
      <motion.div 
        className="text-center space-y-4"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h2 className="text-3xl md:text-4xl font-bold text-gradient-warm">
          How do you want to feel?
        </h2>
        <p className="text-lg text-gray-700 max-w-2xl mx-auto leading-relaxed">
          Choose the fabric that speaks to your senses. Every touch should feel like 
          a gentle reminder of your own comfort and confidence.
        </p>
      </motion.div>

      {/* Fabric Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
        {fabrics.map((fabric, index) => (
          <FabricCard
            key={fabric.id}
            fabric={fabric}
            isSelected={selectedFabric?.id === fabric.id}
            onSelect={() => handleSelect(fabric)}
            index={index}
          />
        ))}
      </div>

      {/* Emotional Encouragement */}
      <AnimatePresence>
        {selectedFabric && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="text-center p-6 bg-gradient-to-r from-warm-50 to-primary-50 rounded-2xl"
          >
            <p className="text-warm-700 font-medium">
              🤗 Wonderful choice! {selectedFabric.name} will feel like {selectedFabric.comfortPromise.toLowerCase()}.
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
