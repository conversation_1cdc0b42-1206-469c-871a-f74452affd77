'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  ProductSelectionStep, 
  StepperState, 
  StepDefinition,
  ProductSelectionFlow 
} from '@/types/product-selection';
import { ProductType, Fabric, Color, Size } from '@/types';

interface ProductSelectionStepperProps {
  initialStep?: ProductSelectionStep;
  onStepChange?: (step: ProductSelectionStep) => void;
  onComplete?: (selections: ProductSelectionFlow) => void;
  className?: string;
  children: React.ReactNode;
}

interface StepIndicatorProps {
  steps: StepDefinition[];
  currentStepIndex: number;
  onStepClick?: (stepIndex: number) => void;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({
  steps,
  currentStepIndex,
  onStepClick
}) => {
  return (
    <div className="w-full max-w-4xl mx-auto px-4 py-6">
      {/* Mobile Progress Bar */}
      <div className="md:hidden mb-4">
        <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
          <span>Step {currentStepIndex + 1} of {steps.length}</span>
          <span>{Math.round(((currentStepIndex + 1) / steps.length) * 100)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            className="bg-gradient-to-r from-primary-500 to-warm-500 h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${((currentStepIndex + 1) / steps.length) * 100}%` }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          />
        </div>
      </div>

      {/* Desktop Step Indicators */}
      <div className="hidden md:flex items-center justify-between">
        {steps.map((step, index) => (
          <React.Fragment key={step.id}>
            <motion.div
              className={cn(
                "flex flex-col items-center cursor-pointer group",
                index <= currentStepIndex ? "text-primary-600" : "text-gray-400"
              )}
              onClick={() => onStepClick?.(index)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {/* Step Circle */}
              <motion.div
                className={cn(
                  "w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold border-2 transition-all duration-300",
                  index < currentStepIndex && "bg-primary-500 border-primary-500 text-white",
                  index === currentStepIndex && "bg-white border-primary-500 text-primary-600 shadow-lg",
                  index > currentStepIndex && "bg-gray-100 border-gray-300 text-gray-400"
                )}
                animate={{
                  scale: index === currentStepIndex ? 1.1 : 1,
                  boxShadow: index === currentStepIndex 
                    ? "0 0 20px rgba(245, 149, 50, 0.3)" 
                    : "0 0 0px rgba(245, 149, 50, 0)"
                }}
                transition={{ duration: 0.3 }}
              >
                {index < currentStepIndex ? (
                  <motion.svg
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="w-6 h-6"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </motion.svg>
                ) : (
                  <span>{step.icon}</span>
                )}
              </motion.div>

              {/* Step Info */}
              <div className="mt-3 text-center">
                <motion.h3
                  className={cn(
                    "text-sm font-semibold transition-colors",
                    index <= currentStepIndex ? "text-gray-900" : "text-gray-500"
                  )}
                  animate={{
                    color: index === currentStepIndex ? "#f59e0b" : undefined
                  }}
                >
                  {step.title}
                </motion.h3>
                <p className="text-xs text-gray-500 mt-1 max-w-20">
                  {step.subtitle}
                </p>
              </div>

              {/* Emotional Prompt */}
              <AnimatePresence>
                {index === currentStepIndex && (
                  <motion.p
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="text-xs text-primary-600 italic mt-2 max-w-24 text-center"
                  >
                    {step.emotionalPrompt}
                  </motion.p>
                )}
              </AnimatePresence>
            </motion.div>

            {/* Connector Line */}
            {index < steps.length - 1 && (
              <div className="flex-1 mx-4">
                <div className="relative h-0.5 bg-gray-200">
                  <motion.div
                    className="absolute left-0 top-0 h-full bg-gradient-to-r from-primary-500 to-warm-500"
                    initial={{ width: 0 }}
                    animate={{ 
                      width: index < currentStepIndex ? "100%" : "0%" 
                    }}
                    transition={{ duration: 0.8, ease: "easeOut" }}
                  />
                </div>
              </div>
            )}
          </React.Fragment>
        ))}
      </div>

      {/* Current Step Emotional Prompt - Mobile */}
      <div className="md:hidden mt-4 text-center">
        <motion.p
          key={currentStepIndex}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-sm text-primary-600 italic"
        >
          {steps[currentStepIndex]?.emotionalPrompt}
        </motion.p>
      </div>
    </div>
  );
};

export const ProductSelectionStepper: React.FC<ProductSelectionStepperProps> = ({
  initialStep = 'product-type',
  onStepChange,
  onComplete,
  className,
  children
}) => {
  const [stepperState, setStepperState] = useState<StepperState>({
    steps: [
      {
        id: 'product-type',
        title: 'Your Style',
        subtitle: 'Choose your canvas',
        icon: '👕',
        isComplete: false,
        isActive: true,
        emotionalPrompt: 'What makes you feel unstoppable?'
      },
      {
        id: 'fabric',
        title: 'Your Comfort',
        subtitle: 'Feel the difference',
        icon: '🤗',
        isComplete: false,
        isActive: false,
        emotionalPrompt: 'How do you want to feel?'
      },
      {
        id: 'color',
        title: 'Your Energy',
        subtitle: 'Express yourself',
        icon: '🎨',
        isComplete: false,
        isActive: false,
        emotionalPrompt: 'What color is your soul?'
      },
      {
        id: 'size',
        title: 'Your Fit',
        subtitle: 'Celebrate you',
        icon: '✨',
        isComplete: false,
        isActive: false,
        emotionalPrompt: 'Find your perfect match'
      }
    ],
    currentStepIndex: 0,
    canGoBack: false,
    canGoForward: false,
    progress: 0
  });

  const [slideDirection, setSlideDirection] = useState<'left' | 'right'>('right');

  // Initialize step based on initialStep
  useEffect(() => {
    const stepIndex = stepperState.steps.findIndex(step => step.id === initialStep);
    if (stepIndex !== -1) {
      setStepperState(prev => ({
        ...prev,
        currentStepIndex: stepIndex,
        canGoBack: stepIndex > 0,
        progress: ((stepIndex + 1) / prev.steps.length) * 100
      }));
    }
  }, [initialStep, stepperState.steps]);

  const goToStep = (stepIndex: number) => {
    if (stepIndex < 0 || stepIndex >= stepperState.steps.length) return;
    
    const direction = stepIndex > stepperState.currentStepIndex ? 'right' : 'left';
    setSlideDirection(direction);
    
    setStepperState(prev => ({
      ...prev,
      currentStepIndex: stepIndex,
      canGoBack: stepIndex > 0,
      canGoForward: stepIndex < prev.steps.length - 1,
      progress: ((stepIndex + 1) / prev.steps.length) * 100,
      steps: prev.steps.map((step, index) => ({
        ...step,
        isActive: index === stepIndex,
        isComplete: index < stepIndex
      }))
    }));

    const currentStep = stepperState.steps[stepIndex];
    onStepChange?.(currentStep.id as ProductSelectionStep);
  };

  const nextStep = () => {
    if (stepperState.currentStepIndex < stepperState.steps.length - 1) {
      goToStep(stepperState.currentStepIndex + 1);
    }
  };

  const prevStep = () => {
    if (stepperState.currentStepIndex > 0) {
      goToStep(stepperState.currentStepIndex - 1);
    }
  };

  const markStepComplete = (stepId: ProductSelectionStep) => {
    setStepperState(prev => ({
      ...prev,
      steps: prev.steps.map(step => 
        step.id === stepId ? { ...step, isComplete: true } : step
      )
    }));
  };

  // Slide animation variants
  const slideVariants = {
    enter: (direction: string) => ({
      x: direction === 'right' ? 1000 : -1000,
      opacity: 0
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1
    },
    exit: (direction: string) => ({
      zIndex: 0,
      x: direction === 'right' ? -1000 : 1000,
      opacity: 0
    })
  };

  return (
    <div className={cn("min-h-screen bg-gradient-to-br from-warm-50 to-amber-50", className)}>
      {/* Step Indicator */}
      <StepIndicator
        steps={stepperState.steps}
        currentStepIndex={stepperState.currentStepIndex}
        onStepClick={goToStep}
      />

      {/* Step Content */}
      <div className="relative overflow-hidden">
        <AnimatePresence initial={false} custom={slideDirection} mode="wait">
          <motion.div
            key={stepperState.currentStepIndex}
            custom={slideDirection}
            variants={slideVariants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{
              x: { type: "spring", stiffness: 300, damping: 30 },
              opacity: { duration: 0.2 }
            }}
            className="w-full"
          >
            <div className="container-fluid py-8 pb-32">
              {/* Pass stepper controls to children */}
              {React.cloneElement(children as React.ReactElement, {
                currentStep: stepperState.steps[stepperState.currentStepIndex].id,
                onNext: nextStep,
                onPrev: prevStep,
                onComplete: markStepComplete,
                canGoBack: stepperState.canGoBack,
                canGoForward: stepperState.canGoForward,
                progress: stepperState.progress
              })}
            </div>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Navigation Hints */}
      <div className="fixed bottom-20 left-4 right-4 md:left-8 md:right-8 pointer-events-none">
        <div className="flex justify-between items-center max-w-4xl mx-auto">
          {/* Back Button Hint */}
          <AnimatePresence>
            {stepperState.canGoBack && (
              <motion.button
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                onClick={prevStep}
                className="pointer-events-auto p-3 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all text-gray-600 hover:text-primary-600"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </motion.button>
            )}
          </AnimatePresence>

          {/* Progress Indicator */}
          <motion.div
            className="text-center text-sm text-gray-600 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full shadow-lg"
            animate={{ scale: [1, 1.05, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            Step {stepperState.currentStepIndex + 1} of {stepperState.steps.length}
          </motion.div>

          {/* Forward Hint */}
          <div className="w-11 h-11" /> {/* Spacer for symmetry */}
        </div>
      </div>
    </div>
  );
};
