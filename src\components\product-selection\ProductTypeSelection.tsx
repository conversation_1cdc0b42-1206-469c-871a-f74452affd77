'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { ProductType, LifestyleImage } from '@/types';
import { Card, CardContent } from '@/components/ui';

interface ProductTypeSelectionProps {
  productTypes: ProductType[];
  selectedType?: ProductType;
  onSelect: (productType: ProductType, emotionalReason: string) => void;
  className?: string;
}

interface ProductTypeCardProps {
  productType: ProductType;
  isSelected: boolean;
  onSelect: () => void;
  index: number;
}

const ProductTypeCard: React.FC<ProductTypeCardProps> = ({
  productType,
  isSelected,
  onSelect,
  index
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [currentLifestyleImage, setCurrentLifestyleImage] = useState(0);

  // Cycle through lifestyle images
  useEffect(() => {
    if (productType.lifestyleImages.length > 1) {
      const interval = setInterval(() => {
        setCurrentLifestyleImage(prev => 
          (prev + 1) % productType.lifestyleImages.length
        );
      }, 4000);
      return () => clearInterval(interval);
    }
  }, [productType.lifestyleImages.length]);

  const currentImage = productType.lifestyleImages[currentLifestyleImage] || {
    url: productType.heroImage,
    context: 'lifestyle',
    mood: 'confident',
    model: 'diverse',
    alt: productType.name
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.6, 
        delay: index * 0.1,
        ease: [0.25, 0.46, 0.45, 0.94]
      }}
      whileHover={{ y: -8, scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className="w-full"
    >
      <Card 
        className={cn(
          "overflow-hidden cursor-pointer transition-all duration-500 group",
          "hover:shadow-2xl hover:shadow-primary-200/50",
          isSelected && "ring-4 ring-primary-400 shadow-2xl shadow-primary-200/50"
        )}
        onClick={onSelect}
      >
        <div className="relative aspect-[4/5] overflow-hidden">
          {/* Hero Image with Parallax Effect */}
          <motion.div
            className="absolute inset-0"
            whileHover={{ scale: 1.1 }}
            transition={{ duration: 0.7, ease: "easeOut" }}
          >
            <img
              src={currentImage.url}
              alt={currentImage.alt}
              className={cn(
                "w-full h-full object-cover transition-opacity duration-500",
                imageLoaded ? "opacity-100" : "opacity-0"
              )}
              onLoad={() => setImageLoaded(true)}
            />
            
            {/* Gradient Overlay for Text Readability */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
            
            {/* Lifestyle Context Badge */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
              className="absolute top-4 left-4"
            >
              <span className="px-3 py-1 bg-white/90 backdrop-blur-sm rounded-full text-xs font-medium text-gray-800 capitalize">
                {currentImage.context} vibes
              </span>
            </motion.div>

            {/* Mood Indicator */}
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 }}
              className="absolute top-4 right-4"
            >
              <div className="w-3 h-3 bg-primary-400 rounded-full animate-pulse" />
            </motion.div>
          </div>

          {/* Loading Skeleton */}
          {!imageLoaded && (
            <div className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-300 animate-pulse" />
          )}
        </div>

        <CardContent className="p-6 space-y-4">
          {/* Product Name & Tagline */}
          <div className="space-y-2">
            <motion.h3 
              className="text-xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              {productType.name}
            </motion.h3>
            
            <motion.p 
              className="text-primary-600 font-medium text-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              {productType.tagline}
            </motion.p>
          </div>

          {/* Emotional Hook */}
          <motion.p 
            className="text-gray-700 text-sm leading-relaxed"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            {productType.emotionalHook}
          </motion.p>

          {/* Mood Tags */}
          <motion.div 
            className="flex flex-wrap gap-2"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            {productType.moodTags.slice(0, 3).map((tag, tagIndex) => (
              <span
                key={tag}
                className="px-2 py-1 bg-primary-50 text-primary-700 rounded-full text-xs font-medium"
              >
                {tag}
              </span>
            ))}
          </motion.div>

          {/* Price */}
          <motion.div 
            className="flex items-center justify-between pt-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
          >
            <span className="text-lg font-bold text-gray-900">
              ${productType.basePrice}
            </span>
            <span className="text-xs text-gray-500">starting from</span>
          </motion.div>

          {/* Selection Indicator */}
          <AnimatePresence>
            {isSelected && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="absolute inset-0 bg-primary-500/10 backdrop-blur-sm rounded-2xl flex items-center justify-center"
              >
                <div className="bg-primary-500 text-white p-3 rounded-full">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export const ProductTypeSelection: React.FC<ProductTypeSelectionProps> = ({
  productTypes,
  selectedType,
  onSelect,
  className
}) => {
  const [emotionalReason, setEmotionalReason] = useState('');

  const handleSelect = (productType: ProductType) => {
    // Generate emotional reason based on the product's appeal
    const reasons = [
      `I love how ${productType.name.toLowerCase()} makes me feel ${productType.moodTags[0]}`,
      `Perfect for my ${productType.lifestyleTags[0]} lifestyle`,
      `${productType.emotionalHook.toLowerCase()}`,
    ];
    
    const selectedReason = reasons[Math.floor(Math.random() * reasons.length)];
    setEmotionalReason(selectedReason);
    onSelect(productType, selectedReason);
  };

  return (
    <div className={cn("space-y-8", className)}>
      {/* Header */}
      <motion.div 
        className="text-center space-y-4"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h2 className="text-3xl md:text-4xl font-bold text-gradient-primary">
          What speaks to your soul?
        </h2>
        <p className="text-lg text-gray-700 max-w-2xl mx-auto leading-relaxed">
          Choose the piece that makes you feel like the most authentic version of yourself. 
          This is where your story begins.
        </p>
      </motion.div>

      {/* Product Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
        {productTypes.map((productType, index) => (
          <ProductTypeCard
            key={productType.id}
            productType={productType}
            isSelected={selectedType?.id === productType.id}
            onSelect={() => handleSelect(productType)}
            index={index}
          />
        ))}
      </div>

      {/* Emotional Encouragement */}
      <AnimatePresence>
        {selectedType && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="text-center p-6 bg-gradient-to-r from-primary-50 to-warm-50 rounded-2xl"
          >
            <p className="text-primary-700 font-medium">
              ✨ Beautiful choice! {selectedType.name} is perfect for someone who values {selectedType.moodTags.join(', ')}.
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
