'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Size } from '@/types';
import { Card, CardContent } from '@/components/ui';

interface SizeSelectionProps {
  sizes: Size[];
  selectedSize?: Size;
  onSelect: (size: Size, fitPreference: string, confidenceBoost: string) => void;
  className?: string;
}

interface SizeCardProps {
  size: Size;
  isSelected: boolean;
  onSelect: () => void;
  index: number;
}

const SizeCard: React.FC<SizeCardProps> = ({
  size,
  isSelected,
  onSelect,
  index
}) => {
  const [showMeasurements, setShowMeasurements] = useState(false);
  const [confidenceLevel, setConfidenceLevel] = useState(0);

  const getStockStatus = (stock: number) => {
    if (stock > 50) return { status: 'high', color: 'text-green-600', message: 'In Stock' };
    if (stock > 10) return { status: 'medium', color: 'text-yellow-600', message: 'Limited Stock' };
    if (stock > 0) return { status: 'low', color: 'text-orange-600', message: 'Few Left' };
    return { status: 'out', color: 'text-red-600', message: 'Out of Stock' };
  };

  const stockInfo = getStockStatus(size.stockLevel);

  const handleConfidenceRating = (rating: number, event: React.MouseEvent) => {
    event.stopPropagation();
    setConfidenceLevel(rating);
  };

  const getSizeVisualization = (sizeName: string) => {
    const sizeMap: Record<string, { width: string; height: string }> = {
      'XS': { width: 'w-8', height: 'h-10' },
      'S': { width: 'w-10', height: 'h-12' },
      'M': { width: 'w-12', height: 'h-14' },
      'L': { width: 'w-14', height: 'h-16' },
      'XL': { width: 'w-16', height: 'h-18' },
      'XXL': { width: 'w-18', height: 'h-20' },
    };
    return sizeMap[sizeName] || { width: 'w-12', height: 'h-14' };
  };

  const visualization = getSizeVisualization(size.name);

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.5, 
        delay: index * 0.1,
        ease: [0.25, 0.46, 0.45, 0.94]
      }}
      whileHover={{ y: -5 }}
      whileTap={{ scale: 0.98 }}
      className="w-full"
    >
      <Card 
        className={cn(
          "overflow-hidden cursor-pointer transition-all duration-500 group relative",
          "hover:shadow-xl hover:shadow-emerald-200/50",
          isSelected && "ring-4 ring-emerald-400 shadow-xl shadow-emerald-200/50",
          size.stockLevel === 0 && "opacity-50 cursor-not-allowed"
        )}
        onClick={size.stockLevel > 0 ? onSelect : undefined}
      >
        {/* Stock Status Badge */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="absolute top-3 right-3 z-10"
        >
          <span className={cn(
            "px-2 py-1 text-xs font-bold rounded-full",
            stockInfo.status === 'high' && "bg-green-100 text-green-700",
            stockInfo.status === 'medium' && "bg-yellow-100 text-yellow-700",
            stockInfo.status === 'low' && "bg-orange-100 text-orange-700 animate-pulse",
            stockInfo.status === 'out' && "bg-red-100 text-red-700"
          )}>
            {stockInfo.message}
          </span>
        </motion.div>

        <CardContent className="p-6 space-y-4">
          {/* Size Visualization & Name */}
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <motion.h3 
                className="text-2xl font-bold text-gray-900 group-hover:text-emerald-600 transition-colors"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                {size.name}
              </motion.h3>
              
              <motion.p 
                className="text-emerald-600 font-medium text-sm"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
              >
                {size.displayName}
              </motion.p>
            </div>

            {/* Visual Size Representation */}
            <motion.div
              className="flex items-end justify-center"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.4, type: "spring" }}
            >
              <div className={cn(
                "bg-gradient-to-t from-emerald-400 to-emerald-300 rounded-lg",
                visualization.width,
                visualization.height,
                "shadow-lg"
              )} />
            </motion.div>
          </div>

          {/* Fit Description */}
          <motion.p 
            className="text-gray-700 text-sm leading-relaxed"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            {size.fitDescription}
          </motion.p>

          {/* Body Positive Message */}
          <motion.div
            className="p-3 bg-emerald-50 rounded-lg"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <p className="text-emerald-700 text-sm font-medium">
              💚 {size.bodyPositiveMessage}
            </p>
          </motion.div>

          {/* Confidence Booster */}
          <motion.div
            className="p-3 bg-gradient-to-r from-primary-50 to-warm-50 rounded-lg"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
          >
            <p className="text-primary-700 text-sm italic">
              "✨ {size.confidenceBooster}"
            </p>
          </motion.div>

          {/* Measurements Toggle */}
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={(e) => {
              e.stopPropagation();
              setShowMeasurements(!showMeasurements);
            }}
            className="w-full p-2 text-sm text-emerald-600 hover:text-emerald-700 font-medium transition-colors"
          >
            {showMeasurements ? 'Hide' : 'Show'} Measurements
          </motion.button>

          {/* Measurements Details */}
          <AnimatePresence>
            {showMeasurements && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-2 text-sm"
              >
                <div className="grid grid-cols-2 gap-2">
                  {size.measurements.chest && (
                    <div>
                      <span className="text-gray-500">Chest:</span>
                      <p className="font-medium">{size.measurements.chest}"</p>
                    </div>
                  )}
                  {size.measurements.waist && (
                    <div>
                      <span className="text-gray-500">Waist:</span>
                      <p className="font-medium">{size.measurements.waist}"</p>
                    </div>
                  )}
                  {size.measurements.length && (
                    <div>
                      <span className="text-gray-500">Length:</span>
                      <p className="font-medium">{size.measurements.length}"</p>
                    </div>
                  )}
                  {size.measurements.shoulderWidth && (
                    <div>
                      <span className="text-gray-500">Shoulder:</span>
                      <p className="font-medium">{size.measurements.shoulderWidth}"</p>
                    </div>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Recommended For */}
          <motion.div
            className="space-y-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
          >
            <p className="text-xs text-gray-500">Great for:</p>
            <div className="flex flex-wrap gap-1">
              {size.recommendedFor.slice(0, 2).map((rec) => (
                <span
                  key={rec}
                  className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs"
                >
                  {rec}
                </span>
              ))}
            </div>
          </motion.div>

          {/* Confidence Rating */}
          <motion.div
            className="space-y-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.9 }}
          >
            <p className="text-sm text-gray-600">How confident do you feel in this size?</p>
            <div className="flex gap-1">
              {[1, 2, 3, 4, 5].map((rating) => (
                <motion.button
                  key={rating}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={(e) => handleConfidenceRating(rating, e)}
                  className={cn(
                    "w-8 h-8 rounded-full transition-colors text-sm",
                    confidenceLevel >= rating 
                      ? "bg-emerald-400 text-white" 
                      : "bg-gray-200 hover:bg-emerald-200"
                  )}
                >
                  {rating <= 2 ? '😊' : rating <= 4 ? '😍' : '🤩'}
                </motion.button>
              ))}
            </div>
          </motion.div>

          {/* Fit Tips */}
          {size.fitTips.length > 0 && (
            <motion.div
              className="p-2 bg-gray-50 rounded-lg"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.0 }}
            >
              <p className="text-xs text-gray-600">
                💡 <strong>Fit Tip:</strong> {size.fitTips[0]}
              </p>
            </motion.div>
          )}

          {/* Selection Indicator */}
          <AnimatePresence>
            {isSelected && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="absolute inset-0 bg-emerald-500/10 backdrop-blur-sm rounded-2xl flex items-center justify-center"
              >
                <div className="bg-emerald-500 text-white p-3 rounded-full">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export const SizeSelection: React.FC<SizeSelectionProps> = ({
  sizes,
  selectedSize,
  onSelect,
  className
}) => {
  const handleSelect = (size: Size) => {
    // Generate fit preference and confidence boost
    const fitPreferences = ['comfortable', 'flattering', 'perfect fit'];
    const confidenceBoosts = [
      `${size.name} makes me feel confident and comfortable`,
      `Perfect fit that celebrates my body`,
      `This size feels just right for me`,
    ];
    
    const selectedFitPreference = fitPreferences[Math.floor(Math.random() * fitPreferences.length)];
    const selectedConfidenceBoost = confidenceBoosts[Math.floor(Math.random() * confidenceBoosts.length)];
    
    onSelect(size, selectedFitPreference, selectedConfidenceBoost);
  };

  return (
    <div className={cn("space-y-8", className)}>
      {/* Header */}
      <motion.div 
        className="text-center space-y-4"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h2 className="text-3xl md:text-4xl font-bold text-gradient-primary">
          Find your perfect fit
        </h2>
        <p className="text-lg text-gray-700 max-w-2xl mx-auto leading-relaxed">
          Every body is beautiful, and every size is designed to make you feel amazing. 
          Choose the fit that celebrates you.
        </p>
      </motion.div>

      {/* Size Guide Link */}
      <motion.div
        className="text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <button className="text-emerald-600 hover:text-emerald-700 font-medium text-sm underline">
          📏 Need help? View our inclusive size guide
        </button>
      </motion.div>

      {/* Size Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
        {sizes.map((size, index) => (
          <SizeCard
            key={size.id}
            size={size}
            isSelected={selectedSize?.id === size.id}
            onSelect={() => handleSelect(size)}
            index={index}
          />
        ))}
      </div>

      {/* Emotional Encouragement */}
      <AnimatePresence>
        {selectedSize && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="text-center p-6 bg-gradient-to-r from-emerald-50 to-primary-50 rounded-2xl"
          >
            <p className="text-emerald-700 font-medium">
              🎉 Perfect choice! Size {selectedSize.name} is designed to make you feel incredible. {selectedSize.confidenceBooster}
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
