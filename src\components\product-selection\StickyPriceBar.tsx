'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui';
import { SelectionPricing, PriceBarState } from '@/types/product-selection';

interface StickyPriceBarProps {
  pricing: SelectionPricing;
  isVisible: boolean;
  canProceed: boolean;
  currentStep: string;
  totalSteps: number;
  onNext: () => void;
  onShowBreakdown?: () => void;
  className?: string;
}

export const StickyPriceBar: React.FC<StickyPriceBarProps> = ({
  pricing,
  isVisible,
  canProceed,
  currentStep,
  totalSteps,
  onNext,
  onShowBreakdown,
  className
}) => {
  const [priceBarState, setPriceBarState] = useState<PriceBarState>({
    isVisible: false,
    isExpanded: false,
    showBreakdown: false,
    animationState: 'idle',
    ctaText: 'Make it yours'
  });

  const [previousPrice, setPreviousPrice] = useState(pricing.totalPrice);
  const [priceAnimation, setPriceAnimation] = useState<'idle' | 'updating' | 'celebrating'>('idle');

  // Update visibility
  useEffect(() => {
    setPriceBarState(prev => ({ ...prev, isVisible }));
  }, [isVisible]);

  // Handle price changes with animation
  useEffect(() => {
    if (pricing.totalPrice !== previousPrice) {
      setPriceAnimation('updating');
      
      // Celebrate price update
      setTimeout(() => {
        setPriceAnimation('celebrating');
        setTimeout(() => {
          setPriceAnimation('idle');
        }, 1000);
      }, 300);
      
      setPreviousPrice(pricing.totalPrice);
    }
  }, [pricing.totalPrice, previousPrice]);

  // Dynamic CTA text based on step and state
  const getCtaText = () => {
    if (!canProceed) return 'Choose your style';
    
    const stepMessages: Record<string, string> = {
      'product-type': 'Continue your journey',
      'fabric': 'Feel the comfort',
      'color': 'Express yourself',
      'size': 'Make it yours',
      'review': 'Create your masterpiece'
    };
    
    return stepMessages[currentStep] || 'Make it yours';
  };

  // Value messaging based on pricing
  const getValueMessage = () => {
    if (pricing.savingsMessage) return pricing.savingsMessage;
    if (pricing.totalPrice > pricing.basePrice * 1.5) return 'Premium quality investment';
    if (pricing.totalPrice > pricing.basePrice * 1.2) return 'Enhanced with premium features';
    return 'Exceptional value for your unique style';
  };

  const toggleBreakdown = () => {
    setPriceBarState(prev => ({ 
      ...prev, 
      showBreakdown: !prev.showBreakdown,
      isExpanded: !prev.showBreakdown 
    }));
    onShowBreakdown?.();
  };

  const progress = ((totalSteps - 1) / totalSteps) * 100; // Assuming we're showing progress

  return (
    <AnimatePresence>
      {priceBarState.isVisible && (
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
          transition={{ 
            type: "spring", 
            stiffness: 300, 
            damping: 30,
            duration: 0.5 
          }}
          className={cn(
            "fixed bottom-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-lg border-t border-gray-200/50 shadow-2xl",
            className
          )}
        >
          {/* Progress Bar */}
          <div className="absolute top-0 left-0 right-0 h-1 bg-gray-200">
            <motion.div
              className="h-full bg-gradient-to-r from-primary-500 to-warm-500"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            />
          </div>

          {/* Expanded Breakdown */}
          <AnimatePresence>
            {priceBarState.showBreakdown && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="px-4 py-3 border-b border-gray-200/50 bg-gray-50/50"
              >
                <div className="max-w-4xl mx-auto">
                  <h4 className="text-sm font-semibold text-gray-800 mb-2">Your Investment Breakdown</h4>
                  <div className="space-y-2">
                    {pricing.breakdown.map((component, index) => (
                      <motion.div
                        key={component.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-center justify-between text-sm"
                      >
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{component.icon}</span>
                          <div>
                            <span className="font-medium text-gray-800">{component.name}</span>
                            <p className="text-xs text-gray-600">{component.emotionalValue}</p>
                          </div>
                        </div>
                        <span className={cn(
                          "font-semibold",
                          component.type === 'discount' ? 'text-green-600' : 'text-gray-800'
                        )}>
                          {component.type === 'discount' ? '-' : '+'}${Math.abs(component.amount).toFixed(2)}
                        </span>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Price Bar Content */}
          <div className="px-4 py-4">
            <div className="max-w-4xl mx-auto flex items-center justify-between">
              {/* Price Section */}
              <div className="flex items-center gap-4">
                <div className="text-left">
                  <motion.div
                    className="flex items-baseline gap-2"
                    animate={{
                      scale: priceAnimation === 'updating' ? [1, 1.1, 1] : 1,
                      color: priceAnimation === 'celebrating' ? ['#000', '#f59e0b', '#000'] : '#000'
                    }}
                    transition={{ duration: 0.6 }}
                  >
                    <span className="text-2xl md:text-3xl font-bold text-gray-900">
                      ${pricing.totalPrice.toFixed(2)}
                    </span>
                    {pricing.totalPrice !== pricing.basePrice && (
                      <span className="text-lg text-gray-500 line-through">
                        ${pricing.basePrice.toFixed(2)}
                      </span>
                    )}
                  </motion.div>
                  
                  <motion.p 
                    className="text-sm text-gray-600"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    key={getValueMessage()} // Re-animate when message changes
                  >
                    {getValueMessage()}
                  </motion.p>
                </div>

                {/* Breakdown Toggle */}
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={toggleBreakdown}
                  className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
                >
                  <motion.svg
                    className="w-5 h-5"
                    animate={{ rotate: priceBarState.showBreakdown ? 180 : 0 }}
                    transition={{ duration: 0.3 }}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </motion.svg>
                </motion.button>
              </div>

              {/* CTA Section */}
              <div className="flex items-center gap-3">
                {/* Quality Promise */}
                <div className="hidden md:block text-right">
                  <p className="text-xs text-gray-600 max-w-32">
                    {pricing.qualityPromise}
                  </p>
                </div>

                {/* Main CTA Button */}
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={onNext}
                    disabled={!canProceed}
                    className={cn(
                      "relative overflow-hidden min-w-[140px] md:min-w-[180px]",
                      canProceed && "shadow-lg hover:shadow-xl"
                    )}
                  >
                    {/* Button Background Animation */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-primary-600 to-warm-600"
                      animate={{
                        opacity: priceAnimation === 'celebrating' ? [0, 1, 0] : 0
                      }}
                      transition={{ duration: 1 }}
                    />
                    
                    <span className="relative z-10 font-semibold">
                      {getCtaText()}
                    </span>
                    
                    {/* Sparkle Effect */}
                    <AnimatePresence>
                      {priceAnimation === 'celebrating' && (
                        <motion.div
                          initial={{ scale: 0, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          exit={{ scale: 0, opacity: 0 }}
                          className="absolute -top-1 -right-1 text-yellow-400"
                        >
                          ✨
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </Button>
                </motion.div>
              </div>
            </div>
          </div>

          {/* Mobile Quality Promise */}
          <div className="md:hidden px-4 pb-2">
            <p className="text-xs text-center text-gray-600">
              {pricing.qualityPromise}
            </p>
          </div>

          {/* Emotional Encouragement */}
          <AnimatePresence>
            {canProceed && priceAnimation === 'idle' && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="px-4 pb-2"
              >
                <p className="text-center text-xs text-primary-600 font-medium">
                  ✨ You're creating something uniquely yours
                </p>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
