'use client';

import { motion } from 'framer-motion';
import { Button } from '@/components/ui';

interface EmptyStateProps {
  onClearFilters: () => void;
}

export function EmptyState({ onClearFilters }: EmptyStateProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center py-16 px-4"
    >
      <div className="max-w-md mx-auto">
        {/* Illustration */}
        <motion.div
          initial={{ scale: 0.8 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
          className="mb-8"
        >
          <div className="w-32 h-32 mx-auto bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
            <div className="text-6xl">🎨</div>
          </div>
        </motion.div>

        {/* Content */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="space-y-4"
        >
          <h3 className="text-2xl font-bold text-gray-900">
            No Templates Found
          </h3>
          <p className="text-gray-600 leading-relaxed">
            We couldn't find any templates matching your current filters. 
            Try adjusting your search criteria or explore our full collection.
          </p>
        </motion.div>

        {/* Actions */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mt-8 space-y-4"
        >
          <Button
            onClick={onClearFilters}
            className="w-full sm:w-auto"
          >
            ✨ Clear All Filters
          </Button>
          
          <div className="text-sm text-gray-500">
            or{' '}
            <button
              onClick={() => window.location.href = '/create'}
              className="text-primary-600 hover:text-primary-700 font-medium"
            >
              create your own design from scratch
            </button>
          </div>
        </motion.div>

        {/* Suggestions */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="mt-12 pt-8 border-t border-gray-200"
        >
          <h4 className="text-lg font-semibold text-gray-900 mb-4">
            Popular Template Categories
          </h4>
          <div className="grid grid-cols-2 gap-3">
            {[
              { name: 'Minimalist', emoji: '✨', count: '12+' },
              { name: 'Streetwear', emoji: '🔥', count: '8+' },
              { name: 'Professional', emoji: '💼', count: '6+' },
              { name: 'Artistic', emoji: '🎨', count: '10+' },
            ].map((category) => (
              <button
                key={category.name}
                onClick={() => {
                  onClearFilters();
                  // You could add logic here to set a specific filter
                }}
                className="p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors duration-200 text-left"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{category.emoji}</span>
                    <span className="font-medium text-gray-900">{category.name}</span>
                  </div>
                  <span className="text-xs text-gray-500">{category.count}</span>
                </div>
              </button>
            ))}
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
}
