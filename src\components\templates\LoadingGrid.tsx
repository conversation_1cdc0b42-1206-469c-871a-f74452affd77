'use client';

import { motion } from 'framer-motion';

export function LoadingGrid() {
  // Create array of 12 skeleton cards
  const skeletonCards = Array.from({ length: 12 }, (_, i) => i);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {skeletonCards.map((index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className="bg-white rounded-2xl shadow-lg overflow-hidden"
        >
          {/* Image Skeleton */}
          <div className="aspect-square bg-gray-200 animate-pulse relative">
            <div className="absolute top-3 left-3">
              <div className="w-16 h-6 bg-gray-300 rounded-full animate-pulse"></div>
            </div>
            <div className="absolute top-3 right-3">
              <div className="w-12 h-6 bg-gray-300 rounded-full animate-pulse"></div>
            </div>
          </div>

          {/* Content Skeleton */}
          <div className="p-4 space-y-3">
            {/* Mood Tag */}
            <div className="w-24 h-6 bg-gray-200 rounded-full animate-pulse"></div>

            {/* Title and Description */}
            <div className="space-y-2">
              <div className="w-3/4 h-5 bg-gray-200 rounded animate-pulse"></div>
              <div className="w-full h-4 bg-gray-200 rounded animate-pulse"></div>
              <div className="w-2/3 h-4 bg-gray-200 rounded animate-pulse"></div>
            </div>

            {/* Lifestyle Context */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-4 bg-gray-200 rounded animate-pulse"></div>
                <div className="w-16 h-4 bg-gray-200 rounded animate-pulse"></div>
              </div>
              <div className="w-12 h-4 bg-gray-200 rounded animate-pulse"></div>
            </div>

            {/* Style Keywords */}
            <div className="flex space-x-2">
              <div className="w-12 h-6 bg-gray-200 rounded-full animate-pulse"></div>
              <div className="w-16 h-6 bg-gray-200 rounded-full animate-pulse"></div>
              <div className="w-10 h-6 bg-gray-200 rounded-full animate-pulse"></div>
            </div>

            {/* Product Info */}
            <div className="pt-2 border-t border-gray-100">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="w-20 h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div className="w-16 h-3 bg-gray-200 rounded animate-pulse"></div>
                </div>
                <div className="text-right space-y-1">
                  <div className="w-24 h-3 bg-gray-200 rounded animate-pulse"></div>
                  <div className="w-12 h-6 bg-gray-200 rounded animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
}

// Alternative minimal loading state
export function LoadingSpinner() {
  return (
    <div className="flex items-center justify-center py-16">
      <motion.div
        className="w-12 h-12 border-4 border-primary-200 border-t-primary-500 rounded-full"
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
      />
    </div>
  );
}

// Loading state with message
export function LoadingWithMessage({ message = 'Loading templates...' }: { message?: string }) {
  return (
    <div className="flex flex-col items-center justify-center py-16 space-y-4">
      <motion.div
        className="w-12 h-12 border-4 border-primary-200 border-t-primary-500 rounded-full"
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
      />
      <motion.p
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="text-gray-600 font-medium"
      >
        {message}
      </motion.p>
    </div>
  );
}
