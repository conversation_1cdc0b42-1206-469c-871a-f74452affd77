'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { Button } from '@/components/ui';

interface Template {
  id: string;
  name: string;
  description: string;
  moodTag: string;
  styleKeywords: string[];
  targetAudience: string;
  designData: any;
  previewImage: string;
  lifestyleContext: string[];
  usageCount: number;
  isFeatured: boolean;
  createdAt: string;
  product: {
    id: string;
    name: string;
    category: string;
    subcategory?: string;
    basePrice: number;
    heroImage: string;
    moodTags: string[];
    lifestyleTags: string[];
  };
}

interface TemplateCardProps {
  template: Template;
  onApply: () => void;
  onPreview?: (templateId: string) => void;
}

export function TemplateCard({ template, onApply, onPreview }: TemplateCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  // Get mood color based on mood tag
  const getMoodColor = (moodTag: string) => {
    const moodColors: Record<string, string> = {
      'Bold Streetwear': 'from-red-500 to-orange-500',
      'Minimalist Chic': 'from-gray-400 to-gray-600',
      'Vintage Rebel': 'from-amber-600 to-yellow-600',
      'Artistic Expression': 'from-purple-500 to-pink-500',
      'Nature Lover': 'from-green-500 to-emerald-500',
      'Urban Professional': 'from-blue-600 to-indigo-600',
      'Fitness Enthusiast': 'from-orange-500 to-red-500',
      'Tech Innovator': 'from-cyan-500 to-blue-500',
      'Default': 'from-primary-500 to-secondary-500',
    };
    return moodColors[moodTag] || moodColors['Default'];
  };

  // Get lifestyle emoji
  const getLifestyleEmoji = (context: string[]) => {
    const emojiMap: Record<string, string> = {
      'work': '💼',
      'weekend': '🌟',
      'date night': '💕',
      'gym': '💪',
      'casual': '😎',
      'formal': '🎩',
      'street': '🏙️',
      'outdoor': '🌲',
      'party': '🎉',
      'travel': '✈️',
    };
    
    return context.slice(0, 3).map(c => emojiMap[c.toLowerCase()] || '✨').join(' ');
  };

  return (
    <motion.div
      className="group relative bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300"
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      whileHover={{ y: -4 }}
      layout
    >
      {/* Featured Badge */}
      {template.isFeatured && (
        <div className="absolute top-3 left-3 z-10">
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
            ⭐ Featured
          </div>
        </div>
      )}

      {/* Usage Count Badge */}
      <div className="absolute top-3 right-3 z-10">
        <div className="bg-black/70 text-white text-xs font-medium px-2 py-1 rounded-full backdrop-blur-sm">
          {template.usageCount} uses
        </div>
      </div>

      {/* Preview Image */}
      <div className="relative aspect-square overflow-hidden bg-gray-100">
        <Image
          src={template.previewImage}
          alt={template.name}
          fill
          className={`object-cover transition-all duration-500 ${
            isHovered ? 'scale-110' : 'scale-100'
          } ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
          onLoad={() => setImageLoaded(true)}
        />
        
        {/* Loading placeholder */}
        {!imageLoaded && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
            <div className="text-gray-400 text-sm">Loading...</div>
          </div>
        )}

        {/* Hover Overlay */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"
          initial={{ opacity: 0 }}
          animate={{ opacity: isHovered ? 1 : 0 }}
          transition={{ duration: 0.3 }}
        />

        {/* Quick Apply Button */}
        <motion.div
          className="absolute inset-0 flex items-center justify-center"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ 
            opacity: isHovered ? 1 : 0,
            scale: isHovered ? 1 : 0.8 
          }}
          transition={{ duration: 0.3 }}
        >
          <Button
            onClick={onApply}
            className="bg-white text-gray-900 hover:bg-gray-100 font-semibold px-6 py-3 rounded-full shadow-lg transform hover:scale-105 transition-all duration-200"
          >
            ✨ Wear This Look
          </Button>
        </motion.div>
      </div>

      {/* Content */}
      <div className="p-4 space-y-3">
        {/* Mood Tag */}
        <div className={`inline-block bg-gradient-to-r ${getMoodColor(template.moodTag)} text-white text-xs font-bold px-3 py-1 rounded-full`}>
          {template.moodTag}
        </div>

        {/* Title and Description */}
        <div>
          <h3 className="font-bold text-lg text-gray-900 mb-1 line-clamp-1">
            {template.name}
          </h3>
          <p className="text-gray-600 text-sm line-clamp-2 leading-relaxed">
            {template.description}
          </p>
        </div>

        {/* Lifestyle Context */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-lg">{getLifestyleEmoji(template.lifestyleContext)}</span>
            <span className="text-xs text-gray-500 capitalize">
              {template.lifestyleContext.slice(0, 2).join(', ')}
              {template.lifestyleContext.length > 2 && ` +${template.lifestyleContext.length - 2}`}
            </span>
          </div>
          <div className="text-xs text-gray-500">
            {template.product.category}
          </div>
        </div>

        {/* Style Keywords */}
        <div className="flex flex-wrap gap-1">
          {template.styleKeywords.slice(0, 3).map((keyword, index) => (
            <span
              key={index}
              className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full"
            >
              {keyword}
            </span>
          ))}
          {template.styleKeywords.length > 3 && (
            <span className="text-xs text-gray-500">
              +{template.styleKeywords.length - 3}
            </span>
          )}
        </div>

        {/* Product Info */}
        <div className="pt-2 border-t border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm font-medium text-gray-900">
                {template.product.name}
              </div>
              <div className="text-xs text-gray-500">
                Starting at ${template.product.basePrice}
              </div>
            </div>
            <div className="text-right">
              <div className="text-xs text-gray-500 mb-1">
                For {template.targetAudience}
              </div>
              <div className="flex space-x-1">
                {onPreview && (
                  <Button
                    onClick={() => onPreview(template.id)}
                    size="sm"
                    variant="outline"
                    className="text-xs px-2 py-1"
                  >
                    👁️
                  </Button>
                )}
                <Button
                  onClick={onApply}
                  size="sm"
                  className="text-xs px-3 py-1"
                >
                  Apply
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Hover Effect Border */}
      <motion.div
        className="absolute inset-0 border-2 border-transparent rounded-2xl"
        animate={{
          borderColor: isHovered ? 'rgb(59 130 246)' : 'transparent',
        }}
        transition={{ duration: 0.3 }}
      />
    </motion.div>
  );
}
