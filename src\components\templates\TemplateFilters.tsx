'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui';

interface TemplateFiltersData {
  moodTags: string[];
  styleKeywords: string[];
  targetAudiences: string[];
  lifestyleContexts: string[];
}

interface TemplateFiltersProps {
  filters: TemplateFiltersData;
  selectedMoodTag: string;
  selectedStyleKeywords: string[];
  selectedLifestyleContext: string;
  onMoodTagChange: (moodTag: string) => void;
  onStyleKeywordsChange: (keywords: string[]) => void;
  onLifestyleContextChange: (context: string) => void;
  onClearFilters: () => void;
}

export function TemplateFilters({
  filters,
  selectedMoodTag,
  selectedStyleKeywords,
  selectedLifestyleContext,
  onMoodTagChange,
  onStyleKeywordsChange,
  onLifestyleContextChange,
  onClearFilters,
}: TemplateFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  // Check if any filters are active
  const hasActiveFilters = selectedMoodTag || selectedStyleKeywords.length > 0 || selectedLifestyleContext;

  // Handle style keyword toggle
  const toggleStyleKeyword = (keyword: string) => {
    if (selectedStyleKeywords.includes(keyword)) {
      onStyleKeywordsChange(selectedStyleKeywords.filter(k => k !== keyword));
    } else {
      onStyleKeywordsChange([...selectedStyleKeywords, keyword]);
    }
  };

  // Get mood tag emoji
  const getMoodEmoji = (moodTag: string) => {
    const emojiMap: Record<string, string> = {
      'Bold Streetwear': '🔥',
      'Minimalist Chic': '✨',
      'Vintage Rebel': '🎸',
      'Artistic Expression': '🎨',
      'Nature Lover': '🌿',
      'Urban Professional': '💼',
      'Fitness Enthusiast': '💪',
      'Tech Innovator': '🚀',
    };
    return emojiMap[moodTag] || '🎯';
  };

  // Get lifestyle context emoji
  const getLifestyleEmoji = (context: string) => {
    const emojiMap: Record<string, string> = {
      'work': '💼',
      'weekend': '🌟',
      'date night': '💕',
      'gym': '💪',
      'casual': '😎',
      'formal': '🎩',
      'street': '🏙️',
      'outdoor': '🌲',
      'party': '🎉',
      'travel': '✈️',
      'everyday': '☀️',
      'professional': '👔',
      'modern': '🔮',
      'tech': '💻',
    };
    return emojiMap[context.toLowerCase()] || '✨';
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      {/* Filter Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <div className="flex items-center space-x-3">
          <h3 className="font-semibold text-gray-900">Filter Templates</h3>
          {hasActiveFilters && (
            <span className="bg-primary-100 text-primary-700 text-xs font-medium px-2 py-1 rounded-full">
              {(selectedMoodTag ? 1 : 0) + selectedStyleKeywords.length + (selectedLifestyleContext ? 1 : 0)} active
            </span>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {hasActiveFilters && (
            <Button
              onClick={onClearFilters}
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-gray-700"
            >
              Clear All
            </Button>
          )}
          <Button
            onClick={() => setIsExpanded(!isExpanded)}
            variant="ghost"
            size="sm"
            className="text-gray-500 hover:text-gray-700"
          >
            {isExpanded ? '▲ Less' : '▼ More'}
          </Button>
        </div>
      </div>

      {/* Quick Filters (Always Visible) */}
      <div className="p-4 space-y-4">
        {/* Mood Tags */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Mood & Style
          </label>
          <div className="flex flex-wrap gap-2">
            {filters.moodTags.slice(0, isExpanded ? filters.moodTags.length : 6).map((moodTag) => (
              <button
                key={moodTag}
                onClick={() => onMoodTagChange(selectedMoodTag === moodTag ? '' : moodTag)}
                className={`inline-flex items-center space-x-1 px-3 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                  selectedMoodTag === moodTag
                    ? 'bg-primary-500 text-white shadow-md'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span>{getMoodEmoji(moodTag)}</span>
                <span>{moodTag}</span>
              </button>
            ))}
            {!isExpanded && filters.moodTags.length > 6 && (
              <button
                onClick={() => setIsExpanded(true)}
                className="px-3 py-2 rounded-full text-sm text-gray-500 hover:text-gray-700 border border-dashed border-gray-300"
              >
                +{filters.moodTags.length - 6} more
              </button>
            )}
          </div>
        </div>

        {/* Lifestyle Context */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Perfect For
          </label>
          <div className="flex flex-wrap gap-2">
            {filters.lifestyleContexts.slice(0, isExpanded ? filters.lifestyleContexts.length : 8).map((context) => (
              <button
                key={context}
                onClick={() => onLifestyleContextChange(selectedLifestyleContext === context ? '' : context)}
                className={`inline-flex items-center space-x-1 px-3 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                  selectedLifestyleContext === context
                    ? 'bg-accent-500 text-white shadow-md'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span>{getLifestyleEmoji(context)}</span>
                <span className="capitalize">{context}</span>
              </button>
            ))}
            {!isExpanded && filters.lifestyleContexts.length > 8 && (
              <button
                onClick={() => setIsExpanded(true)}
                className="px-3 py-2 rounded-full text-sm text-gray-500 hover:text-gray-700 border border-dashed border-gray-300"
              >
                +{filters.lifestyleContexts.length - 8} more
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Expanded Filters */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="border-t border-gray-100"
          >
            <div className="p-4 space-y-4">
              {/* Style Keywords */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Style Keywords
                </label>
                <div className="flex flex-wrap gap-2">
                  {filters.styleKeywords.map((keyword) => (
                    <button
                      key={keyword}
                      onClick={() => toggleStyleKeyword(keyword)}
                      className={`px-3 py-1 rounded-full text-sm font-medium transition-all duration-200 ${
                        selectedStyleKeywords.includes(keyword)
                          ? 'bg-secondary-500 text-white shadow-md'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {keyword}
                    </button>
                  ))}
                </div>
              </div>

              {/* Target Audiences */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Designed For
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {filters.targetAudiences.map((audience) => (
                    <div
                      key={audience}
                      className="text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-lg capitalize"
                    >
                      {audience}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="bg-gray-50 px-4 py-3 border-t border-gray-100">
          <div className="flex items-center justify-between">
            <div className="flex flex-wrap gap-2">
              {selectedMoodTag && (
                <span className="inline-flex items-center space-x-1 bg-primary-100 text-primary-700 text-xs font-medium px-2 py-1 rounded-full">
                  <span>{getMoodEmoji(selectedMoodTag)}</span>
                  <span>{selectedMoodTag}</span>
                  <button
                    onClick={() => onMoodTagChange('')}
                    className="ml-1 text-primary-500 hover:text-primary-700"
                  >
                    ×
                  </button>
                </span>
              )}
              {selectedLifestyleContext && (
                <span className="inline-flex items-center space-x-1 bg-accent-100 text-accent-700 text-xs font-medium px-2 py-1 rounded-full">
                  <span>{getLifestyleEmoji(selectedLifestyleContext)}</span>
                  <span className="capitalize">{selectedLifestyleContext}</span>
                  <button
                    onClick={() => onLifestyleContextChange('')}
                    className="ml-1 text-accent-500 hover:text-accent-700"
                  >
                    ×
                  </button>
                </span>
              )}
              {selectedStyleKeywords.map((keyword) => (
                <span
                  key={keyword}
                  className="inline-flex items-center space-x-1 bg-secondary-100 text-secondary-700 text-xs font-medium px-2 py-1 rounded-full"
                >
                  <span>{keyword}</span>
                  <button
                    onClick={() => toggleStyleKeyword(keyword)}
                    className="ml-1 text-secondary-500 hover:text-secondary-700"
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
