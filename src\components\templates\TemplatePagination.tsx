'use client';

import { motion } from 'framer-motion';
import { Button } from '@/components/ui';

interface PaginationData {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  limit: number;
}

interface TemplatePaginationProps {
  pagination: PaginationData;
  onPageChange: (page: number) => void;
}

export function TemplatePagination({ pagination, onPageChange }: TemplatePaginationProps) {
  const { currentPage, totalPages, totalCount, hasNextPage, hasPrevPage, limit } = pagination;

  // Calculate page numbers to show
  const getPageNumbers = () => {
    const delta = 2; // Number of pages to show on each side of current page
    const range = [];
    const rangeWithDots = [];

    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  const pageNumbers = getPageNumbers();

  // Calculate result range
  const startResult = (currentPage - 1) * limit + 1;
  const endResult = Math.min(currentPage * limit, totalCount);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
    >
      {/* Results Summary */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="text-sm text-gray-600">
          Showing <span className="font-medium text-gray-900">{startResult}</span> to{' '}
          <span className="font-medium text-gray-900">{endResult}</span> of{' '}
          <span className="font-medium text-gray-900">{totalCount}</span> templates
        </div>
        
        {/* Quick Jump */}
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">Jump to page:</span>
          <select
            value={currentPage}
            onChange={(e) => onPageChange(parseInt(e.target.value))}
            className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <option key={page} value={page}>
                {page}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Pagination Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
        {/* Previous Button */}
        <Button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={!hasPrevPage}
          variant="outline"
          className="flex items-center space-x-2 w-full sm:w-auto"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          <span>Previous</span>
        </Button>

        {/* Page Numbers */}
        <div className="flex items-center space-x-1 overflow-x-auto">
          {pageNumbers.map((page, index) => (
            <motion.div
              key={index}
              whileHover={{ scale: page !== '...' ? 1.05 : 1 }}
              whileTap={{ scale: page !== '...' ? 0.95 : 1 }}
            >
              {page === '...' ? (
                <span className="px-3 py-2 text-gray-400">...</span>
              ) : (
                <button
                  onClick={() => onPageChange(page as number)}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                    currentPage === page
                      ? 'bg-primary-500 text-white shadow-md'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  {page}
                </button>
              )}
            </motion.div>
          ))}
        </div>

        {/* Next Button */}
        <Button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={!hasNextPage}
          variant="outline"
          className="flex items-center space-x-2 w-full sm:w-auto"
        >
          <span>Next</span>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </Button>
      </div>

      {/* Mobile-friendly page info */}
      <div className="mt-4 pt-4 border-t border-gray-100 sm:hidden">
        <div className="text-center text-sm text-gray-600">
          Page {currentPage} of {totalPages}
        </div>
      </div>
    </motion.div>
  );
}
