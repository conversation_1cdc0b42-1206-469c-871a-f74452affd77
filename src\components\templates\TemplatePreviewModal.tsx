'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { Button } from '@/components/ui';

interface Template {
  id: string;
  name: string;
  description: string;
  moodTag: string;
  styleKeywords: string[];
  targetAudience: string;
  designData: any;
  previewImage: string;
  lifestyleContext: string[];
  usageCount: number;
  isFeatured: boolean;
  createdAt: string;
  product: {
    id: string;
    name: string;
    category: string;
    subcategory?: string;
    basePrice: number;
    heroImage: string;
    moodTags: string[];
    lifestyleTags: string[];
  };
}

interface TemplatePreviewData {
  template: Template;
  product: any;
  pricing: {
    basePrice: number;
    customizationFee: number;
    estimatedTotal: number;
    currency: string;
  };
  compatibility: {
    isCompatible: boolean;
    notes: string;
  };
}

interface TemplatePreviewModalProps {
  templateId: string | null;
  isOpen: boolean;
  onClose: () => void;
  onApply: (templateId: string) => void;
  productId?: string; // Optional product to apply template to
}

export function TemplatePreviewModal({
  templateId,
  isOpen,
  onClose,
  onApply,
  productId,
}: TemplatePreviewModalProps) {
  const [previewData, setPreviewData] = useState<TemplatePreviewData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch template preview data
  useEffect(() => {
    const fetchPreviewData = async () => {
      if (!templateId || !isOpen) return;

      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (productId) {
          params.append('productId', productId);
        }

        const response = await fetch(`/api/templates/${templateId}/apply?${params}`);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Failed to load template preview');
        }

        if (data.success) {
          setPreviewData(data.data);
        } else {
          throw new Error(data.error || 'Failed to load template preview');
        }
      } catch (err) {
        console.error('Error fetching template preview:', err);
        setError(err instanceof Error ? err.message : 'Failed to load template preview');
      } finally {
        setLoading(false);
      }
    };

    fetchPreviewData();
  }, [templateId, isOpen, productId]);

  const handleApply = () => {
    if (templateId) {
      onApply(templateId);
      onClose();
    }
  };

  // Get mood color based on mood tag
  const getMoodColor = (moodTag: string) => {
    const moodColors: Record<string, string> = {
      'Bold Streetwear': 'from-red-500 to-orange-500',
      'Minimalist Chic': 'from-gray-400 to-gray-600',
      'Vintage Rebel': 'from-amber-600 to-yellow-600',
      'Artistic Expression': 'from-purple-500 to-pink-500',
      'Nature Lover': 'from-green-500 to-emerald-500',
      'Urban Professional': 'from-blue-600 to-indigo-600',
      'Fitness Enthusiast': 'from-orange-500 to-red-500',
      'Tech Innovator': 'from-cyan-500 to-blue-500',
      'Default': 'from-primary-500 to-secondary-500',
    };
    return moodColors[moodTag] || moodColors['Default'];
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            {loading ? (
              <div className="flex items-center justify-center py-16">
                <motion.div
                  className="w-12 h-12 border-4 border-primary-200 border-t-primary-500 rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                />
                <span className="ml-3 text-gray-600">Loading preview...</span>
              </div>
            ) : error ? (
              <div className="p-8 text-center">
                <div className="text-red-600 mb-4">⚠️ {error}</div>
                <Button onClick={onClose}>Close</Button>
              </div>
            ) : previewData ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 h-full">
                {/* Left Side - Template Preview */}
                <div className="relative bg-gray-50 flex items-center justify-center p-8">
                  <div className="relative w-full max-w-sm aspect-square">
                    <Image
                      src={previewData.template.previewImage}
                      alt={previewData.template.name}
                      fill
                      className="object-cover rounded-xl shadow-lg"
                    />
                  </div>
                  
                  {/* Close Button */}
                  <button
                    onClick={onClose}
                    className="absolute top-4 right-4 w-10 h-10 bg-white/90 hover:bg-white rounded-full flex items-center justify-center text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    ×
                  </button>
                </div>

                {/* Right Side - Template Details */}
                <div className="p-8 flex flex-col">
                  {/* Header */}
                  <div className="mb-6">
                    <div className={`inline-block bg-gradient-to-r ${getMoodColor(previewData.template.moodTag)} text-white text-sm font-bold px-3 py-1 rounded-full mb-3`}>
                      {previewData.template.moodTag}
                    </div>
                    <h2 className="text-3xl font-bold text-gray-900 mb-2">
                      {previewData.template.name}
                    </h2>
                    <p className="text-gray-600 leading-relaxed">
                      {previewData.template.description}
                    </p>
                  </div>

                  {/* Details */}
                  <div className="space-y-4 mb-6">
                    {/* Target Audience */}
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">Perfect For</h4>
                      <p className="text-gray-600 capitalize">{previewData.template.targetAudience}</p>
                    </div>

                    {/* Lifestyle Context */}
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Lifestyle</h4>
                      <div className="flex flex-wrap gap-2">
                        {previewData.template.lifestyleContext.map((context, index) => (
                          <span
                            key={index}
                            className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm capitalize"
                          >
                            {context}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Style Keywords */}
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Style</h4>
                      <div className="flex flex-wrap gap-2">
                        {previewData.template.styleKeywords.map((keyword, index) => (
                          <span
                            key={index}
                            className="bg-primary-100 text-primary-700 px-3 py-1 rounded-full text-sm"
                          >
                            {keyword}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Product Info */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900 mb-2">Product</h4>
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-gray-900">{previewData.product.name}</div>
                          <div className="text-sm text-gray-600">{previewData.product.category}</div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-gray-900">
                            ${previewData.pricing.estimatedTotal}
                          </div>
                          <div className="text-xs text-gray-500">
                            Base: ${previewData.pricing.basePrice} + Custom: ${previewData.pricing.customizationFee}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Compatibility */}
                    {previewData.compatibility.notes && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <div className="text-sm text-blue-800">
                          💡 {previewData.compatibility.notes}
                        </div>
                      </div>
                    )}

                    {/* Usage Stats */}
                    <div className="text-sm text-gray-500">
                      ⭐ Used {previewData.template.usageCount} times by other customers
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="mt-auto space-y-3">
                    <Button
                      onClick={handleApply}
                      className="w-full text-lg py-3"
                    >
                      ✨ Wear This Look
                    </Button>
                    <Button
                      onClick={onClose}
                      variant="outline"
                      className="w-full"
                    >
                      Browse More Templates
                    </Button>
                  </div>
                </div>
              </div>
            ) : null}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
