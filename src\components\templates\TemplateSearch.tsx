'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Input, Button } from '@/components/ui';

interface TemplateSearchProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  sortBy: 'popular' | 'recent' | 'name';
  onSortChange: (sort: 'popular' | 'recent' | 'name') => void;
  showFeaturedOnly: boolean;
  onFeaturedToggle: (featured: boolean) => void;
}

export function TemplateSearch({
  searchQuery,
  onSearchChange,
  sortBy,
  onSortChange,
  showFeaturedOnly,
  onFeaturedToggle,
}: TemplateSearchProps) {
  const [isFocused, setIsFocused] = useState(false);

  const sortOptions = [
    { value: 'popular', label: 'Most Popular', icon: '🔥' },
    { value: 'recent', label: 'Newest First', icon: '⭐' },
    { value: 'name', label: 'A to Z', icon: '📝' },
  ] as const;

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
      <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center">
        {/* Search Input */}
        <div className="flex-1 relative">
          <motion.div
            className={`relative transition-all duration-200 ${
              isFocused ? 'scale-[1.02]' : 'scale-100'
            }`}
          >
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg
                className="h-5 w-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            <Input
              type="text"
              placeholder="Search templates by name, mood, or style..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              className="pl-10 pr-4 py-3 w-full border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
            {searchQuery && (
              <button
                onClick={() => onSearchChange('')}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
              >
                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </motion.div>
        </div>

        {/* Sort Options */}
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700 whitespace-nowrap">
            Sort by:
          </span>
          <div className="flex bg-gray-100 rounded-lg p-1">
            {sortOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => onSortChange(option.value)}
                className={`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 whitespace-nowrap ${
                  sortBy === option.value
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <span>{option.icon}</span>
                <span>{option.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Featured Toggle */}
        <div className="flex items-center space-x-3">
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              checked={showFeaturedOnly}
              onChange={(e) => onFeaturedToggle(e.target.checked)}
              className="sr-only"
            />
            <motion.div
              className={`relative w-12 h-6 rounded-full transition-colors duration-200 ${
                showFeaturedOnly ? 'bg-primary-500' : 'bg-gray-300'
              }`}
              whileTap={{ scale: 0.95 }}
            >
              <motion.div
                className="absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full shadow-md"
                animate={{
                  x: showFeaturedOnly ? 24 : 0,
                }}
                transition={{ type: 'spring', stiffness: 500, damping: 30 }}
              />
            </motion.div>
            <span className="text-sm font-medium text-gray-700 whitespace-nowrap">
              ⭐ Featured Only
            </span>
          </label>
        </div>
      </div>

      {/* Search Results Summary */}
      {searchQuery && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-3 pt-3 border-t border-gray-100"
        >
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Searching for: <span className="font-medium text-gray-900">"{searchQuery}"</span>
            </div>
            <button
              onClick={() => onSearchChange('')}
              className="text-sm text-primary-600 hover:text-primary-700 font-medium"
            >
              Clear search
            </button>
          </div>
        </motion.div>
      )}

      {/* Quick Search Suggestions */}
      {!searchQuery && (
        <div className="mt-3 pt-3 border-t border-gray-100">
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-sm text-gray-500">Popular searches:</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {[
              'minimalist',
              'streetwear',
              'vintage',
              'professional',
              'artistic',
              'nature',
            ].map((suggestion) => (
              <button
                key={suggestion}
                onClick={() => onSearchChange(suggestion)}
                className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full hover:bg-gray-200 transition-colors duration-200"
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
