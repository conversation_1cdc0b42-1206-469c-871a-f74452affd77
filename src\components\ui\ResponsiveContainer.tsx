'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  mobileFirst?: boolean;
  touchOptimized?: boolean;
  reducedMotion?: boolean;
}

interface UseResponsiveReturn {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouchDevice: boolean;
  prefersReducedMotion: boolean;
  screenWidth: number;
}

// Custom hook for responsive behavior
export const useResponsive = (): UseResponsiveReturn => {
  const [screenData, setScreenData] = useState<UseResponsiveReturn>({
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    isTouchDevice: false,
    prefersReducedMotion: false,
    screenWidth: 0
  });

  useEffect(() => {
    const updateScreenData = () => {
      const width = window.innerWidth;
      const isMobile = width <= 640;
      const isTablet = width > 640 && width <= 1024;
      const isDesktop = width > 1024;
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

      setScreenData({
        isMobile,
        isTablet,
        isDesktop,
        isTouchDevice,
        prefersReducedMotion,
        screenWidth: width
      });
    };

    // Initial check
    updateScreenData();

    // Listen for resize events
    window.addEventListener('resize', updateScreenData);
    
    // Listen for reduced motion preference changes
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    mediaQuery.addEventListener('change', updateScreenData);

    return () => {
      window.removeEventListener('resize', updateScreenData);
      mediaQuery.removeEventListener('change', updateScreenData);
    };
  }, []);

  return screenData;
};

// Responsive grid component
interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  mobileColumns?: number;
  tabletColumns?: number;
  desktopColumns?: number;
  gap?: 'sm' | 'md' | 'lg' | 'xl';
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  className,
  mobileColumns = 1,
  tabletColumns = 2,
  desktopColumns = 3,
  gap = 'md'
}) => {
  const gapClasses = {
    sm: 'gap-2 md:gap-4',
    md: 'gap-4 md:gap-6',
    lg: 'gap-6 md:gap-8',
    xl: 'gap-8 md:gap-12'
  };

  return (
    <div
      className={cn(
        'grid',
        `grid-cols-${mobileColumns}`,
        `md:grid-cols-${tabletColumns}`,
        `lg:grid-cols-${desktopColumns}`,
        gapClasses[gap],
        className
      )}
    >
      {children}
    </div>
  );
};

// Touch-optimized button wrapper
interface TouchButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
  hapticFeedback?: boolean;
}

export const TouchButton: React.FC<TouchButtonProps> = ({
  children,
  onClick,
  className,
  disabled = false,
  hapticFeedback = true
}) => {
  const { isTouchDevice } = useResponsive();

  const handleClick = () => {
    if (disabled) return;
    
    // Haptic feedback on supported devices
    if (hapticFeedback && isTouchDevice && 'vibrate' in navigator) {
      navigator.vibrate(10); // Very light haptic feedback
    }
    
    onClick?.();
  };

  return (
    <button
      onClick={handleClick}
      disabled={disabled}
      className={cn(
        // Base styles
        'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200',
        // Touch-optimized sizing
        isTouchDevice ? 'min-h-[44px] min-w-[44px] py-3 px-4' : 'py-2 px-3',
        // Touch-specific interactions
        isTouchDevice && 'active:scale-95 active:bg-opacity-80',
        // Desktop hover effects
        !isTouchDevice && 'hover:scale-105 hover:shadow-lg',
        // Disabled state
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
    >
      {children}
    </button>
  );
};

// Responsive text component
interface ResponsiveTextProps {
  children: React.ReactNode;
  variant?: 'h1' | 'h2' | 'h3' | 'body' | 'caption';
  className?: string;
}

export const ResponsiveText: React.FC<ResponsiveTextProps> = ({
  children,
  variant = 'body',
  className
}) => {
  const textClasses = {
    h1: 'text-2xl md:text-3xl lg:text-4xl font-bold leading-tight',
    h2: 'text-xl md:text-2xl lg:text-3xl font-semibold leading-snug',
    h3: 'text-lg md:text-xl lg:text-2xl font-medium leading-normal',
    body: 'text-base md:text-lg leading-relaxed',
    caption: 'text-sm md:text-base leading-normal'
  };

  const Component = variant.startsWith('h') ? variant as 'h1' | 'h2' | 'h3' : 'p';

  return (
    <Component className={cn(textClasses[variant], className)}>
      {children}
    </Component>
  );
};

// Main responsive container
export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className,
  mobileFirst = true,
  touchOptimized = true,
  reducedMotion = true
}) => {
  const { isTouchDevice, prefersReducedMotion } = useResponsive();

  return (
    <div
      className={cn(
        // Base container styles
        'w-full',
        // Mobile-first padding
        mobileFirst && 'px-4 md:px-6 lg:px-8',
        // Touch optimizations
        touchOptimized && isTouchDevice && 'touch-target',
        // Reduced motion respect
        reducedMotion && prefersReducedMotion && 'respect-motion-preference',
        className
      )}
    >
      {children}
    </div>
  );
};

// Responsive spacing utility
interface ResponsiveSpacingProps {
  children: React.ReactNode;
  vertical?: 'sm' | 'md' | 'lg' | 'xl';
  horizontal?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export const ResponsiveSpacing: React.FC<ResponsiveSpacingProps> = ({
  children,
  vertical = 'md',
  horizontal = 'md',
  className
}) => {
  const verticalClasses = {
    sm: 'py-2 md:py-4',
    md: 'py-4 md:py-6 lg:py-8',
    lg: 'py-6 md:py-8 lg:py-12',
    xl: 'py-8 md:py-12 lg:py-16'
  };

  const horizontalClasses = {
    sm: 'px-2 md:px-4',
    md: 'px-4 md:px-6 lg:px-8',
    lg: 'px-6 md:px-8 lg:px-12',
    xl: 'px-8 md:px-12 lg:px-16'
  };

  return (
    <div
      className={cn(
        verticalClasses[vertical],
        horizontalClasses[horizontal],
        className
      )}
    >
      {children}
    </div>
  );
};

// Responsive image component
interface ResponsiveImageProps {
  src: string;
  alt: string;
  className?: string;
  aspectRatio?: 'square' | 'video' | 'portrait' | 'landscape';
  priority?: boolean;
}

export const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  src,
  alt,
  className,
  aspectRatio = 'landscape',
  priority = false
}) => {
  const aspectClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    portrait: 'aspect-[3/4]',
    landscape: 'aspect-[4/3]'
  };

  return (
    <div className={cn('relative overflow-hidden rounded-lg', aspectClasses[aspectRatio], className)}>
      <img
        src={src}
        alt={alt}
        className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
        loading={priority ? 'eager' : 'lazy'}
      />
    </div>
  );
};

// Export all components and hooks
export default ResponsiveContainer;
