/**
 * AI Try-On Hook
 * 
 * React hook for managing AI try-on functionality including:
 * - Image upload and validation
 * - API communication
 * - Job status polling
 * - Error handling and retry logic
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { AiTryOnRequest, AiTryOnResponse, AiTryOnStatus } from '@/types';

export interface AiTryOnState {
  // Current job state
  jobId: string | null;
  status: AiTryOnStatus;
  resultImageUrl: string | null;
  confidence: number | null;
  
  // UI state
  isLoading: boolean;
  isUploading: boolean;
  isProcessing: boolean;
  error: string | null;
  
  // Upload state
  selectedImage: File | null;
  imagePreview: string | null;
  
  // Progress tracking
  progress: number; // 0-100
  estimatedTimeRemaining: number | null; // seconds
}

export interface AiTryOnActions {
  // Image handling
  selectImage: (file: File) => Promise<void>;
  clearImage: () => void;
  
  // Try-on generation
  generateTryOn: (customizationId: string, userId: string, contextScene?: string) => Promise<void>;
  
  // Job management
  checkJobStatus: (jobId: string) => Promise<void>;
  cancelJob: () => void;
  retryJob: () => Promise<void>;
  
  // State management
  reset: () => void;
  clearError: () => void;
}

const INITIAL_STATE: AiTryOnState = {
  jobId: null,
  status: 'PENDING',
  resultImageUrl: null,
  confidence: null,
  isLoading: false,
  isUploading: false,
  isProcessing: false,
  error: null,
  selectedImage: null,
  imagePreview: null,
  progress: 0,
  estimatedTimeRemaining: null,
};

const POLLING_INTERVAL = 2000; // 2 seconds
const MAX_POLLING_TIME = 300000; // 5 minutes

export function useAiTryOn(): AiTryOnState & AiTryOnActions {
  const [state, setState] = useState<AiTryOnState>(INITIAL_STATE);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number | null>(null);
  const lastRequestRef = useRef<AiTryOnRequest | null>(null);

  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
    };
  }, []);

  // Select and validate image
  const selectImage = useCallback(async (file: File) => {
    setState(prev => ({ ...prev, isUploading: true, error: null }));

    try {
      // Validate file type
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        throw new Error('Please select a valid image file (JPEG, PNG, or WebP)');
      }

      // Validate file size (5MB limit)
      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize) {
        throw new Error('Image file size must be less than 5MB');
      }

      // Create preview
      const preview = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.onerror = () => reject(new Error('Failed to read image file'));
        reader.readAsDataURL(file);
      });

      setState(prev => ({
        ...prev,
        selectedImage: file,
        imagePreview: preview,
        isUploading: false,
      }));

    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to process image',
        isUploading: false,
      }));
    }
  }, []);

  // Clear selected image
  const clearImage = useCallback(() => {
    setState(prev => ({
      ...prev,
      selectedImage: null,
      imagePreview: null,
      error: null,
    }));
  }, []);

  // Start polling for job status
  const startPolling = useCallback((jobId: string) => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
    }

    startTimeRef.current = Date.now();

    pollingRef.current = setInterval(async () => {
      try {
        const response = await fetch(`/api/ai-tryon/create?jobId=${jobId}`);
        const data: AiTryOnResponse = await response.json();

        if (data.success && data.data) {
          const { status, resultImageUrl, confidence } = data.data;
          
          setState(prev => ({
            ...prev,
            status,
            resultImageUrl: resultImageUrl || null,
            confidence: confidence || null,
          }));

          // Update progress based on status
          let progress = 0;
          if (status === 'PROCESSING') progress = 50;
          else if (status === 'COMPLETED') progress = 100;
          else if (status === 'FAILED') progress = 0;

          setState(prev => ({ ...prev, progress }));

          // Calculate estimated time remaining
          if (status === 'PROCESSING' && startTimeRef.current) {
            const elapsed = (Date.now() - startTimeRef.current) / 1000;
            const estimatedTotal = 60; // Assume 60 seconds total
            const remaining = Math.max(0, estimatedTotal - elapsed);
            setState(prev => ({ ...prev, estimatedTimeRemaining: remaining }));
          }

          // Stop polling if job is complete or failed
          if (status === 'COMPLETED' || status === 'FAILED') {
            if (pollingRef.current) {
              clearInterval(pollingRef.current);
              pollingRef.current = null;
            }
            
            setState(prev => ({
              ...prev,
              isProcessing: false,
              isLoading: false,
              estimatedTimeRemaining: null,
            }));

            if (status === 'FAILED') {
              setState(prev => ({
                ...prev,
                error: data.error || 'AI try-on generation failed',
              }));
            }
          }
        }

        // Stop polling after max time
        if (startTimeRef.current && Date.now() - startTimeRef.current > MAX_POLLING_TIME) {
          if (pollingRef.current) {
            clearInterval(pollingRef.current);
            pollingRef.current = null;
          }
          setState(prev => ({
            ...prev,
            isProcessing: false,
            isLoading: false,
            error: 'Try-on generation timed out. Please try again.',
          }));
        }

      } catch (error) {
        console.error('Error polling job status:', error);
      }
    }, POLLING_INTERVAL);
  }, []);

  // Generate AI try-on
  const generateTryOn = useCallback(async (
    customizationId: string,
    userId: string,
    contextScene?: string
  ) => {
    if (!state.selectedImage) {
      setState(prev => ({ ...prev, error: 'Please select an image first' }));
      return;
    }

    setState(prev => ({
      ...prev,
      isLoading: true,
      isProcessing: true,
      error: null,
      progress: 10,
      jobId: null,
      resultImageUrl: null,
      confidence: null,
    }));

    try {
      // Prepare form data
      const formData = new FormData();
      formData.append('userPhoto', state.selectedImage);
      formData.append('customizationId', customizationId);
      formData.append('userId', userId);
      if (contextScene) {
        formData.append('contextScene', contextScene);
      }

      // Store request for retry
      lastRequestRef.current = {
        userPhotoUrl: '', // Will be handled by FormData
        customizationId,
        userId,
        contextScene: contextScene as any,
      };

      setState(prev => ({ ...prev, progress: 20 }));

      // Send request
      const response = await fetch('/api/ai-tryon/create', {
        method: 'POST',
        body: formData,
      });

      const data: AiTryOnResponse = await response.json();

      if (data.success && data.data) {
        setState(prev => ({
          ...prev,
          jobId: data.data!.jobId,
          status: data.data!.status,
          progress: 30,
        }));

        // Start polling if job is processing
        if (data.data.status === 'PROCESSING' || data.data.status === 'PENDING') {
          startPolling(data.data.jobId);
        } else if (data.data.status === 'COMPLETED') {
          setState(prev => ({
            ...prev,
            resultImageUrl: data.data!.resultImageUrl || null,
            confidence: data.data!.confidence || null,
            isLoading: false,
            isProcessing: false,
            progress: 100,
          }));
        }
      } else {
        throw new Error(data.error || 'Failed to start AI try-on');
      }

    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to generate try-on',
        isLoading: false,
        isProcessing: false,
        progress: 0,
      }));
    }
  }, [state.selectedImage, startPolling]);

  // Check job status manually
  const checkJobStatus = useCallback(async (jobId: string) => {
    try {
      const response = await fetch(`/api/ai-tryon/create?jobId=${jobId}`);
      const data: AiTryOnResponse = await response.json();

      if (data.success && data.data) {
        setState(prev => ({
          ...prev,
          jobId,
          status: data.data!.status,
          resultImageUrl: data.data!.resultImageUrl || null,
          confidence: data.data!.confidence || null,
        }));
      }
    } catch (error) {
      console.error('Error checking job status:', error);
    }
  }, []);

  // Cancel current job
  const cancelJob = useCallback(() => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }

    setState(prev => ({
      ...prev,
      isLoading: false,
      isProcessing: false,
      progress: 0,
      estimatedTimeRemaining: null,
    }));
  }, []);

  // Retry last job
  const retryJob = useCallback(async () => {
    if (!lastRequestRef.current) {
      setState(prev => ({ ...prev, error: 'No previous request to retry' }));
      return;
    }

    const { customizationId, userId, contextScene } = lastRequestRef.current;
    await generateTryOn(customizationId, userId, contextScene);
  }, [generateTryOn]);

  // Reset all state
  const reset = useCallback(() => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }
    
    setState(INITIAL_STATE);
    lastRequestRef.current = null;
    startTimeRef.current = null;
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    ...state,
    selectImage,
    clearImage,
    generateTryOn,
    checkJobStatus,
    cancelJob,
    retryJob,
    reset,
    clearError,
  };
}
