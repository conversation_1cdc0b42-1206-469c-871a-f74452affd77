'use client';

import { useState, useCallback } from 'react';
import { DesignCanvas } from '@/types';

// Types for API responses
interface PreviewResponse {
  success: boolean;
  data?: {
    canvas: {
      width: number;
      height: number;
      elementCount: number;
    };
    area: {
      totalArea: number;
      printSizeCategory: string;
      categoryInfo: any;
      isWithinLimits: boolean;
      elementAreas: Array<{ id: string; area: number; type: string }>;
      overlappingArea: number;
      recommendations?: string[];
    };
    images: {
      preview: {
        buffer: string; // base64
        metadata: any;
      };
      thumbnail?: {
        buffer: string;
        metadata: any;
      };
      optimized?: {
        buffer: string;
        metadata: any;
      };
    };
    pricing?: {
      printSizeCategory: string;
      categoryLabel: string;
      categoryDescription: string;
      impactMessage: string;
    };
    timestamp: string;
  };
  error?: string;
  message?: string;
}

interface SaveResponse {
  success: boolean;
  data?: {
    customization: {
      id: string;
      name: string;
      description?: string;
      status: string;
      previewImage?: string;
      createdAt: string;
      updatedAt: string;
    };
    canvas: {
      width: number;
      height: number;
      elementCount: number;
      placement: string;
    };
    area: {
      totalArea: number;
      printSizeCategory: string;
      isWithinLimits: boolean;
      elementAreas: Array<{ id: string; area: number }>;
    };
    pricing?: any;
    user: any;
    product: any;
    template?: any;
  };
  error?: string;
  message?: string;
}

interface CustomizationAPIState {
  isGeneratingPreview: boolean;
  isSaving: boolean;
  previewData: PreviewResponse['data'] | null;
  saveData: SaveResponse['data'] | null;
  error: string | null;
  lastPreviewUrl: string | null;
}

export const useCustomizationAPI = () => {
  const [state, setState] = useState<CustomizationAPIState>({
    isGeneratingPreview: false,
    isSaving: false,
    previewData: null,
    saveData: null,
    error: null,
    lastPreviewUrl: null,
  });

  // Generate preview
  const generatePreview = useCallback(async (
    canvas: DesignCanvas,
    options: {
      format?: 'png' | 'jpeg' | 'webp';
      quality?: number;
      includeVariants?: boolean;
      includePricing?: boolean;
    } = {}
  ): Promise<PreviewResponse['data'] | null> => {
    setState(prev => ({ 
      ...prev, 
      isGeneratingPreview: true, 
      error: null 
    }));

    try {
      const response = await fetch('/api/customizations/preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          canvas,
          options: {
            format: 'png',
            quality: 85,
            includeVariants: false,
            includePricing: true,
            ...options,
          },
        }),
      });

      const result: PreviewResponse = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to generate preview');
      }

      // Create preview URL from base64 data
      const previewUrl = result.data?.images.preview.buffer 
        ? `data:image/png;base64,${result.data.images.preview.buffer}`
        : null;

      setState(prev => ({
        ...prev,
        isGeneratingPreview: false,
        previewData: result.data || null,
        lastPreviewUrl: previewUrl,
        error: null,
      }));

      return result.data || null;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      setState(prev => ({
        ...prev,
        isGeneratingPreview: false,
        error: errorMessage,
      }));

      return null;
    }
  }, []);

  // Save customization
  const saveCustomization = useCallback(async (
    canvas: DesignCanvas,
    saveData: {
      name: string;
      description?: string;
      productId: string;
      templateId?: string;
      userId: string;
      placement?: 'front' | 'back' | 'left' | 'right';
      status?: 'DRAFT' | 'PUBLISHED';
      generatePreview?: boolean;
    }
  ): Promise<SaveResponse['data'] | null> => {
    setState(prev => ({ 
      ...prev, 
      isSaving: true, 
      error: null 
    }));

    try {
      const response = await fetch('/api/customizations/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          canvas,
          ...saveData,
          generatePreview: saveData.generatePreview ?? true,
        }),
      });

      const result: SaveResponse = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to save customization');
      }

      setState(prev => ({
        ...prev,
        isSaving: false,
        saveData: result.data || null,
        error: null,
      }));

      return result.data || null;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      setState(prev => ({
        ...prev,
        isSaving: false,
        error: errorMessage,
      }));

      return null;
    }
  }, []);

  // Get saved customizations
  const getCustomizations = useCallback(async (
    userId: string,
    options: {
      productId?: string;
      status?: string;
      limit?: number;
      offset?: number;
    } = {}
  ) => {
    try {
      const params = new URLSearchParams({
        userId,
        ...options,
        limit: String(options.limit || 10),
        offset: String(options.offset || 0),
      });

      const response = await fetch(`/api/customizations/save?${params}`);
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch customizations');
      }

      return result.data;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setState(prev => ({ ...prev, error: errorMessage }));
      return null;
    }
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Clear preview data
  const clearPreview = useCallback(() => {
    setState(prev => ({ 
      ...prev, 
      previewData: null, 
      lastPreviewUrl: null 
    }));
  }, []);

  // Clear save data
  const clearSaveData = useCallback(() => {
    setState(prev => ({ ...prev, saveData: null }));
  }, []);

  return {
    // State
    isGeneratingPreview: state.isGeneratingPreview,
    isSaving: state.isSaving,
    previewData: state.previewData,
    saveData: state.saveData,
    error: state.error,
    lastPreviewUrl: state.lastPreviewUrl,
    
    // Actions
    generatePreview,
    saveCustomization,
    getCustomizations,
    clearError,
    clearPreview,
    clearSaveData,
    
    // Computed values
    hasPreview: !!state.previewData,
    hasSaved: !!state.saveData,
    isLoading: state.isGeneratingPreview || state.isSaving,
  };
};

// Hook for preview configuration
export const usePreviewConfig = () => {
  const [config, setConfig] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const fetchConfig = useCallback(async (configType?: string) => {
    setIsLoading(true);
    
    try {
      const params = configType ? `?config=${configType}` : '';
      const response = await fetch(`/api/customizations/preview${params}`);
      const result = await response.json();

      if (result.success) {
        setConfig(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch preview config:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    config,
    isLoading,
    fetchConfig,
  };
};
