'use client';

import { useState, useCallback } from 'react';
import { PricingRequest, PricingResponse, PricingRule } from '@/types/pricing';

// Custom hook for pricing calculations
export function usePricing() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const calculatePrice = useCallback(async (request: PricingRequest): Promise<PricingResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/pricing/compute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      const result: PricingResponse = await response.json();

      if (!result.success) {
        setError(result.error || 'Pricing calculation failed');
        return null;
      }

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Network error';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const previewPricing = useCallback(async (scenarios: Array<{ name: string; request: PricingRequest }>) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/pricing/preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ scenarios }),
      });

      const result = await response.json();

      if (!result.success) {
        setError(result.error || 'Preview failed');
        return null;
      }

      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Network error';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    calculatePrice,
    previewPricing,
    loading,
    error,
  };
}

// Custom hook for pricing rules management
export function usePricingRules() {
  const [rules, setRules] = useState<PricingRule[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchRules = useCallback(async (filters?: { active?: boolean; type?: string }) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      if (filters?.active !== undefined) params.append('active', filters.active.toString());
      if (filters?.type) params.append('type', filters.type);

      const response = await fetch(`/api/pricing/rules?${params}`);
      const result = await response.json();

      if (result.success) {
        setRules(result.data);
      } else {
        setError(result.error || 'Failed to fetch rules');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Network error';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const createRule = useCallback(async (ruleData: Partial<PricingRule>) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/pricing/rules', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(ruleData),
      });

      const result = await response.json();

      if (result.success) {
        setRules(prev => [...prev, result.data]);
        return result.data;
      } else {
        setError(result.error || 'Failed to create rule');
        return null;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Network error';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateRule = useCallback(async (id: string, updates: Partial<PricingRule>) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/pricing/rules', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id, ...updates }),
      });

      const result = await response.json();

      if (result.success) {
        setRules(prev => prev.map(rule => rule.id === id ? result.data : rule));
        return result.data;
      } else {
        setError(result.error || 'Failed to update rule');
        return null;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Network error';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteRule = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/pricing/rules?id=${id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        setRules(prev => prev.filter(rule => rule.id !== id));
        return true;
      } else {
        setError(result.error || 'Failed to delete rule');
        return false;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Network error';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    rules,
    loading,
    error,
    fetchRules,
    createRule,
    updateRule,
    deleteRule,
  };
}

// Custom hook for pricing configuration
export function usePricingConfig() {
  const [config, setConfig] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchConfig = useCallback(async (configType?: string) => {
    setLoading(true);
    setError(null);

    try {
      const params = configType ? `?config=${configType}` : '';
      const response = await fetch(`/api/pricing/compute${params}`);
      const result = await response.json();

      if (result.success) {
        setConfig(result.data);
      } else {
        setError(result.error || 'Failed to fetch config');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Network error';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    config,
    loading,
    error,
    fetchConfig,
  };
}
