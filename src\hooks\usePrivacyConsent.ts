'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';

interface ConsentStatus {
  aiTryOnConsent: boolean;
  aiTryOnConsentDate?: string;
  privacyPolicyAccepted: boolean;
  privacyPolicyDate?: string;
  dataRetentionDays: number;
  dailyTryOnCount: number;
  dailyLimit: number;
  totalTryOnCount: number;
  canTryOn: boolean;
}

interface UsePrivacyConsentReturn {
  consentStatus: ConsentStatus | null;
  loading: boolean;
  error: string | null;
  showConsentModal: boolean;
  setShowConsentModal: (show: boolean) => void;
  giveConsent: () => Promise<boolean>;
  withdrawConsent: (deleteData?: boolean) => Promise<boolean>;
  updateRetention: (days: number) => Promise<boolean>;
  checkTryOnLimit: () => Promise<boolean>;
  refreshStatus: () => Promise<void>;
}

export const usePrivacyConsent = (): UsePrivacyConsentReturn => {
  const { data: session, status } = useSession();
  const [consentStatus, setConsentStatus] = useState<ConsentStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showConsentModal, setShowConsentModal] = useState(false);

  // Fetch current consent status
  const fetchConsentStatus = useCallback(async () => {
    if (status !== 'authenticated' || !session?.user?.id) {
      setLoading(false);
      return;
    }

    try {
      setError(null);
      const response = await fetch('/api/privacy/consent');
      
      if (!response.ok) {
        throw new Error('Failed to fetch consent status');
      }

      const data = await response.json();
      if (data.success) {
        setConsentStatus(data.data);
      } else {
        throw new Error(data.error || 'Unknown error');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      console.error('Error fetching consent status:', err);
    } finally {
      setLoading(false);
    }
  }, [session?.user?.id, status]);

  // Initial load
  useEffect(() => {
    fetchConsentStatus();
  }, [fetchConsentStatus]);

  // Give consent for AI try-on
  const giveConsent = useCallback(async (): Promise<boolean> => {
    try {
      setError(null);
      const response = await fetch('/api/privacy/consent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          aiTryOnConsent: true,
          privacyPolicyAccepted: true,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        await fetchConsentStatus();
        return true;
      } else {
        throw new Error(data.error || 'Failed to give consent');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      console.error('Error giving consent:', err);
      return false;
    }
  }, [fetchConsentStatus]);

  // Withdraw consent
  const withdrawConsent = useCallback(async (deleteData = false): Promise<boolean> => {
    try {
      setError(null);
      const response = await fetch('/api/privacy/consent', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          reason: 'User withdrew consent',
          deleteExistingData: deleteData,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        await fetchConsentStatus();
        return true;
      } else {
        throw new Error(data.error || 'Failed to withdraw consent');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      console.error('Error withdrawing consent:', err);
      return false;
    }
  }, [fetchConsentStatus]);

  // Update data retention preference
  const updateRetention = useCallback(async (days: number): Promise<boolean> => {
    try {
      setError(null);
      const response = await fetch('/api/privacy/consent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          aiTryOnConsent: consentStatus?.aiTryOnConsent,
          dataRetentionDays: days,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        await fetchConsentStatus();
        return true;
      } else {
        throw new Error(data.error || 'Failed to update retention');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      console.error('Error updating retention:', err);
      return false;
    }
  }, [consentStatus?.aiTryOnConsent, fetchConsentStatus]);

  // Check and increment try-on limit
  const checkTryOnLimit = useCallback(async (): Promise<boolean> => {
    try {
      setError(null);
      const response = await fetch('/api/privacy/consent', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
      });

      const data = await response.json();
      
      if (data.success) {
        // Update local state with new counts
        setConsentStatus(prev => prev ? {
          ...prev,
          dailyTryOnCount: data.data.dailyCount,
          totalTryOnCount: data.data.totalCount,
          canTryOn: data.data.remainingToday > 0,
        } : null);
        return true;
      } else {
        if (data.error === 'Daily try-on limit reached') {
          // Update local state to reflect limit reached
          setConsentStatus(prev => prev ? {
            ...prev,
            canTryOn: false,
          } : null);
        }
        throw new Error(data.error || 'Failed to check limit');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      console.error('Error checking try-on limit:', err);
      return false;
    }
  }, []);

  // Refresh consent status
  const refreshStatus = useCallback(async () => {
    setLoading(true);
    await fetchConsentStatus();
  }, [fetchConsentStatus]);

  // Auto-show consent modal if needed
  useEffect(() => {
    if (consentStatus && !consentStatus.aiTryOnConsent && !showConsentModal) {
      // Don't auto-show modal, let components decide when to show it
    }
  }, [consentStatus, showConsentModal]);

  return {
    consentStatus,
    loading,
    error,
    showConsentModal,
    setShowConsentModal,
    giveConsent,
    withdrawConsent,
    updateRetention,
    checkTryOnLimit,
    refreshStatus,
  };
};

// Helper hook for checking if user can perform AI try-on
export const useCanTryOn = () => {
  const { consentStatus, loading, checkTryOnLimit } = usePrivacyConsent();

  const canTryOn = !loading && consentStatus?.aiTryOnConsent && consentStatus?.canTryOn;
  const needsConsent = !loading && !consentStatus?.aiTryOnConsent;
  const limitReached = !loading && consentStatus?.aiTryOnConsent && !consentStatus?.canTryOn;

  return {
    canTryOn,
    needsConsent,
    limitReached,
    dailyCount: consentStatus?.dailyTryOnCount || 0,
    dailyLimit: consentStatus?.dailyLimit || 2,
    checkTryOnLimit,
  };
};
