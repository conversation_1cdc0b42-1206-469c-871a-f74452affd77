
// Animation utilities for Product Selection Flow
// Focus on emotional engagement and smooth micro-interactions

import { Variants } from 'framer-motion';

// Emotional easing functions
export const easings = {
  gentle: [0.25, 0.46, 0.45, 0.94],
  confident: [0.4, 0, 0.2, 1],
  playful: [0.68, -0.55, 0.265, 1.55],
  sophisticated: [0.25, 0.1, 0.25, 1],
  energetic: [0.87, 0, 0.13, 1]
};

// Stagger animations for grid layouts
export const staggerContainer: Variants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

export const staggerItem: Variants = {
  hidden: { 
    opacity: 0, 
    y: 50,
    scale: 0.9
  },
  show: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: easings.gentle
    }
  }
};

// Card hover animations
export const cardHover: Variants = {
  rest: { 
    scale: 1,
    y: 0,
    boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
  },
  hover: { 
    scale: 1.03,
    y: -8,
    boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
    transition: {
      duration: 0.3,
      ease: easings.confident
    }
  },
  tap: {
    scale: 0.98,
    transition: {
      duration: 0.1
    }
  }
};

// Parallax scroll effect
export const parallaxImage: Variants = {
  rest: { scale: 1 },
  hover: { 
    scale: 1.1,
    transition: {
      duration: 0.7,
      ease: easings.sophisticated
    }
  }
};

// Shimmer effect for loading states
export const shimmer: Variants = {
  initial: { x: '-100%' },
  animate: {
    x: '100%',
    transition: {
      duration: 1.5,
      ease: 'easeInOut',
      repeat: Infinity,
      repeatDelay: 0.5
    }
  }
};

// Emotional celebration animations
export const celebrate: Variants = {
  initial: { scale: 1, rotate: 0 },
  animate: {
    scale: [1, 1.2, 1],
    rotate: [0, 5, -5, 0],
    transition: {
      duration: 0.8,
      ease: easings.playful
    }
  }
};

// Price update animations
export const priceUpdate: Variants = {
  initial: { scale: 1, color: '#000000' },
  updating: {
    scale: [1, 1.1, 1],
    transition: {
      duration: 0.3,
      ease: easings.energetic
    }
  },
  celebrating: {
    scale: [1, 1.15, 1],
    color: ['#000000', '#f59e0b', '#000000'],
    transition: {
      duration: 0.6,
      ease: easings.playful
    }
  }
};

// Step transition animations
export const stepTransition: Variants = {
  enter: (direction: number) => ({
    x: direction > 0 ? 1000 : -1000,
    opacity: 0,
    scale: 0.95
  }),
  center: {
    zIndex: 1,
    x: 0,
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: easings.confident
    }
  },
  exit: (direction: number) => ({
    zIndex: 0,
    x: direction < 0 ? 1000 : -1000,
    opacity: 0,
    scale: 0.95,
    transition: {
      duration: 0.3,
      ease: easings.sophisticated
    }
  })
};

// Floating animation for decorative elements
export const float: Variants = {
  animate: {
    y: [-10, 10, -10],
    transition: {
      duration: 6,
      ease: 'easeInOut',
      repeat: Infinity
    }
  }
};

// Glow effect for selected items
export const glow: Variants = {
  initial: { 
    boxShadow: '0 0 0px rgba(245, 149, 50, 0)' 
  },
  animate: {
    boxShadow: [
      '0 0 0px rgba(245, 149, 50, 0)',
      '0 0 20px rgba(245, 149, 50, 0.4)',
      '0 0 30px rgba(245, 149, 50, 0.6)',
      '0 0 20px rgba(245, 149, 50, 0.4)',
      '0 0 0px rgba(245, 149, 50, 0)'
    ],
    transition: {
      duration: 2,
      ease: 'easeInOut',
      repeat: Infinity
    }
  }
};

// Micro-interactions for buttons
export const buttonPress: Variants = {
  rest: { scale: 1 },
  hover: { 
    scale: 1.05,
    transition: {
      duration: 0.2,
      ease: easings.confident
    }
  },
  tap: { 
    scale: 0.95,
    transition: {
      duration: 0.1
    }
  }
};

// Emotional reveal animation
export const emotionalReveal: Variants = {
  hidden: {
    opacity: 0,
    y: 20,
    scale: 0.9
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: easings.gentle,
      staggerChildren: 0.1
    }
  }
};

// Progress bar animation
export const progressBar: Variants = {
  initial: { width: 0 },
  animate: (progress: number) => ({
    width: `${progress}%`,
    transition: {
      duration: 0.8,
      ease: easings.confident
    }
  })
};

// Sticky bar entrance
export const stickyBarEntrance: Variants = {
  hidden: {
    y: 100,
    opacity: 0
  },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 30,
      duration: 0.5
    }
  },
  exit: {
    y: 100,
    opacity: 0,
    transition: {
      duration: 0.3,
      ease: easings.sophisticated
    }
  }
};

// Color picker animation
export const colorPicker: Variants = {
  rest: { 
    scale: 1,
    rotate: 0
  },
  hover: { 
    scale: 1.1,
    rotate: 5,
    transition: {
      duration: 0.3,
      ease: easings.playful
    }
  },
  selected: {
    scale: 1.2,
    rotate: [0, 10, -10, 0],
    transition: {
      duration: 0.5,
      ease: easings.celebrate
    }
  }
};

// Fabric texture animation
export const fabricTexture: Variants = {
  rest: { scale: 1 },
  hover: {
    scale: [1, 1.05, 1],
    transition: {
      duration: 2,
      ease: 'easeInOut',
      repeat: Infinity
    }
  }
};

// Size visualization animation
export const sizeVisualization: Variants = {
  initial: { scale: 0, opacity: 0 },
  animate: {
    scale: 1,
    opacity: 1,
    transition: {
      type: 'spring',
      stiffness: 200,
      damping: 15,
      delay: 0.2
    }
  }
};

// Confidence rating animation
export const confidenceRating: Variants = {
  rest: { scale: 1 },
  hover: { 
    scale: 1.2,
    transition: {
      duration: 0.2,
      ease: easings.playful
    }
  },
  selected: {
    scale: [1, 1.3, 1],
    transition: {
      duration: 0.4,
      ease: easings.celebrate
    }
  }
};

// Emotional message animation
export const emotionalMessage: Variants = {
  hidden: {
    opacity: 0,
    y: 20,
    scale: 0.95
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: easings.gentle
    }
  },
  exit: {
    opacity: 0,
    y: -20,
    scale: 0.95,
    transition: {
      duration: 0.3,
      ease: easings.sophisticated
    }
  }
};

// Utility function to create custom spring animations
export const createSpringAnimation = (
  stiffness: number = 300,
  damping: number = 30,
  mass: number = 1
) => ({
  type: 'spring' as const,
  stiffness,
  damping,
  mass
});

// Utility function for sequential animations
export const createSequentialAnimation = (
  keyframes: any[],
  duration: number = 1,
  ease: string = 'easeInOut'
) => ({
  keyframes,
  transition: {
    duration,
    ease,
    times: keyframes.map((_, i) => i / (keyframes.length - 1))
  }
});

