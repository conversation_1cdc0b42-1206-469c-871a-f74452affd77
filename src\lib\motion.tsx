'use client';

import { motion } from 'framer-motion';
import { forwardRef } from 'react';

// Create motion components that properly handle prop conflicts
export const MotionDiv = motion.div;
export const MotionSection = motion.section;
export const MotionButton = motion.button;
export const MotionInput = motion.input;
export const MotionTextarea = motion.textarea;

// Alternative approach: Create wrapper components
export const AnimatedDiv = forwardRef<
  HTMLDivElement,
  React.ComponentProps<typeof motion.div>
>((props, ref) => <motion.div ref={ref} {...props} />);

AnimatedDiv.displayName = 'AnimatedDiv';

export const AnimatedSection = forwardRef<
  HTMLElement,
  React.ComponentProps<typeof motion.section>
>((props, ref) => <motion.section ref={ref} {...props} />);

AnimatedSection.displayName = 'AnimatedSection';

export const AnimatedButton = forwardRef<
  HTMLButtonElement,
  React.ComponentProps<typeof motion.button>
>((props, ref) => <motion.button ref={ref} {...props} />);

AnimatedButton.displayName = 'AnimatedButton';

export const AnimatedInput = forwardRef<
  HTMLInputElement,
  React.ComponentProps<typeof motion.input>
>((props, ref) => <motion.input ref={ref} {...props} />);

AnimatedInput.displayName = 'AnimatedInput';

export const AnimatedTextarea = forwardRef<
  HTMLTextAreaElement,
  React.ComponentProps<typeof motion.textarea>
>((props, ref) => <motion.textarea ref={ref} {...props} />);

AnimatedTextarea.displayName = 'AnimatedTextarea';
