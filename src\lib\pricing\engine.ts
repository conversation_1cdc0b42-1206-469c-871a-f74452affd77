import { PrismaClient } from '@prisma/client';
import {
  PricingRequest,
  PricingResponse,
  PricingBreakdown,
  PricingComponent,
  PricingRule,
  PrintSize,
  QualityTier,
  PrintSizeConfig,
  QualityTierConfig,
} from '@/types/pricing';
import { generateId } from '@/lib/utils';

const prisma = new PrismaClient();

// Print size configurations with emotional messaging
const PRINT_SIZE_CONFIG: Record<PrintSize, PrintSizeConfig> = {
  small: {
    size: 'small',
    name: 'Subtle Statement',
    description: 'Perfect for minimalist elegance',
    maxDimensions: { width: 4, height: 4 },
    baseMultiplier: 1.0,
    valueMessage: 'Refined simplicity that speaks volumes'
  },
  medium: {
    size: 'medium',
    name: 'Balanced Expression',
    description: 'The sweet spot of visibility and style',
    maxDimensions: { width: 8, height: 8 },
    baseMultiplier: 1.3,
    valueMessage: 'Perfect balance of impact and sophistication'
  },
  large: {
    size: 'large',
    name: 'Bold Statement',
    description: 'Make your presence known',
    maxDimensions: { width: 12, height: 12 },
    baseMultiplier: 1.7,
    valueMessage: 'Commanding attention with confident style'
  },
  full_coverage: {
    size: 'full_coverage',
    name: 'Total Expression',
    description: 'Your canvas, your masterpiece',
    maxDimensions: { width: 16, height: 20 },
    baseMultiplier: 2.2,
    valueMessage: 'Complete creative freedom for maximum impact'
  }
};

// Quality tier configurations with emotional promises
const QUALITY_TIER_CONFIG: Record<QualityTier, QualityTierConfig> = {
  standard: {
    tier: 'standard',
    name: 'Everyday Excellence',
    description: 'Quality you can count on, every day',
    features: ['Durable printing', 'Comfortable fit', 'Easy care'],
    priceMultiplier: 1.0,
    valueMessage: 'Reliable quality that fits your lifestyle',
    qualityPromise: 'Built to last through countless wears and washes'
  },
  premium: {
    tier: 'premium',
    name: 'Elevated Experience',
    description: 'Where comfort meets luxury',
    features: ['Premium fabrics', 'Enhanced durability', 'Superior finish', 'Color protection'],
    priceMultiplier: 1.4,
    valueMessage: 'Indulge in the finer details that make all the difference',
    qualityPromise: 'Crafted with premium materials for an elevated wearing experience'
  },
  luxury: {
    tier: 'luxury',
    name: 'Uncompromising Excellence',
    description: 'The pinnacle of fashion craftsmanship',
    features: ['Luxury fabrics', 'Hand-finished details', 'Lifetime quality', 'Exclusive techniques'],
    priceMultiplier: 2.0,
    valueMessage: 'Investment in timeless quality and unmatched sophistication',
    qualityPromise: 'Heirloom-quality craftsmanship that defines true luxury'
  }
};

export class PricingEngine {
  private static instance: PricingEngine;
  
  public static getInstance(): PricingEngine {
    if (!PricingEngine.instance) {
      PricingEngine.instance = new PricingEngine();
    }
    return PricingEngine.instance;
  }

  async calculatePrice(request: PricingRequest): Promise<PricingResponse> {
    try {
      // Validate request
      const validation = this.validateRequest(request);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error
        };
      }

      // Get product and variant data
      const productData = await this.getProductData(request);
      if (!productData) {
        return {
          success: false,
          error: 'Product not found or unavailable'
        };
      }

      // Calculate pricing breakdown
      const breakdown = await this.computePricingBreakdown(request, productData);
      
      return {
        success: true,
        data: breakdown
      };
      
    } catch (error) {
      console.error('Pricing calculation error:', error);
      return {
        success: false,
        error: 'Unable to calculate pricing at this time'
      };
    }
  }

  private validateRequest(request: PricingRequest): { isValid: boolean; error?: string } {
    if (!request.productId) {
      return { isValid: false, error: 'Product ID is required' };
    }
    
    if (!request.quantity || request.quantity < 1) {
      return { isValid: false, error: 'Valid quantity is required' };
    }
    
    if (request.quantity > 100) {
      return { isValid: false, error: 'Quantity exceeds maximum limit' };
    }
    
    return { isValid: true };
  }

  private async getProductData(request: PricingRequest) {
    const product = await prisma.product.findUnique({
      where: { id: request.productId },
      include: {
        variants: {
          include: {
            fabric: true,
            color: true,
            size: true
          }
        }
      }
    });

    return product;
  }

  private async computePricingBreakdown(
    request: PricingRequest, 
    productData: any
  ): Promise<PricingBreakdown> {
    const components: PricingComponent[] = [];
    let totalPrice = 0;

    // Base price component
    const basePrice = Number(productData.basePrice);
    const baseComponent: PricingComponent = {
      id: 'base_price',
      name: 'Base Price',
      description: `${productData.name} - Foundation of quality`,
      amount: basePrice * request.quantity,
      type: 'base',
      valueMessage: 'Premium design and craftsmanship included',
      icon: '✨'
    };
    components.push(baseComponent);
    totalPrice += baseComponent.amount;

    // Apply pricing rules
    const rules = await this.getApplicableRules(request, productData);
    for (const rule of rules) {
      const component = this.applyRule(rule, request, basePrice);
      if (component) {
        components.push(component);
        totalPrice += component.amount;
      }
    }

    // Generate emotional messaging
    const messaging = this.generateEmotionalMessaging(request, components, totalPrice);

    const priceId = generateId();
    const now = new Date();
    const validUntil = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24 hours

    return {
      basePrice,
      totalPrice: Math.max(0, totalPrice), // Ensure non-negative
      currency: 'USD',
      breakdown: components,
      valueMessage: messaging.valueMessage,
      qualityPromise: messaging.qualityPromise,
      savingsMessage: messaging.savingsMessage,
      priceJustification: messaging.priceJustification,
      calculatedAt: now.toISOString(),
      validUntil: validUntil.toISOString(),
      priceId
    };
  }

  private async getApplicableRules(request: PricingRequest, productData: any): Promise<PricingRule[]> {
    // This would typically query the database for active pricing rules
    // For now, we'll return some default rules based on the request
    const rules: PricingRule[] = [];

    // Fabric surcharge rule
    if (request.fabricId) {
      const fabric = productData.variants?.find((v: any) => v.fabricId === request.fabricId)?.fabric;
      if (fabric && fabric.priceModifier > 0) {
        rules.push({
          id: 'fabric_surcharge',
          name: 'Premium Fabric',
          description: `${fabric.name} upgrade`,
          type: 'fabric_surcharge',
          conditions: [],
          modifier: Number(fabric.priceModifier),
          modifierType: 'fixed',
          customerMessage: `Upgraded to ${fabric.name}`,
          valueFraming: fabric.feelTags?.join(', ') || 'Premium quality',
          priority: 1,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: 'system'
        });
      }
    }

    return rules.sort((a, b) => a.priority - b.priority);
  }

  private applyRule(rule: PricingRule, request: PricingRequest, basePrice: number): PricingComponent | null {
    let amount = 0;
    
    if (rule.modifierType === 'fixed') {
      amount = rule.modifier * request.quantity;
    } else if (rule.modifierType === 'percentage') {
      amount = (basePrice * rule.modifier / 100) * request.quantity;
    }

    if (amount === 0) return null;

    return {
      id: rule.id,
      name: rule.name,
      description: rule.description,
      amount,
      type: amount > 0 ? 'surcharge' : 'discount',
      valueMessage: rule.valueFraming,
      icon: this.getRuleIcon(rule.type),
      ruleId: rule.id
    };
  }

  private getRuleIcon(ruleType: string): string {
    const icons: Record<string, string> = {
      fabric_surcharge: '🧵',
      print_size_cost: '📏',
      quality_tier: '⭐',
      quantity_discount: '💰',
      seasonal_promotion: '🎉',
      loyalty_discount: '💎'
    };
    return icons[ruleType] || '✨';
  }

  private generateEmotionalMessaging(
    request: PricingRequest,
    components: PricingComponent[],
    totalPrice: number
  ) {
    const qualityTier = request.qualityTier || 'standard';
    const printSize = request.printSize || 'medium';
    
    const qualityConfig = QUALITY_TIER_CONFIG[qualityTier];
    const printConfig = PRINT_SIZE_CONFIG[printSize];

    // Generate value message based on components
    let valueMessage = 'Exceptional value for your unique style';
    if (components.some(c => c.type === 'surcharge')) {
      valueMessage = 'Premium upgrades for an elevated experience';
    }
    if (components.some(c => c.type === 'discount')) {
      valueMessage = 'Great savings on quality you can trust';
    }

    // Generate price justification
    const priceJustification = [
      'Ethically sourced materials',
      'Expert craftsmanship',
      'Quality guaranteed',
      'Sustainable production'
    ];

    // Add specific justifications based on components
    components.forEach(component => {
      if (component.type === 'surcharge' && component.valueMessage) {
        priceJustification.push(component.valueMessage);
      }
    });

    // Generate savings message if applicable
    let savingsMessage: string | undefined;
    const discountComponents = components.filter(c => c.type === 'discount');
    if (discountComponents.length > 0) {
      const totalSavings = discountComponents.reduce((sum, c) => sum + Math.abs(c.amount), 0);
      savingsMessage = `You're saving $${totalSavings.toFixed(2)} with current offers!`;
    }

    return {
      valueMessage,
      qualityPromise: qualityConfig.qualityPromise,
      savingsMessage,
      priceJustification
    };
  }

  // Public utility methods for admin interface
  async previewPricing(requests: PricingRequest[]): Promise<PricingResponse[]> {
    const results = [];
    for (const request of requests) {
      const result = await this.calculatePrice(request);
      results.push(result);
    }
    return results;
  }

  getPrintSizeConfig(): Record<PrintSize, PrintSizeConfig> {
    return PRINT_SIZE_CONFIG;
  }

  getQualityTierConfig(): Record<QualityTier, QualityTierConfig> {
    return QUALITY_TIER_CONFIG;
  }
}
