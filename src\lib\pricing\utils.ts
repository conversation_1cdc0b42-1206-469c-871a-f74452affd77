import { PricingRule, PricingCondition, ValueMessage } from '@/types/pricing';

// Value messaging templates for emotional transparency
export const VALUE_MESSAGES: Record<string, ValueMessage[]> = {
  fabric: [
    {
      context: 'fabric',
      template: 'Upgraded to {fabricName} for {feelDescription}',
      variables: ['fabricName', 'feelDescription']
    },
    {
      context: 'fabric',
      template: 'Premium {fabricName} - {valueProposition}',
      variables: ['fabricName', 'valueProposition']
    }
  ],
  quality: [
    {
      context: 'quality',
      template: '{tierName} quality means {qualityBenefit}',
      variables: ['tierName', 'qualityBenefit']
    },
    {
      context: 'quality',
      template: 'Investing in {tierName} for {longTermValue}',
      variables: ['tierName', 'longTermValue']
    }
  ],
  print: [
    {
      context: 'print',
      template: '{printSize} design for {impactDescription}',
      variables: ['printSize', 'impactDescription']
    }
  ],
  discount: [
    {
      context: 'discount',
      template: 'Special savings: {discountReason}',
      variables: ['discountReason']
    },
    {
      context: 'discount',
      template: 'You\'re saving with {promotionName}',
      variables: ['promotionName']
    }
  ]
};

// Pricing validation utilities
export class PricingValidator {
  static validateRule(rule: Partial<PricingRule>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!rule.name?.trim()) {
      errors.push('Rule name is required');
    }

    if (!rule.type) {
      errors.push('Rule type is required');
    }

    if (rule.modifier === undefined || rule.modifier === null) {
      errors.push('Modifier value is required');
    }

    if (!rule.modifierType) {
      errors.push('Modifier type is required');
    }

    if (rule.modifierType === 'percentage' && Math.abs(rule.modifier!) > 100) {
      errors.push('Percentage modifier cannot exceed 100%');
    }

    if (!rule.customerMessage?.trim()) {
      errors.push('Customer message is required');
    }

    if (!rule.valueFraming?.trim()) {
      errors.push('Value framing message is required');
    }

    if (!rule.conditions || rule.conditions.length === 0) {
      errors.push('At least one condition is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static validateCondition(condition: PricingCondition): { isValid: boolean; error?: string } {
    if (!condition.field?.trim()) {
      return { isValid: false, error: 'Condition field is required' };
    }

    if (!condition.operator) {
      return { isValid: false, error: 'Condition operator is required' };
    }

    if (condition.value === undefined || condition.value === null) {
      return { isValid: false, error: 'Condition value is required' };
    }

    // Validate operator-specific requirements
    if (['in', 'contains'].includes(condition.operator) && !Array.isArray(condition.value)) {
      return { isValid: false, error: `Operator '${condition.operator}' requires an array value` };
    }

    if (['greater_than', 'less_than'].includes(condition.operator) && typeof condition.value !== 'number') {
      return { isValid: false, error: `Operator '${condition.operator}' requires a numeric value` };
    }

    return { isValid: true };
  }
}

// Pricing formatting utilities
export class PricingFormatter {
  static formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }

  static formatPercentage(value: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: 0,
      maximumFractionDigits: 1
    }).format(value / 100);
  }

  static formatPriceRange(min: number, max: number, currency: string = 'USD'): string {
    if (min === max) {
      return this.formatCurrency(min, currency);
    }
    return `${this.formatCurrency(min, currency)} - ${this.formatCurrency(max, currency)}`;
  }

  static generateValueMessage(template: string, variables: Record<string, string>): string {
    let message = template;
    Object.entries(variables).forEach(([key, value]) => {
      message = message.replace(new RegExp(`{${key}}`, 'g'), value);
    });
    return message;
  }
}

// Pricing analytics utilities
export class PricingAnalytics {
  static calculatePriceElasticity(
    oldPrice: number,
    newPrice: number,
    oldQuantity: number,
    newQuantity: number
  ): number {
    const priceChange = (newPrice - oldPrice) / oldPrice;
    const quantityChange = (newQuantity - oldQuantity) / oldQuantity;
    
    if (priceChange === 0) return 0;
    return quantityChange / priceChange;
  }

  static calculateConversionImpact(
    baseConversionRate: number,
    priceIncrease: number
  ): number {
    // Simple model: 1% price increase = 0.5% conversion decrease
    const elasticity = -0.5;
    const priceChangePercent = priceIncrease * 100;
    const conversionChange = priceChangePercent * elasticity;
    
    return Math.max(0, baseConversionRate + (conversionChange / 100));
  }

  static suggestOptimalPrice(
    costs: number,
    targetMargin: number,
    competitorPrices: number[],
    demandElasticity: number = -1.5
  ): number {
    // Cost-plus pricing
    const costPlusPrice = costs / (1 - targetMargin);
    
    // Competitive pricing
    const avgCompetitorPrice = competitorPrices.reduce((sum, price) => sum + price, 0) / competitorPrices.length;
    
    // Demand-based adjustment
    const demandAdjustment = Math.abs(demandElasticity) > 1 ? 0.95 : 1.05; // Elastic vs inelastic
    
    // Weighted average of pricing strategies
    const optimalPrice = (costPlusPrice * 0.4 + avgCompetitorPrice * 0.4 + (avgCompetitorPrice * demandAdjustment) * 0.2);
    
    return Math.max(costPlusPrice, optimalPrice); // Never go below cost-plus
  }
}

// Rule condition helpers
export const CONDITION_OPERATORS = [
  { value: 'equals', label: 'Equals', description: 'Exact match' },
  { value: 'greater_than', label: 'Greater than', description: 'Numeric comparison' },
  { value: 'less_than', label: 'Less than', description: 'Numeric comparison' },
  { value: 'in', label: 'In list', description: 'Value is in array' },
  { value: 'contains', label: 'Contains', description: 'Array contains value' }
] as const;

export const RULE_FIELDS = [
  { value: 'fabricType', label: 'Fabric Type', type: 'string' },
  { value: 'printSize', label: 'Print Size', type: 'enum' },
  { value: 'qualityTier', label: 'Quality Tier', type: 'enum' },
  { value: 'quantity', label: 'Quantity', type: 'number' },
  { value: 'productCategory', label: 'Product Category', type: 'string' },
  { value: 'userTier', label: 'User Tier', type: 'enum' },
  { value: 'seasonalPeriod', label: 'Seasonal Period', type: 'enum' },
  { value: 'customizationComplexity', label: 'Customization Complexity', type: 'enum' }
] as const;

// Default pricing rules for seeding
export const DEFAULT_PRICING_RULES: Partial<PricingRule>[] = [
  {
    name: 'Premium Fabric Surcharge',
    description: 'Additional cost for premium fabric upgrades',
    type: 'fabric_surcharge',
    conditions: [
      { field: 'fabricType', operator: 'in', value: ['Premium Modal', 'Bamboo Silk'] }
    ],
    modifier: 15,
    modifierType: 'fixed',
    customerMessage: 'Premium fabric upgrade',
    valueFraming: 'Luxurious feel and enhanced durability',
    priority: 1,
    isActive: true
  },
  {
    name: 'Large Print Size Cost',
    description: 'Additional cost for large print designs',
    type: 'print_size_cost',
    conditions: [
      { field: 'printSize', operator: 'in', value: ['large', 'full_coverage'] }
    ],
    modifier: 25,
    modifierType: 'percentage',
    customerMessage: 'Large design upgrade',
    valueFraming: 'Maximum impact and visual presence',
    priority: 2,
    isActive: true
  },
  {
    name: 'Bulk Order Discount',
    description: '10% discount for orders of 5 or more items',
    type: 'quantity_discount',
    conditions: [
      { field: 'quantity', operator: 'greater_than', value: 4 }
    ],
    modifier: -10,
    modifierType: 'percentage',
    customerMessage: 'Bulk order savings',
    valueFraming: 'More pieces, more savings',
    priority: 3,
    isActive: true
  }
];
