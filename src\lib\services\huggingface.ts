/**
 * Hugging Face OOTDiffusion Service
 * 
 * Handles communication with Hugging Face Spaces for AI try-on functionality
 * using the OOTDiffusion model for outfit try-on generation.
 */

import { Client } from '@gradio/client';
import { HuggingFaceOOTDRequest, HuggingFaceOOTDResponse } from '@/types';

// Configuration for Hugging Face OOTDiffusion Space
const HUGGINGFACE_CONFIG = {
  // OOTDiffusion Space URL - replace with actual space URL
  SPACE_URL: process.env.HUGGINGFACE_OOTD_SPACE_URL || 'https://huggingface.co/spaces/levihsu/OOTDiffusion',
  
  // Default parameters for OOTDiffusion
  DEFAULT_INFERENCE_STEPS: 20,
  DEFAULT_GUIDANCE_SCALE: 2.0,
  DEFAULT_SEED: -1, // Random seed
  
  // Timeout settings
  REQUEST_TIMEOUT: 120000, // 2 minutes
  MAX_RETRIES: 3,
  
  // Image processing
  MAX_IMAGE_SIZE: 1024,
  SUPPORTED_FORMATS: ['image/jpeg', 'image/png', 'image/webp'],
} as const;

/**
 * Hugging Face OOTDiffusion Service Class
 */
export class HuggingFaceOOTDService {
  private client: Client | null = null;
  private isInitialized = false;

  /**
   * Initialize the Gradio client connection
   */
  async initialize(): Promise<void> {
    try {
      if (this.isInitialized && this.client) {
        return;
      }

      console.log('Initializing Hugging Face OOTDiffusion client...');
      
      this.client = await Client.connect(HUGGINGFACE_CONFIG.SPACE_URL, {
        timeout: HUGGINGFACE_CONFIG.REQUEST_TIMEOUT,
      });
      
      this.isInitialized = true;
      console.log('Hugging Face client initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Hugging Face client:', error);
      throw new Error('Failed to connect to Hugging Face OOTDiffusion service');
    }
  }

  /**
   * Generate try-on image using OOTDiffusion
   */
  async generateTryOn(request: HuggingFaceOOTDRequest): Promise<HuggingFaceOOTDResponse> {
    const startTime = Date.now();
    
    try {
      // Ensure client is initialized
      await this.initialize();
      
      if (!this.client) {
        throw new Error('Hugging Face client not initialized');
      }

      // Validate request
      this.validateRequest(request);

      console.log('Starting OOTDiffusion generation...');
      
      // Prepare parameters for the OOTDiffusion model
      const parameters = {
        person_image: request.person_image,
        garment_image: request.garment_image,
        category: request.category,
        num_inference_steps: request.num_inference_steps || HUGGINGFACE_CONFIG.DEFAULT_INFERENCE_STEPS,
        guidance_scale: request.guidance_scale || HUGGINGFACE_CONFIG.DEFAULT_GUIDANCE_SCALE,
        seed: request.seed || HUGGINGFACE_CONFIG.DEFAULT_SEED,
      };

      // Call the OOTDiffusion model
      // Note: The exact endpoint name may vary based on the specific Space implementation
      const result = await this.client.predict('/generate', parameters);
      
      const processingTime = Math.round((Date.now() - startTime) / 1000);
      
      console.log(`OOTDiffusion completed in ${processingTime}s`);

      // Process the result
      if (result && result.data && result.data[0]) {
        return {
          success: true,
          result_image: result.data[0], // Assuming the result is a base64 image
          processing_time: processingTime,
        };
      } else {
        throw new Error('Invalid response from OOTDiffusion model');
      }

    } catch (error) {
      const processingTime = Math.round((Date.now() - startTime) / 1000);
      
      console.error('OOTDiffusion generation failed:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        processing_time: processingTime,
      };
    }
  }

  /**
   * Generate try-on with retry logic
   */
  async generateTryOnWithRetry(request: HuggingFaceOOTDRequest, maxRetries = HUGGINGFACE_CONFIG.MAX_RETRIES): Promise<HuggingFaceOOTDResponse> {
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Try-on generation attempt ${attempt}/${maxRetries}`);
        
        const result = await this.generateTryOn(request);
        
        if (result.success) {
          return result;
        }
        
        lastError = new Error(result.error || 'Generation failed');
        
        // Wait before retry (exponential backoff)
        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s...
          console.log(`Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
        
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt) * 1000;
          console.log(`Retrying in ${delay}ms due to error:`, error);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    return {
      success: false,
      error: lastError?.message || 'All retry attempts failed',
    };
  }

  /**
   * Validate the request parameters
   */
  private validateRequest(request: HuggingFaceOOTDRequest): void {
    if (!request.person_image) {
      throw new Error('Person image is required');
    }
    
    if (!request.garment_image) {
      throw new Error('Garment image is required');
    }
    
    if (!['upper_body', 'lower_body', 'dresses'].includes(request.category)) {
      throw new Error('Invalid category. Must be upper_body, lower_body, or dresses');
    }
    
    // Validate base64 images
    if (!this.isValidBase64Image(request.person_image)) {
      throw new Error('Invalid person image format');
    }
    
    if (!this.isValidBase64Image(request.garment_image)) {
      throw new Error('Invalid garment image format');
    }
  }

  /**
   * Validate base64 image format
   */
  private isValidBase64Image(base64String: string): boolean {
    try {
      // Check if it's a valid base64 string with image data URL prefix
      const base64Regex = /^data:image\/(jpeg|jpg|png|webp);base64,/;
      return base64Regex.test(base64String);
    } catch {
      return false;
    }
  }

  /**
   * Convert image buffer to base64 data URL
   */
  static bufferToBase64DataUrl(buffer: Buffer, mimeType: string): string {
    const base64 = buffer.toString('base64');
    return `data:${mimeType};base64,${base64}`;
  }

  /**
   * Extract base64 data from data URL
   */
  static extractBase64FromDataUrl(dataUrl: string): string {
    const matches = dataUrl.match(/^data:image\/[a-zA-Z]+;base64,(.+)$/);
    if (!matches || matches.length !== 2) {
      throw new Error('Invalid data URL format');
    }
    return matches[1];
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    if (this.client) {
      try {
        // Close the client connection if available
        // Note: Gradio client may not have explicit close method
        this.client = null;
        this.isInitialized = false;
        console.log('Hugging Face client cleaned up');
      } catch (error) {
        console.error('Error during cleanup:', error);
      }
    }
  }
}

// Export singleton instance
export const huggingFaceService = new HuggingFaceOOTDService();

// Export utility functions
export const HuggingFaceUtils = {
  bufferToBase64DataUrl: HuggingFaceOOTDService.bufferToBase64DataUrl,
  extractBase64FromDataUrl: HuggingFaceOOTDService.extractBase64FromDataUrl,
};
