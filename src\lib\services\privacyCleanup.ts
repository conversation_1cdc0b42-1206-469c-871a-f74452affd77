import { PrismaClient } from '@prisma/client';
import { promises as fs } from 'fs';
import path from 'path';

const prisma = new PrismaClient();

export interface CleanupResult {
  deletedJobs: number;
  deletedFiles: number;
  errors: string[];
  processedAt: Date;
}

export class PrivacyCleanupService {
  private static instance: PrivacyCleanupService;

  public static getInstance(): PrivacyCleanupService {
    if (!PrivacyCleanupService.instance) {
      PrivacyCleanupService.instance = new PrivacyCleanupService();
    }
    return PrivacyCleanupService.instance;
  }

  /**
   * Run automatic cleanup of expired AI try-on data
   */
  async runCleanup(): Promise<CleanupResult> {
    const result: CleanupResult = {
      deletedJobs: 0,
      deletedFiles: 0,
      errors: [],
      processedAt: new Date(),
    };

    try {
      console.log('Starting privacy cleanup process...');

      // Find expired try-on jobs that need cleanup
      const expiredJobs = await this.findExpiredJobs();
      console.log(`Found ${expiredJobs.length} expired jobs to clean up`);

      // Process each expired job
      for (const job of expiredJobs) {
        try {
          await this.cleanupJob(job);
          result.deletedJobs++;
        } catch (error) {
          const errorMsg = `Failed to cleanup job ${job.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          console.error(errorMsg);
          result.errors.push(errorMsg);
        }
      }

      // Clean up orphaned files
      const orphanedFiles = await this.findOrphanedFiles();
      for (const filePath of orphanedFiles) {
        try {
          await this.deleteFile(filePath);
          result.deletedFiles++;
        } catch (error) {
          const errorMsg = `Failed to delete file ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          console.error(errorMsg);
          result.errors.push(errorMsg);
        }
      }

      console.log(`Cleanup completed: ${result.deletedJobs} jobs, ${result.deletedFiles} files deleted`);
      
      // Log cleanup result to database
      await this.logCleanupResult(result);

    } catch (error) {
      const errorMsg = `Cleanup process failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      console.error(errorMsg);
      result.errors.push(errorMsg);
    }

    return result;
  }

  /**
   * Find AI try-on jobs that have expired based on user retention preferences
   */
  private async findExpiredJobs() {
    const now = new Date();

    // Get jobs that should be deleted based on user retention settings
    const expiredJobs = await prisma.aiTryOnJob.findMany({
      where: {
        isDeleted: false,
        status: { in: ['COMPLETED', 'FAILED'] },
        OR: [
          // Jobs with explicit scheduled deletion
          {
            scheduledDeletion: {
              lte: now,
            },
          },
          // Jobs older than user's retention preference
          {
            AND: [
              { scheduledDeletion: null },
              {
                user: {
                  dataRetentionDays: {
                    not: null,
                  },
                },
              },
            ],
          },
        ],
      },
      include: {
        user: {
          select: {
            dataRetentionDays: true,
          },
        },
      },
    });

    // Filter jobs that are actually expired
    return expiredJobs.filter(job => {
      if (job.scheduledDeletion) {
        return job.scheduledDeletion <= now;
      }

      const retentionDays = job.user.dataRetentionDays || 30;
      const expirationDate = new Date(job.createdAt.getTime() + retentionDays * 24 * 60 * 60 * 1000);
      return expirationDate <= now;
    });
  }

  /**
   * Clean up a specific AI try-on job
   */
  private async cleanupJob(job: any) {
    console.log(`Cleaning up job ${job.id}`);

    // Delete associated files
    const filesToDelete = [];
    
    if (job.userPhotoUrl && this.isLocalFile(job.userPhotoUrl)) {
      filesToDelete.push(job.userPhotoUrl);
    }
    
    if (job.resultImageUrl && this.isLocalFile(job.resultImageUrl)) {
      filesToDelete.push(job.resultImageUrl);
    }

    // Delete files
    for (const filePath of filesToDelete) {
      try {
        await this.deleteFile(filePath);
      } catch (error) {
        console.warn(`Failed to delete file ${filePath}:`, error);
      }
    }

    // Update database record
    await prisma.aiTryOnJob.update({
      where: { id: job.id },
      data: {
        isDeleted: true,
        deletedAt: new Date(),
        userPhotoUrl: '[DELETED]',
        resultImageUrl: job.resultImageUrl ? '[DELETED]' : null,
      },
    });
  }

  /**
   * Find orphaned files that are no longer referenced in the database
   */
  private async findOrphanedFiles(): Promise<string[]> {
    // This is a simplified implementation
    // In a real system, you'd scan the upload directories and check against the database
    return [];
  }

  /**
   * Delete a file from the filesystem
   */
  private async deleteFile(filePath: string) {
    if (!this.isLocalFile(filePath)) {
      return; // Skip external URLs
    }

    try {
      // Convert URL to file system path
      const localPath = this.urlToFilePath(filePath);
      await fs.unlink(localPath);
      console.log(`Deleted file: ${localPath}`);
    } catch (error) {
      if ((error as any).code !== 'ENOENT') {
        throw error; // Re-throw if it's not a "file not found" error
      }
    }
  }

  /**
   * Check if a URL points to a local file
   */
  private isLocalFile(url: string): boolean {
    return url.startsWith('/uploads/') || url.startsWith('/api/files/');
  }

  /**
   * Convert a URL to a file system path
   */
  private urlToFilePath(url: string): string {
    // This is a simplified implementation
    // Adjust based on your file storage structure
    if (url.startsWith('/uploads/')) {
      return path.join(process.cwd(), 'public', url);
    }
    
    if (url.startsWith('/api/files/')) {
      const filename = url.split('/').pop();
      return path.join(process.cwd(), 'uploads', filename || '');
    }

    throw new Error(`Cannot convert URL to file path: ${url}`);
  }

  /**
   * Log cleanup result to database for audit purposes
   */
  private async logCleanupResult(result: CleanupResult) {
    try {
      // You could create a CleanupLog model to track cleanup operations
      console.log('Cleanup result:', {
        deletedJobs: result.deletedJobs,
        deletedFiles: result.deletedFiles,
        errorCount: result.errors.length,
        processedAt: result.processedAt,
      });
    } catch (error) {
      console.error('Failed to log cleanup result:', error);
    }
  }

  /**
   * Schedule automatic cleanup to run periodically
   */
  static scheduleCleanup() {
    const service = PrivacyCleanupService.getInstance();
    
    // Run cleanup every 24 hours
    setInterval(async () => {
      try {
        await service.runCleanup();
      } catch (error) {
        console.error('Scheduled cleanup failed:', error);
      }
    }, 24 * 60 * 60 * 1000);

    console.log('Privacy cleanup scheduled to run every 24 hours');
  }

  /**
   * Clean up data for a specific user (for GDPR deletion requests)
   */
  async cleanupUserData(userId: string, options: {
    includeDesigns?: boolean;
    includeOrders?: boolean;
  } = {}): Promise<CleanupResult> {
    const result: CleanupResult = {
      deletedJobs: 0,
      deletedFiles: 0,
      errors: [],
      processedAt: new Date(),
    };

    try {
      // Get all user's AI try-on jobs
      const userJobs = await prisma.aiTryOnJob.findMany({
        where: {
          userId,
          isDeleted: false,
        },
      });

      // Clean up each job
      for (const job of userJobs) {
        try {
          await this.cleanupJob(job);
          result.deletedJobs++;
        } catch (error) {
          const errorMsg = `Failed to cleanup user job ${job.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          result.errors.push(errorMsg);
        }
      }

      // Optionally clean up designs and customizations
      if (options.includeDesigns) {
        await prisma.design.updateMany({
          where: { userId },
          data: { status: 'ARCHIVED' },
        });

        await prisma.customization.updateMany({
          where: { userId },
          data: { status: 'ARCHIVED' },
        });
      }

      console.log(`User data cleanup completed for ${userId}: ${result.deletedJobs} jobs deleted`);

    } catch (error) {
      const errorMsg = `User data cleanup failed for ${userId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
      console.error(errorMsg);
      result.errors.push(errorMsg);
    }

    return result;
  }
}
