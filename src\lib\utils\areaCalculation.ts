/**
 * Area Calculation Utilities for Ottiq Customization Editor
 * 
 * Provides accurate print area calculation in cm² considering:
 * - Element overlaps and intersections
 * - Transformations (rotation, scaling)
 * - Real-world print dimensions
 * - Canvas to physical dimension conversion
 */

import { DesignElement, DesignCanvas } from '@/types';

// Standard print dimensions and DPI settings
export const PRINT_CONFIG = {
  // Standard canvas size in pixels (this should match your editor canvas)
  CANVAS_WIDTH: 800,
  CANVAS_HEIGHT: 600,
  
  // Standard print area in cm (adjust based on your products)
  PRINT_WIDTH_CM: 25, // 25cm wide print area
  PRINT_HEIGHT_CM: 20, // 20cm tall print area
  
  // DPI for high-quality printing
  PRINT_DPI: 300,
  
  // Minimum printable area in cm²
  MIN_PRINT_AREA: 1,
  
  // Maximum printable area in cm²
  MAX_PRINT_AREA: 500,
} as const;

// Print size categories for pricing
export const PRINT_SIZE_CATEGORIES = {
  small: { maxArea: 25, label: 'Small Print', description: 'Perfect for subtle details' },
  medium: { maxArea: 100, label: 'Medium Print', description: 'Great for statement pieces' },
  large: { maxArea: 250, label: 'Large Print', description: 'Bold and eye-catching' },
  full_coverage: { maxArea: 500, label: 'Full Coverage', description: 'Maximum impact design' },
} as const;

export type PrintSizeCategory = keyof typeof PRINT_SIZE_CATEGORIES;

/**
 * Convert canvas pixels to centimeters
 */
export function pixelsToCm(pixels: number, isWidth: boolean = true): number {
  const canvasSize = isWidth ? PRINT_CONFIG.CANVAS_WIDTH : PRINT_CONFIG.CANVAS_HEIGHT;
  const printSize = isWidth ? PRINT_CONFIG.PRINT_WIDTH_CM : PRINT_CONFIG.PRINT_HEIGHT_CM;
  
  return (pixels / canvasSize) * printSize;
}

/**
 * Convert centimeters to canvas pixels
 */
export function cmToPixels(cm: number, isWidth: boolean = true): number {
  const canvasSize = isWidth ? PRINT_CONFIG.CANVAS_WIDTH : PRINT_CONFIG.CANVAS_HEIGHT;
  const printSize = isWidth ? PRINT_CONFIG.PRINT_WIDTH_CM : PRINT_CONFIG.PRINT_HEIGHT_CM;
  
  return (cm / printSize) * canvasSize;
}

/**
 * Calculate the bounding box of a rotated element
 */
export function getRotatedBoundingBox(element: DesignElement): {
  x: number;
  y: number;
  width: number;
  height: number;
} {
  if (!element.rotation || element.rotation === 0) {
    return {
      x: element.x,
      y: element.y,
      width: element.width,
      height: element.height,
    };
  }

  const centerX = element.x + element.width / 2;
  const centerY = element.y + element.height / 2;
  const angle = (element.rotation * Math.PI) / 180;

  // Calculate the corners of the original rectangle
  const corners = [
    { x: element.x, y: element.y },
    { x: element.x + element.width, y: element.y },
    { x: element.x + element.width, y: element.y + element.height },
    { x: element.x, y: element.y + element.height },
  ];

  // Rotate each corner around the center
  const rotatedCorners = corners.map(corner => {
    const dx = corner.x - centerX;
    const dy = corner.y - centerY;
    
    return {
      x: centerX + dx * Math.cos(angle) - dy * Math.sin(angle),
      y: centerY + dx * Math.sin(angle) + dy * Math.cos(angle),
    };
  });

  // Find the bounding box of the rotated corners
  const minX = Math.min(...rotatedCorners.map(c => c.x));
  const maxX = Math.max(...rotatedCorners.map(c => c.x));
  const minY = Math.min(...rotatedCorners.map(c => c.y));
  const maxY = Math.max(...rotatedCorners.map(c => c.y));

  return {
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY,
  };
}

/**
 * Calculate the area of a single element in cm²
 */
export function calculateElementArea(element: DesignElement): number {
  if (!element.visible || element.opacity === 0) {
    return 0;
  }

  const boundingBox = getRotatedBoundingBox(element);
  const widthCm = pixelsToCm(boundingBox.width, true);
  const heightCm = pixelsToCm(boundingBox.height, false);
  
  return widthCm * heightCm;
}

/**
 * Check if two rectangles intersect
 */
export function rectanglesIntersect(
  rect1: { x: number; y: number; width: number; height: number },
  rect2: { x: number; y: number; width: number; height: number }
): boolean {
  return !(
    rect1.x + rect1.width <= rect2.x ||
    rect2.x + rect2.width <= rect1.x ||
    rect1.y + rect1.height <= rect2.y ||
    rect2.y + rect2.height <= rect1.y
  );
}

/**
 * Calculate the intersection area between two rectangles
 */
export function calculateIntersectionArea(
  rect1: { x: number; y: number; width: number; height: number },
  rect2: { x: number; y: number; width: number; height: number }
): number {
  if (!rectanglesIntersect(rect1, rect2)) {
    return 0;
  }

  const left = Math.max(rect1.x, rect2.x);
  const right = Math.min(rect1.x + rect1.width, rect2.x + rect2.width);
  const top = Math.max(rect1.y, rect2.y);
  const bottom = Math.min(rect1.y + rect1.height, rect2.y + rect2.height);

  const widthCm = pixelsToCm(right - left, true);
  const heightCm = pixelsToCm(bottom - top, false);

  return Math.max(0, widthCm * heightCm);
}

/**
 * Calculate total print area considering overlaps
 * Uses a simplified approach for performance - can be enhanced with more sophisticated algorithms
 */
export function calculateTotalPrintArea(elements: DesignElement[]): {
  totalArea: number;
  elementAreas: Array<{ id: string; area: number }>;
  overlappingArea: number;
  printSizeCategory: PrintSizeCategory;
  isWithinLimits: boolean;
} {
  const visibleElements = elements.filter(el => el.visible && el.opacity > 0);

  if (visibleElements.length === 0) {
    return {
      totalArea: 0,
      elementAreas: [],
      overlappingArea: 0,
      printSizeCategory: 'small',
      isWithinLimits: true,
    };
  }

  // Calculate individual element areas
  const elementAreas = visibleElements.map(element => ({
    id: element.id,
    area: calculateElementArea(element),
  }));

  // Simple approach: calculate union of all bounding boxes
  // For more accuracy, you could implement a more sophisticated overlap detection
  const boundingBoxes = visibleElements.map(getRotatedBoundingBox);

  if (boundingBoxes.length === 1) {
    const totalArea = elementAreas[0].area;
    return {
      totalArea,
      elementAreas,
      overlappingArea: 0,
      printSizeCategory: getPrintSizeCategory(totalArea),
      isWithinLimits: isAreaWithinLimits(totalArea),
    };
  }

  // Calculate overall bounding box
  const minX = Math.min(...boundingBoxes.map(box => box.x));
  const maxX = Math.max(...boundingBoxes.map(box => box.x + box.width));
  const minY = Math.min(...boundingBoxes.map(box => box.y));
  const maxY = Math.max(...boundingBoxes.map(box => box.y + box.height));

  const totalWidthCm = pixelsToCm(maxX - minX, true);
  const totalHeightCm = pixelsToCm(maxY - minY, false);
  const totalArea = totalWidthCm * totalHeightCm;

  // Estimate overlapping area (simplified)
  const sumOfIndividualAreas = elementAreas.reduce((sum, el) => sum + el.area, 0);
  const overlappingArea = Math.max(0, sumOfIndividualAreas - totalArea);

  return {
    totalArea,
    elementAreas,
    overlappingArea,
    printSizeCategory: getPrintSizeCategory(totalArea),
    isWithinLimits: isAreaWithinLimits(totalArea),
  };
}

/**
 * Get print size category based on area
 */
export function getPrintSizeCategory(area: number): PrintSizeCategory {
  if (area <= PRINT_SIZE_CATEGORIES.small.maxArea) return 'small';
  if (area <= PRINT_SIZE_CATEGORIES.medium.maxArea) return 'medium';
  if (area <= PRINT_SIZE_CATEGORIES.large.maxArea) return 'large';
  return 'full_coverage';
}

/**
 * Check if area is within printable limits
 */
export function isAreaWithinLimits(area: number): boolean {
  return area >= PRINT_CONFIG.MIN_PRINT_AREA && area <= PRINT_CONFIG.MAX_PRINT_AREA;
}

/**
 * Get pricing impact message based on area change
 */
export function getPricingImpactMessage(
  oldArea: number,
  newArea: number
): {
  hasImpact: boolean;
  message: string;
  type: 'positive' | 'negative' | 'neutral';
  emotionalMessage: string;
} {
  const oldCategory = getPrintSizeCategory(oldArea);
  const newCategory = getPrintSizeCategory(newArea);

  if (oldCategory === newCategory) {
    return {
      hasImpact: false,
      message: 'No pricing change',
      type: 'neutral',
      emotionalMessage: 'Perfect! Your design stays in the same price range.',
    };
  }

  const categoryOrder = ['small', 'medium', 'large', 'full_coverage'];
  const oldIndex = categoryOrder.indexOf(oldCategory);
  const newIndex = categoryOrder.indexOf(newCategory);

  if (newIndex > oldIndex) {
    return {
      hasImpact: true,
      message: `Upgraded to ${PRINT_SIZE_CATEGORIES[newCategory].label}`,
      type: 'positive',
      emotionalMessage: `🚀 Bigger impact for your style! Your design just got more powerful.`,
    };
  } else {
    return {
      hasImpact: true,
      message: `Reduced to ${PRINT_SIZE_CATEGORIES[newCategory].label}`,
      type: 'negative',
      emotionalMessage: `💡 Smaller print, smarter price. Still looks amazing!`,
    };
  }
}

/**
 * Calculate print area for canvas data (main function for API)
 */
export function calculateCanvasPrintArea(canvas: DesignCanvas): {
  totalArea: number;
  elementAreas: Array<{ id: string; area: number; type: string }>;
  overlappingArea: number;
  printSizeCategory: PrintSizeCategory;
  isWithinLimits: boolean;
  recommendations?: string[];
} {
  const result = calculateTotalPrintArea(canvas.elements);

  // Add element type information
  const elementAreas = result.elementAreas.map(el => {
    const element = canvas.elements.find(e => e.id === el.id);
    return {
      ...el,
      type: element?.type || 'unknown',
    };
  });

  // Generate recommendations
  const recommendations: string[] = [];

  if (!result.isWithinLimits) {
    if (result.totalArea < PRINT_CONFIG.MIN_PRINT_AREA) {
      recommendations.push('Consider making your design larger for better visibility');
    } else {
      recommendations.push('Your design is quite large - consider reducing size to lower costs');
    }
  }

  if (result.overlappingArea > result.totalArea * 0.3) {
    recommendations.push('Elements are heavily overlapping - consider spacing them out');
  }

  return {
    ...result,
    elementAreas,
    recommendations: recommendations.length > 0 ? recommendations : undefined,
  };
}
