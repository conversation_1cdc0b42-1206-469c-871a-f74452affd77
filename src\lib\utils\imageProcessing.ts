/**
 * Server-side Image Processing Utilities for Ottiq
 * 
 * Handles canvas to image conversion, optimization, and preview generation
 * using Sharp for high-performance image processing
 */

import sharp from 'sharp';
import { DesignCanvas, DesignElement } from '@/types';

// Image processing configuration
export const IMAGE_CONFIG = {
  // Preview image settings
  PREVIEW_WIDTH: 800,
  PREVIEW_HEIGHT: 600,
  PREVIEW_QUALITY: 85,
  PREVIEW_FORMAT: 'png' as const,
  
  // High-resolution export settings
  EXPORT_DPI: 300,
  EXPORT_QUALITY: 95,
  EXPORT_FORMAT: 'png' as const,
  
  // Thumbnail settings
  THUMBNAIL_SIZE: 200,
  THUMBNAIL_QUALITY: 80,
  
  // Maximum file sizes (in bytes)
  MAX_PREVIEW_SIZE: 2 * 1024 * 1024, // 2MB
  MAX_EXPORT_SIZE: 10 * 1024 * 1024, // 10MB
} as const;

/**
 * Convert canvas data to SVG string
 * This is a simplified version - in production you'd want more robust SVG generation
 */
export function canvasToSVG(canvas: DesignCanvas): string {
  const { width, height, backgroundColor, elements } = canvas;
  
  let svgContent = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;
  
  // Add background
  svgContent += `<rect width="100%" height="100%" fill="${backgroundColor}"/>`;
  
  // Add elements (simplified - you'd need more complex rendering for real implementation)
  elements.forEach(element => {
    if (!element.visible || element.opacity === 0) return;
    
    const transform = element.rotation 
      ? `transform="rotate(${element.rotation} ${element.x + element.width/2} ${element.y + element.height/2})"` 
      : '';
    
    if (element.type === 'text' && element.data) {
      const textData = element.data as any;
      svgContent += `<text x="${element.x}" y="${element.y + element.height/2}" 
        font-family="${textData.fontFamily || 'Arial'}" 
        font-size="${textData.fontSize || 16}" 
        font-weight="${textData.fontWeight || 'normal'}"
        fill="${textData.color || '#000000'}" 
        opacity="${element.opacity}"
        ${transform}>
        ${textData.text || ''}
      </text>`;
    } else if (element.type === 'image' && element.data) {
      const imageData = element.data as any;
      svgContent += `<image x="${element.x}" y="${element.y}" 
        width="${element.width}" height="${element.height}" 
        href="${imageData.src}" 
        opacity="${element.opacity}"
        ${transform}/>`;
    } else {
      // Generic rectangle for other elements
      svgContent += `<rect x="${element.x}" y="${element.y}" 
        width="${element.width}" height="${element.height}" 
        fill="#cccccc" 
        opacity="${element.opacity}"
        ${transform}/>`;
    }
  });
  
  svgContent += '</svg>';
  return svgContent;
}

/**
 * Generate preview image from canvas data
 */
export async function generatePreviewImage(
  canvas: DesignCanvas,
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'png' | 'jpeg' | 'webp';
  } = {}
): Promise<{
  buffer: Buffer;
  metadata: {
    width: number;
    height: number;
    format: string;
    size: number;
  };
}> {
  const {
    width = IMAGE_CONFIG.PREVIEW_WIDTH,
    height = IMAGE_CONFIG.PREVIEW_HEIGHT,
    quality = IMAGE_CONFIG.PREVIEW_QUALITY,
    format = IMAGE_CONFIG.PREVIEW_FORMAT,
  } = options;

  try {
    // Convert canvas to SVG
    const svgString = canvasToSVG(canvas);
    const svgBuffer = Buffer.from(svgString);

    // Process with Sharp
    let sharpInstance = sharp(svgBuffer)
      .resize(width, height, {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 1 }
      });

    // Apply format-specific settings
    if (format === 'png') {
      sharpInstance = sharpInstance.png({ quality, compressionLevel: 6 });
    } else if (format === 'jpeg') {
      sharpInstance = sharpInstance.jpeg({ quality, progressive: true });
    } else if (format === 'webp') {
      sharpInstance = sharpInstance.webp({ quality });
    }

    const buffer = await sharpInstance.toBuffer();
    const metadata = await sharp(buffer).metadata();

    return {
      buffer,
      metadata: {
        width: metadata.width || width,
        height: metadata.height || height,
        format: metadata.format || format,
        size: buffer.length,
      },
    };
  } catch (error) {
    console.error('Error generating preview image:', error);
    throw new Error('Failed to generate preview image');
  }
}

/**
 * Generate thumbnail from preview image
 */
export async function generateThumbnail(
  imageBuffer: Buffer,
  size: number = IMAGE_CONFIG.THUMBNAIL_SIZE
): Promise<Buffer> {
  try {
    return await sharp(imageBuffer)
      .resize(size, size, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({ quality: IMAGE_CONFIG.THUMBNAIL_QUALITY })
      .toBuffer();
  } catch (error) {
    console.error('Error generating thumbnail:', error);
    throw new Error('Failed to generate thumbnail');
  }
}

/**
 * Optimize image for web delivery
 */
export async function optimizeImage(
  imageBuffer: Buffer,
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    format?: 'png' | 'jpeg' | 'webp';
  } = {}
): Promise<Buffer> {
  const {
    maxWidth = 1200,
    maxHeight = 1200,
    quality = 85,
    format = 'webp',
  } = options;

  try {
    let sharpInstance = sharp(imageBuffer);
    
    // Get original metadata
    const metadata = await sharpInstance.metadata();
    
    // Resize if necessary
    if (metadata.width && metadata.height) {
      if (metadata.width > maxWidth || metadata.height > maxHeight) {
        sharpInstance = sharpInstance.resize(maxWidth, maxHeight, {
          fit: 'inside',
          withoutEnlargement: true
        });
      }
    }

    // Apply format and quality
    if (format === 'webp') {
      sharpInstance = sharpInstance.webp({ quality });
    } else if (format === 'jpeg') {
      sharpInstance = sharpInstance.jpeg({ quality, progressive: true });
    } else {
      sharpInstance = sharpInstance.png({ quality: Math.round(quality / 10) });
    }

    return await sharpInstance.toBuffer();
  } catch (error) {
    console.error('Error optimizing image:', error);
    throw new Error('Failed to optimize image');
  }
}

/**
 * Validate image buffer and metadata
 */
export async function validateImage(imageBuffer: Buffer): Promise<{
  isValid: boolean;
  metadata?: sharp.Metadata;
  error?: string;
}> {
  try {
    const metadata = await sharp(imageBuffer).metadata();
    
    // Check if it's a valid image
    if (!metadata.width || !metadata.height || !metadata.format) {
      return {
        isValid: false,
        error: 'Invalid image format or corrupted file'
      };
    }

    // Check file size limits
    if (imageBuffer.length > IMAGE_CONFIG.MAX_EXPORT_SIZE) {
      return {
        isValid: false,
        error: 'Image file size exceeds maximum limit'
      };
    }

    return {
      isValid: true,
      metadata
    };
  } catch (error) {
    return {
      isValid: false,
      error: 'Failed to process image file'
    };
  }
}

/**
 * Generate multiple image variants (preview, thumbnail, optimized)
 */
export async function generateImageVariants(canvas: DesignCanvas): Promise<{
  preview: Buffer;
  thumbnail: Buffer;
  optimized: Buffer;
  metadata: {
    preview: any;
    thumbnail: any;
    optimized: any;
  };
}> {
  try {
    // Generate preview
    const previewResult = await generatePreviewImage(canvas);
    
    // Generate thumbnail from preview
    const thumbnail = await generateThumbnail(previewResult.buffer);
    
    // Generate optimized version
    const optimized = await optimizeImage(previewResult.buffer);
    
    // Get metadata for all variants
    const thumbnailMetadata = await sharp(thumbnail).metadata();
    const optimizedMetadata = await sharp(optimized).metadata();

    return {
      preview: previewResult.buffer,
      thumbnail,
      optimized,
      metadata: {
        preview: previewResult.metadata,
        thumbnail: {
          width: thumbnailMetadata.width,
          height: thumbnailMetadata.height,
          format: thumbnailMetadata.format,
          size: thumbnail.length,
        },
        optimized: {
          width: optimizedMetadata.width,
          height: optimizedMetadata.height,
          format: optimizedMetadata.format,
          size: optimized.length,
        },
      },
    };
  } catch (error) {
    console.error('Error generating image variants:', error);
    throw new Error('Failed to generate image variants');
  }
}
