/**
 * Image Upload and Validation Utilities for AI Try-On
 * 
 * Handles secure image upload, validation, and processing for user portraits
 * and garment images used in AI try-on functionality.
 */

import sharp from 'sharp';
import { NextRequest } from 'next/server';

// Configuration for image uploads
export const IMAGE_UPLOAD_CONFIG = {
  // File size limits
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_PORTRAIT_SIZE: 5 * 1024 * 1024, // 5MB for portraits
  
  // Image dimensions
  MAX_WIDTH: 2048,
  MAX_HEIGHT: 2048,
  MIN_WIDTH: 256,
  MIN_HEIGHT: 256,
  
  // Supported formats
  SUPPORTED_FORMATS: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  
  // Processing settings
  PORTRAIT_TARGET_SIZE: 512, // Target size for AI processing
  GARMENT_TARGET_SIZE: 512,
  QUALITY: 90,
  
  // Security
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.webp'],
  SCAN_FOR_MALWARE: false, // Set to true in production with proper scanner
} as const;

export interface ImageUploadResult {
  success: boolean;
  data?: {
    originalBuffer: Buffer;
    processedBuffer: Buffer;
    metadata: {
      width: number;
      height: number;
      format: string;
      size: number;
      processedSize: number;
    };
    base64DataUrl: string;
    mimeType: string;
  };
  error?: string;
}

export interface ImageValidationResult {
  isValid: boolean;
  error?: string;
  metadata?: {
    width: number;
    height: number;
    format: string;
    size: number;
  };
}

/**
 * Parse and validate uploaded image from FormData
 */
export async function parseUploadedImage(
  request: NextRequest,
  fieldName: string = 'image'
): Promise<ImageUploadResult> {
  try {
    const formData = await request.formData();
    const file = formData.get(fieldName) as File;
    
    if (!file) {
      return {
        success: false,
        error: `No file found in field '${fieldName}'`,
      };
    }
    
    // Convert File to Buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Validate the image
    const validation = await validateImage(buffer, file.type);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.error,
      };
    }
    
    // Process the image
    const processed = await processImageForAI(buffer, 'portrait');
    
    return {
      success: true,
      data: processed,
    };
    
  } catch (error) {
    console.error('Error parsing uploaded image:', error);
    return {
      success: false,
      error: 'Failed to process uploaded image',
    };
  }
}

/**
 * Parse base64 image from request body
 */
export async function parseBase64Image(
  base64DataUrl: string,
  imageType: 'portrait' | 'garment' = 'portrait'
): Promise<ImageUploadResult> {
  try {
    // Validate base64 format
    const base64Regex = /^data:image\/(jpeg|jpg|png|webp);base64,(.+)$/;
    const matches = base64DataUrl.match(base64Regex);
    
    if (!matches || matches.length !== 3) {
      return {
        success: false,
        error: 'Invalid base64 image format',
      };
    }
    
    const mimeType = `image/${matches[1]}`;
    const base64Data = matches[2];
    
    // Convert to buffer
    const buffer = Buffer.from(base64Data, 'base64');
    
    // Validate the image
    const validation = await validateImage(buffer, mimeType);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.error,
      };
    }
    
    // Process the image
    const processed = await processImageForAI(buffer, imageType);
    
    return {
      success: true,
      data: processed,
    };
    
  } catch (error) {
    console.error('Error parsing base64 image:', error);
    return {
      success: false,
      error: 'Failed to process base64 image',
    };
  }
}

/**
 * Validate image buffer and metadata
 */
export async function validateImage(
  buffer: Buffer,
  mimeType?: string
): Promise<ImageValidationResult> {
  try {
    // Check file size
    if (buffer.length > IMAGE_UPLOAD_CONFIG.MAX_FILE_SIZE) {
      return {
        isValid: false,
        error: `File size exceeds maximum limit of ${IMAGE_UPLOAD_CONFIG.MAX_FILE_SIZE / (1024 * 1024)}MB`,
      };
    }
    
    // Get image metadata
    const metadata = await sharp(buffer).metadata();
    
    if (!metadata.width || !metadata.height || !metadata.format) {
      return {
        isValid: false,
        error: 'Invalid image format or corrupted file',
      };
    }
    
    // Check format
    const format = `image/${metadata.format}`;
    if (!IMAGE_UPLOAD_CONFIG.SUPPORTED_FORMATS.includes(format)) {
      return {
        isValid: false,
        error: `Unsupported image format. Supported formats: ${IMAGE_UPLOAD_CONFIG.SUPPORTED_FORMATS.join(', ')}`,
      };
    }
    
    // Check dimensions
    if (metadata.width < IMAGE_UPLOAD_CONFIG.MIN_WIDTH || 
        metadata.height < IMAGE_UPLOAD_CONFIG.MIN_HEIGHT) {
      return {
        isValid: false,
        error: `Image too small. Minimum size: ${IMAGE_UPLOAD_CONFIG.MIN_WIDTH}x${IMAGE_UPLOAD_CONFIG.MIN_HEIGHT}`,
      };
    }
    
    if (metadata.width > IMAGE_UPLOAD_CONFIG.MAX_WIDTH || 
        metadata.height > IMAGE_UPLOAD_CONFIG.MAX_HEIGHT) {
      return {
        isValid: false,
        error: `Image too large. Maximum size: ${IMAGE_UPLOAD_CONFIG.MAX_WIDTH}x${IMAGE_UPLOAD_CONFIG.MAX_HEIGHT}`,
      };
    }
    
    // Validate MIME type if provided
    if (mimeType && !IMAGE_UPLOAD_CONFIG.SUPPORTED_FORMATS.includes(mimeType)) {
      return {
        isValid: false,
        error: 'Invalid MIME type',
      };
    }
    
    return {
      isValid: true,
      metadata: {
        width: metadata.width,
        height: metadata.height,
        format: metadata.format,
        size: buffer.length,
      },
    };
    
  } catch (error) {
    console.error('Error validating image:', error);
    return {
      isValid: false,
      error: 'Failed to validate image',
    };
  }
}

/**
 * Process image for AI try-on (resize, optimize, format)
 */
export async function processImageForAI(
  buffer: Buffer,
  imageType: 'portrait' | 'garment' = 'portrait'
): Promise<{
  originalBuffer: Buffer;
  processedBuffer: Buffer;
  metadata: {
    width: number;
    height: number;
    format: string;
    size: number;
    processedSize: number;
  };
  base64DataUrl: string;
  mimeType: string;
}> {
  try {
    const targetSize = imageType === 'portrait' 
      ? IMAGE_UPLOAD_CONFIG.PORTRAIT_TARGET_SIZE 
      : IMAGE_UPLOAD_CONFIG.GARMENT_TARGET_SIZE;
    
    // Get original metadata
    const originalMetadata = await sharp(buffer).metadata();
    
    // Process image: resize, optimize, and convert to RGB
    const processedBuffer = await sharp(buffer)
      .resize(targetSize, targetSize, {
        fit: 'inside',
        withoutEnlargement: false,
      })
      .jpeg({
        quality: IMAGE_UPLOAD_CONFIG.QUALITY,
        progressive: true,
      })
      .toBuffer();
    
    // Get processed metadata
    const processedMetadata = await sharp(processedBuffer).metadata();
    
    // Convert to base64 data URL
    const base64 = processedBuffer.toString('base64');
    const mimeType = 'image/jpeg';
    const base64DataUrl = `data:${mimeType};base64,${base64}`;
    
    return {
      originalBuffer: buffer,
      processedBuffer,
      metadata: {
        width: processedMetadata.width || targetSize,
        height: processedMetadata.height || targetSize,
        format: processedMetadata.format || 'jpeg',
        size: buffer.length,
        processedSize: processedBuffer.length,
      },
      base64DataUrl,
      mimeType,
    };
    
  } catch (error) {
    console.error('Error processing image for AI:', error);
    throw new Error('Failed to process image for AI');
  }
}

/**
 * Validate portrait image specifically for AI try-on
 */
export async function validatePortraitImage(buffer: Buffer): Promise<ImageValidationResult> {
  const baseValidation = await validateImage(buffer);
  
  if (!baseValidation.isValid) {
    return baseValidation;
  }
  
  // Additional portrait-specific validations
  const metadata = baseValidation.metadata!;
  
  // Check aspect ratio (should be roughly portrait or square)
  const aspectRatio = metadata.width / metadata.height;
  if (aspectRatio > 2 || aspectRatio < 0.5) {
    return {
      isValid: false,
      error: 'Portrait image should have a reasonable aspect ratio (not too wide or too tall)',
    };
  }
  
  // Check if image is large enough for good AI results
  if (metadata.width < 256 || metadata.height < 256) {
    return {
      isValid: false,
      error: 'Portrait image should be at least 256x256 pixels for best results',
    };
  }
  
  return {
    isValid: true,
    metadata,
  };
}

/**
 * Security scan for uploaded images (placeholder for production implementation)
 */
export async function scanImageForSecurity(buffer: Buffer): Promise<{
  isSafe: boolean;
  threats?: string[];
}> {
  // In production, integrate with a malware scanning service
  // For now, just do basic checks
  
  try {
    // Check if it's actually an image
    const metadata = await sharp(buffer).metadata();
    
    if (!metadata.format) {
      return {
        isSafe: false,
        threats: ['Not a valid image file'],
      };
    }
    
    // Check for suspicious file size patterns
    if (buffer.length < 100) {
      return {
        isSafe: false,
        threats: ['File too small to be a valid image'],
      };
    }
    
    return {
      isSafe: true,
    };
    
  } catch (error) {
    return {
      isSafe: false,
      threats: ['Failed to analyze file'],
    };
  }
}
