'use client';

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import {
  DesignElement,
  DesignCanvas,
  EditorState,
  PlacementSide,
  TextElementData,
  ImageElementData,
  TouchGestureState,
  GestureConfig,
  SnapGuide,
  Template,
  TemplatePreview,
  ActionGroup,
  HistoryAction
} from '@/types';
import {
  DEFAULT_GESTURE_CONFIG,
  createInitialGestureState
} from '@/utils/touchGestures';
import { generateSnapGuides } from '@/utils/snapGuides';
import { HistoryManager } from '@/utils/historyManager';

interface EditorActions {
  // Canvas actions
  setCanvasSize: (width: number, height: number) => void;
  setBackgroundColor: (color: string) => void;
  
  // Element actions
  addElement: (element: Omit<DesignElement, 'id'>) => void;
  updateElement: (id: string, updates: Partial<DesignElement>) => void;
  deleteElement: (id: string) => void;
  selectElement: (id: string | null) => void;
  duplicateElement: (id: string) => void;
  
  // Tool actions
  setActiveTool: (tool: EditorState['activeTool']) => void;
  setPlacement: (placement: PlacementSide) => void;
  setZoom: (zoom: number) => void;
  
  // History actions
  undo: () => void;
  redo: () => void;
  saveToHistory: () => void;
  
  // Utility actions
  clearCanvas: () => void;
  loadCanvas: (canvas: DesignCanvas) => void;
  setLoading: (loading: boolean) => void;
  
  // Text-specific actions
  addText: (text: string, x?: number, y?: number) => void;
  updateTextData: (id: string, data: Partial<TextElementData>) => void;
  
  // Image-specific actions
  addImage: (src: string, x?: number, y?: number) => void;
  updateImageData: (id: string, data: Partial<ImageElementData>) => void;

  // Touch gesture actions
  updateGestureState: (updates: Partial<TouchGestureState>) => void;
  updateGestureConfig: (updates: Partial<GestureConfig>) => void;

  // Snap guide actions
  updateSnapGuides: () => void;
  setSnapEnabled: (enabled: boolean) => void;
  setSnapThreshold: (threshold: number) => void;
  setGridEnabled: (enabled: boolean) => void;
  setGridSize: (size: number) => void;

  // Template actions
  loadTemplate: (template: Template) => void;
  setSelectedTemplate: (template: Template | null) => void;
  addTemplate: (template: Template) => void;

  // Enhanced history actions
  startActionGroup: (name: string) => void;
  finishActionGroup: () => void;

  // Mobile actions
  setMobileMode: (enabled: boolean) => void;
  setTouchOptimized: (enabled: boolean) => void;
  setHapticEnabled: (enabled: boolean) => void;
}

interface EnhancedEditorState extends EditorState {
  // Touch gesture state
  gestureState: TouchGestureState;
  gestureConfig: GestureConfig;

  // Snap guides
  snapGuides: SnapGuide[];
  snapEnabled: boolean;
  snapThreshold: number;
  gridEnabled: boolean;
  gridSize: number;

  // Template system
  availableTemplates: Template[];
  selectedTemplate: Template | null;
  templatePreviews: TemplatePreview[];

  // Enhanced history
  historyManager: HistoryManager;
  actionGroups: ActionGroup[];
  currentActionGroup: ActionGroup | null;

  // Mobile optimizations
  isMobileMode: boolean;
  touchOptimized: boolean;
  hapticEnabled: boolean;
}

type EditorStore = EnhancedEditorState & EditorActions;

const createInitialCanvas = (): DesignCanvas => ({
  width: 400,
  height: 500,
  backgroundColor: '#ffffff',
  elements: [],
  layers: []
});

const generateId = () => Math.random().toString(36).substring(2, 9);

export const useEditorStore = create<EditorStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      canvas: createInitialCanvas(),
      selectedElementId: null,
      activeTool: 'select',
      placement: 'front',
      zoom: 1,
      isLoading: false,
      history: [createInitialCanvas()],
      historyIndex: 0,

      // Touch gesture state
      gestureState: createInitialGestureState(),
      gestureConfig: DEFAULT_GESTURE_CONFIG,

      // Snap guides
      snapGuides: [],
      snapEnabled: true,
      snapThreshold: 10,
      gridEnabled: true,
      gridSize: 20,

      // Template system
      availableTemplates: [],
      selectedTemplate: null,
      templatePreviews: [],

      // Enhanced history
      historyManager: new HistoryManager(createInitialCanvas()),
      actionGroups: [],
      currentActionGroup: null,

      // Mobile optimizations
      isMobileMode: false,
      touchOptimized: true,
      hapticEnabled: true,

      // Canvas actions
      setCanvasSize: (width, height) => {
        set((state) => ({
          canvas: { ...state.canvas, width, height }
        }));
        get().saveToHistory();
      },

      setBackgroundColor: (color) => {
        set((state) => ({
          canvas: { ...state.canvas, backgroundColor: color }
        }));
        get().saveToHistory();
      },

      // Element actions
      addElement: (elementData) => {
        const id = generateId();
        const element: DesignElement = { ...elementData, id };
        
        set((state) => ({
          canvas: {
            ...state.canvas,
            elements: [...state.canvas.elements, element],
            layers: [...state.canvas.layers, id]
          },
          selectedElementId: id
        }));
        get().saveToHistory();
      },

      updateElement: (id, updates) => {
        set((state) => ({
          canvas: {
            ...state.canvas,
            elements: state.canvas.elements.map(el =>
              el.id === id ? { ...el, ...updates } : el
            )
          }
        }));
      },

      deleteElement: (id) => {
        set((state) => ({
          canvas: {
            ...state.canvas,
            elements: state.canvas.elements.filter(el => el.id !== id),
            layers: state.canvas.layers.filter(layerId => layerId !== id)
          },
          selectedElementId: state.selectedElementId === id ? null : state.selectedElementId
        }));
        get().saveToHistory();
      },

      selectElement: (id) => {
        set({ selectedElementId: id });
      },

      duplicateElement: (id) => {
        const state = get();
        const element = state.canvas.elements.find(el => el.id === id);
        if (element) {
          const newElement = {
            ...element,
            x: element.x + 20,
            y: element.y + 20
          };
          delete (newElement as any).id; // Remove id so addElement generates new one
          get().addElement(newElement);
        }
      },

      // Tool actions
      setActiveTool: (tool) => {
        set({ activeTool: tool });
      },

      setPlacement: (placement) => {
        set({ placement });
      },

      setZoom: (zoom) => {
        set({ zoom: Math.max(0.1, Math.min(3, zoom)) });
      },

      // History actions
      undo: () => {
        const state = get();
        if (state.historyIndex > 0) {
          const newIndex = state.historyIndex - 1;
          set({
            canvas: state.history[newIndex],
            historyIndex: newIndex,
            selectedElementId: null
          });
        }
      },

      redo: () => {
        const state = get();
        if (state.historyIndex < state.history.length - 1) {
          const newIndex = state.historyIndex + 1;
          set({
            canvas: state.history[newIndex],
            historyIndex: newIndex,
            selectedElementId: null
          });
        }
      },

      saveToHistory: () => {
        const state = get();
        const newHistory = state.history.slice(0, state.historyIndex + 1);
        newHistory.push({ ...state.canvas });
        
        // Limit history to 50 entries
        if (newHistory.length > 50) {
          newHistory.shift();
        } else {
          set({ historyIndex: state.historyIndex + 1 });
        }
        
        set({ history: newHistory });
      },

      // Utility actions
      clearCanvas: () => {
        const initialCanvas = createInitialCanvas();
        set({
          canvas: initialCanvas,
          selectedElementId: null,
          history: [initialCanvas],
          historyIndex: 0
        });
      },

      loadCanvas: (canvas) => {
        set({
          canvas,
          selectedElementId: null,
          history: [canvas],
          historyIndex: 0
        });
      },

      setLoading: (loading) => {
        set({ isLoading: loading });
      },

      // Text-specific actions
      addText: (text, x = 100, y = 100) => {
        const textData: TextElementData = {
          text,
          fontSize: 24,
          fontFamily: 'Inter',
          fontWeight: 'normal',
          fontStyle: 'normal',
          color: '#000000',
          align: 'left',
          mood: 'bold'
        };

        get().addElement({
          type: 'text',
          x,
          y,
          width: 200,
          height: 30,
          rotation: 0,
          opacity: 1,
          visible: true,
          locked: false,
          data: textData
        });
      },

      updateTextData: (id, data) => {
        const state = get();
        const element = state.canvas.elements.find(el => el.id === id);
        if (element && element.type === 'text') {
          get().updateElement(id, {
            data: { ...element.data, ...data }
          });
        }
      },

      // Image-specific actions
      addImage: (src, x = 100, y = 100) => {
        const imageData: ImageElementData = {
          src,
          originalWidth: 200,
          originalHeight: 200
        };

        get().addElement({
          type: 'image',
          x,
          y,
          width: 200,
          height: 200,
          rotation: 0,
          opacity: 1,
          visible: true,
          locked: false,
          data: imageData
        });
      },

      updateImageData: (id, data) => {
        const state = get();
        const element = state.canvas.elements.find(el => el.id === id);
        if (element && element.type === 'image') {
          get().updateElement(id, {
            data: { ...element.data, ...data }
          });
        }
      },

      // Touch gesture actions
      updateGestureState: (updates) => {
        set((state) => ({
          gestureState: { ...state.gestureState, ...updates }
        }));
      },

      updateGestureConfig: (updates) => {
        set((state) => ({
          gestureConfig: { ...state.gestureConfig, ...updates }
        }));
      },

      // Snap guide actions
      updateSnapGuides: () => {
        const state = get();
        const guides = generateSnapGuides(
          state.canvas,
          state.canvas.elements,
          state.selectedElementId || undefined
        );
        set({ snapGuides: guides });
      },

      setSnapEnabled: (enabled) => {
        set({ snapEnabled: enabled });
      },

      setSnapThreshold: (threshold) => {
        set({ snapThreshold: threshold });
      },

      setGridEnabled: (enabled) => {
        set({ gridEnabled: enabled });
      },

      setGridSize: (size) => {
        set({ gridSize: size });
      },

      // Template actions
      loadTemplate: (template) => {
        set({
          canvas: template.designData,
          selectedTemplate: template,
          selectedElementId: null
        });
        get().saveToHistory('Apply Template');
      },

      setSelectedTemplate: (template) => {
        set({ selectedTemplate: template });
      },

      addTemplate: (template) => {
        set((state) => ({
          availableTemplates: [...state.availableTemplates, template]
        }));
      },

      // Enhanced history actions
      startActionGroup: (name) => {
        const state = get();
        state.historyManager.startActionGroup(name);
      },

      finishActionGroup: () => {
        const state = get();
        state.historyManager.finishCurrentActionGroup();
      },

      // Mobile actions
      setMobileMode: (enabled) => {
        set({ isMobileMode: enabled });
      },

      setTouchOptimized: (enabled) => {
        set({ touchOptimized: enabled });
      },

      setHapticEnabled: (enabled) => {
        set({ hapticEnabled: enabled });
      }
    }),
    {
      name: 'ottiq-editor-store'
    }
  )
);
