// Global type definitions for Ottiq

export interface User {
  id: string;
  email: string;
  name?: string;
  image?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Design {
  id: string;
  title: string;
  description?: string;
  imageUrl?: string;
  designData: any; // Konva/Fabric.js design data
  category: string;
  tags: string[];
  isPublic: boolean;
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  userId: string;
  user?: User;
  createdAt: Date;
  updatedAt: Date;
}

export interface Order {
  id: string;
  orderNumber: string;
  status: 'PENDING' | 'CONFIRMED' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED' | 'REFUNDED';
  totalAmount: number;
  currency: string;
  shippingAddress: Address;
  billingAddress?: Address;
  userId: string;
  user?: User;
  items: OrderItem[];
  createdAt: Date;
  updatedAt: Date;
}

export interface OrderItem {
  id: string;
  quantity: number;
  price: number;
  size?: string;
  color?: string;
  orderId: string;
  designId: string;
  design?: Design;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  name?: string;
  phone?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// UI Component Types
export interface ComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// Design Editor Types
export interface DesignElement {
  id: string;
  type: 'text' | 'image' | 'shape' | 'pattern';
  x: number;
  y: number;
  width: number;
  height: number;
  rotation: number;
  opacity: number;
  visible: boolean;
  locked: boolean;
  data: any; // Element-specific data
}

export interface TextElementData {
  text: string;
  fontSize: number;
  fontFamily: string;
  fontWeight: 'normal' | 'bold' | 'lighter';
  fontStyle: 'normal' | 'italic';
  color: string;
  align: 'left' | 'center' | 'right';
  mood: 'bold' | 'elegant' | 'playful';
}

export interface ImageElementData {
  src: string;
  originalWidth: number;
  originalHeight: number;
  filters?: {
    brightness?: number;
    contrast?: number;
    saturation?: number;
  };
}

export interface DesignCanvas {
  width: number;
  height: number;
  backgroundColor: string;
  elements: DesignElement[];
  layers: string[]; // Element IDs in layer order
}

export type PlacementSide = 'front' | 'back' | 'left' | 'right';

export interface EditorState {
  canvas: DesignCanvas;
  selectedElementId: string | null;
  activeTool: 'select' | 'text' | 'image' | 'shape';
  placement: PlacementSide;
  zoom: number;
  isLoading: boolean;
  history: DesignCanvas[];
  historyIndex: number;
}

// Product Selection Flow Types
export interface ProductSelectionState {
  currentStep: ProductSelectionStep;
  productType?: ProductType;
  fabric?: Fabric;
  color?: Color;
  size?: Size;
  quantity: number;
  totalPrice?: number;
  isComplete: boolean;
}

export type ProductSelectionStep = 'product-type' | 'fabric' | 'color' | 'size';

export interface ProductType {
  id: string;
  name: string;
  category: string;
  subcategory?: string;

  // Emotional & Lifestyle Appeal
  moodTags: string[];
  lifestyleTags: string[];

  // Imagery for emotional connection
  heroImage: string;
  lifestyleImages: LifestyleImage[];

  // Emotional messaging
  tagline: string;
  description: string;
  emotionalHook: string; // "Feel confident in every meeting"

  // Pricing
  basePrice: number;
  currency: string;

  // Metadata
  isActive: boolean;
  isFeatured: boolean;
}

export interface LifestyleImage {
  url: string;
  context: string; // "street", "office", "weekend", "date-night"
  mood: string; // "confident", "playful", "sophisticated", "relaxed"
  model: string; // "diverse", "professional", "casual"
  alt: string;
}

export interface Fabric {
  id: string;
  name: string;
  description: string;

  // Sensory appeal
  texture: string; // "soft", "smooth", "structured", "breathable"
  weight: string; // "lightweight", "medium", "heavy"
  feel: string; // "luxurious", "comfortable", "athletic", "cozy"

  // Care & durability
  careInstructions: string[];
  durabilityRating: number; // 1-5

  // Emotional messaging
  emotionalBenefit: string; // "All-day comfort that moves with you"
  comfortPromise: string; // "Feels like a gentle hug"

  // Visual representation
  image: string;
  swatchImage: string;
  textureVideo?: string;

  // Pricing
  priceModifier: number; // Multiplier for base price

  // Metadata
  isActive: boolean;
  isPremium: boolean;
}

export interface Color {
  id: string;
  name: string;
  displayName: string; // "Midnight Black" vs "black"
  hexCode: string;

  // Color psychology & emotion
  mood: string[]; // ["sophisticated", "powerful", "timeless"]
  personality: string; // "Bold and confident"
  occasion: string[]; // ["professional", "evening", "casual"]

  // Inspiration & styling
  inspirationImage?: string;
  stylingTips: string[];
  complementaryColors: string[]; // Color IDs that pair well

  // Seasonal & trend data
  season: string[]; // ["spring", "summer", "fall", "winter", "year-round"]
  trendStatus: 'classic' | 'trending' | 'seasonal' | 'limited';

  // Availability
  isActive: boolean;
  stockLevel: 'high' | 'medium' | 'low' | 'out-of-stock';
}

export interface Size {
  id: string;
  name: string; // "XS", "S", "M", "L", "XL"
  displayName: string; // "Extra Small", "Small", etc.

  // Measurements
  measurements: SizeMeasurements;

  // Fit & comfort messaging
  fitDescription: string; // "Relaxed fit with room to move"
  bodyPositiveMessage: string; // "Designed to flatter every body"
  confidenceBooster: string; // "You'll feel amazing in this"

  // Sizing guidance
  recommendedFor: string[]; // ["athletic build", "curvy figure", "tall frame"]
  fitTips: string[];

  // Availability
  isActive: boolean;
  stockLevel: number;
}

export interface SizeMeasurements {
  chest?: number;
  waist?: number;
  hips?: number;
  length?: number;
  shoulderWidth?: number;
  sleeveLength?: number;
  // Measurements in inches
}

// Selection UI State
export interface SelectionUIState {
  isLoading: boolean;
  error?: string;
  animations: {
    fadeIn: boolean;
    slideDirection: 'left' | 'right' | 'up' | 'down';
    parallaxOffset: number;
  };
  priceBar: {
    isVisible: boolean;
    isAnimating: boolean;
    showBreakdown: boolean;
  };
}

// Touch Gesture Types
export interface TouchGestureState {
  isActive: boolean;
  type: 'pinch' | 'rotate' | 'pan' | 'tap' | 'longPress' | 'swipe';
  startTime: number;
  scale: number;
  rotation: number;
  translation: { x: number; y: number };
  velocity: { x: number; y: number };
  center: { x: number; y: number };
  fingers: number;
}

export interface GestureConfig {
  enablePinch: boolean;
  enableRotation: boolean;
  enablePan: boolean;
  minScale: number;
  maxScale: number;
  snapToGrid: boolean;
  snapThreshold: number;
  hapticFeedback: boolean;
}

export interface SnapGuide {
  id: string;
  type: 'horizontal' | 'vertical' | 'center' | 'edge';
  position: number;
  isActive: boolean;
  strength: number; // Magnetic strength 0-1
  color: string;
}

export interface SnapResult {
  snapped: boolean;
  guides: SnapGuide[];
  adjustedPosition: { x: number; y: number };
  feedback: 'visual' | 'haptic' | 'both';
}

export interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  moodTag: string;
  styleKeywords: string[];
  targetAudience: string;
  designData: DesignCanvas;
  previewImage: string;
  lifestyleContext: string[];
  emotionalHook: string;
  isActive: boolean;
  isPremium: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface TemplatePreview {
  template: Template;
  previewUrl: string;
  emotionalMessage: string;
  confidence: number; // 0-1 how well it matches user preferences
}

// Enhanced Editor State with Touch Support
export interface EnhancedEditorState extends EditorState {
  // Touch gesture state
  gestureState: TouchGestureState;
  gestureConfig: GestureConfig;

  // Snap guides
  snapGuides: SnapGuide[];
  snapEnabled: boolean;

  // Template system
  availableTemplates: Template[];
  selectedTemplate: Template | null;
  templatePreviews: TemplatePreview[];

  // Enhanced history with action grouping
  actionGroups: ActionGroup[];
  currentActionGroup: ActionGroup | null;

  // Mobile optimizations
  isMobileMode: boolean;
  touchOptimized: boolean;
  hapticEnabled: boolean;
}

export interface ActionGroup {
  id: string;
  name: string;
  actions: HistoryAction[];
  timestamp: number;
  canUndo: boolean;
  canRedo: boolean;
}

export interface HistoryAction {
  id: string;
  type: 'add' | 'update' | 'delete' | 'move' | 'transform' | 'template';
  elementId?: string;
  beforeState: any;
  afterState: any;
  timestamp: number;
}

// Emotional messaging templates
export interface EmotionalMessage {
  context: 'selection' | 'confirmation' | 'encouragement' | 'value';
  message: string;
  tone: 'inspiring' | 'confident' | 'playful' | 'sophisticated';
  trigger?: string; // When to show this message
}

// AI Try-On Types
export interface AiTryOnJob {
  id: string;
  status: AiTryOnStatus;
  jobId?: string; // External AI service job ID
  userPhotoUrl: string;
  customizationId: string;
  customization?: Customization;
  resultImageUrl?: string;
  confidence?: number;
  processingTime?: number;
  errorMessage?: string;
  retryCount: number;
  userId: string;
  user?: User;
  createdAt: Date;
  updatedAt: Date;
}

export type AiTryOnStatus = 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';

export interface AiTryOnRequest {
  userPhotoUrl: string;
  customizationId: string;
  userId: string;
  contextScene?: 'outdoor' | 'social' | 'active' | 'studio'; // Optional scene context
}

export interface AiTryOnResponse {
  success: boolean;
  data?: {
    jobId: string;
    status: AiTryOnStatus;
    estimatedProcessingTime?: number;
    resultImageUrl?: string;
    confidence?: number;
  };
  error?: string;
  message?: string;
}

export interface HuggingFaceOOTDRequest {
  person_image: string; // Base64 encoded image
  garment_image: string; // Base64 encoded garment image
  category: 'upper_body' | 'lower_body' | 'dresses';
  num_inference_steps?: number;
  guidance_scale?: number;
  seed?: number;
}

export interface HuggingFaceOOTDResponse {
  success: boolean;
  result_image?: string; // Base64 encoded result
  error?: string;
  processing_time?: number;
}

export interface Customization {
  id: string;
  name: string;
  description?: string;
  designData: any; // Store the customized design
  previewImage?: string;
  userId: string;
  user?: User;
  productId: string;
  product?: ProductType;
  templateId?: string;
  template?: Template;
  status: CustomizationStatus;
  createdAt: Date;
  updatedAt: Date;
}

export type CustomizationStatus = 'DRAFT' | 'COMPLETED' | 'ARCHIVED';
