// Pricing Engine Types - Emotional transparency and value framing

export interface PricingRequest {
  productId: string;
  variantId?: string;
  customizationId?: string;
  quantity: number;
  fabricId?: string;
  printSize?: PrintSize;
  qualityTier?: QualityTier;
  userId?: string; // For personalized pricing
}

export interface PricingResponse {
  success: boolean;
  data?: PricingBreakdown;
  error?: string;
}

export interface PricingBreakdown {
  // Core pricing
  basePrice: number;
  totalPrice: number;
  currency: string;
  
  // Detailed breakdown
  breakdown: PricingComponent[];
  
  // Emotional value messaging
  valueMessage: string;
  qualityPromise: string;
  savingsMessage?: string;
  
  // Transparency
  priceJustification: string[];
  
  // Metadata
  calculatedAt: string;
  validUntil: string;
  priceId: string; // For tracking
}

export interface PricingComponent {
  id: string;
  name: string;
  description: string;
  amount: number;
  type: 'base' | 'surcharge' | 'discount' | 'fee';
  
  // Emotional framing
  valueMessage: string;
  icon?: string;
  
  // Technical details (hidden from customer)
  ruleId?: string;
  calculation?: any;
}

export interface PricingRule {
  id: string;
  name: string;
  description: string;
  type: PricingRuleType;
  
  // Conditions
  conditions: PricingCondition[];
  
  // Action
  modifier: number;
  modifierType: 'fixed' | 'percentage';
  
  // Emotional messaging
  customerMessage: string;
  valueFraming: string;
  
  // Rule metadata
  priority: number;
  isActive: boolean;
  validFrom?: Date;
  validUntil?: Date;
  
  // Admin metadata
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export type PricingRuleType = 
  | 'fabric_surcharge'
  | 'print_size_cost'
  | 'quality_tier'
  | 'quantity_discount'
  | 'seasonal_promotion'
  | 'loyalty_discount'
  | 'first_time_discount'
  | 'complexity_surcharge';

export interface PricingCondition {
  field: string; // e.g., 'fabricType', 'printSize', 'quantity'
  operator: 'equals' | 'greater_than' | 'less_than' | 'in' | 'contains';
  value: any;
}

export type PrintSize = 'small' | 'medium' | 'large' | 'full_coverage';
export type QualityTier = 'standard' | 'premium' | 'luxury';

export interface PrintSizeConfig {
  size: PrintSize;
  name: string;
  description: string;
  maxDimensions: { width: number; height: number };
  baseMultiplier: number;
  valueMessage: string;
}

export interface QualityTierConfig {
  tier: QualityTier;
  name: string;
  description: string;
  features: string[];
  priceMultiplier: number;
  valueMessage: string;
  qualityPromise: string;
}

// Admin interfaces
export interface PricingRuleBuilder {
  rule: Partial<PricingRule>;
  preview?: PricingPreview;
}

export interface PricingPreview {
  sampleRequests: PricingRequest[];
  results: PricingResponse[];
  totalTests: number;
  passedTests: number;
}

// Value messaging templates
export interface ValueMessage {
  context: 'fabric' | 'quality' | 'print' | 'discount' | 'general';
  template: string;
  variables: string[];
}

// Pricing analytics
export interface PricingAnalytics {
  averageOrderValue: number;
  conversionRate: number;
  priceElasticity: number;
  popularTiers: QualityTier[];
  revenueByRule: Record<string, number>;
}
