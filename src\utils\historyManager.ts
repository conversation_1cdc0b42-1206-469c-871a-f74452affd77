'use client';

import { DesignCanvas, HistoryAction, ActionGroup } from '@/types';
import { triggerHapticFeedback } from './touchGestures';

// History configuration
export const HISTORY_CONFIG = {
  maxEntries: 50,
  maxActionGroupTime: 1000, // 1 second to group actions
  autoSaveInterval: 5000, // 5 seconds
};

// Action types for better categorization
export const ACTION_TYPES = {
  ADD_ELEMENT: 'add',
  UPDATE_ELEMENT: 'update',
  DELETE_ELEMENT: 'delete',
  MOVE_ELEMENT: 'move',
  TRANSFORM_ELEMENT: 'transform',
  APPLY_TEMPLATE: 'template',
  BATCH_UPDATE: 'batch',
} as const;

// Create a new action
export const createAction = (
  type: HistoryAction['type'],
  elementId: string | undefined,
  beforeState: any,
  afterState: any
): HistoryAction => ({
  id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  type,
  elementId,
  beforeState,
  afterState,
  timestamp: Date.now(),
});

// Create a new action group
export const createActionGroup = (
  name: string,
  actions: HistoryAction[] = []
): ActionGroup => ({
  id: `group_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  name,
  actions,
  timestamp: Date.now(),
  canUndo: true,
  canRedo: false,
});

// Enhanced history manager class
export class HistoryManager {
  private history: DesignCanvas[] = [];
  private historyIndex: number = 0;
  private actionGroups: ActionGroup[] = [];
  private currentActionGroup: ActionGroup | null = null;
  private lastActionTime: number = 0;

  constructor(initialCanvas: DesignCanvas) {
    this.history = [{ ...initialCanvas }];
    this.historyIndex = 0;
  }

  // Get current state
  getCurrentState(): DesignCanvas {
    return this.history[this.historyIndex];
  }

  // Check if can undo
  canUndo(): boolean {
    return this.historyIndex > 0;
  }

  // Check if can redo
  canRedo(): boolean {
    return this.historyIndex < this.history.length - 1;
  }

  // Start a new action group
  startActionGroup(name: string): void {
    this.finishCurrentActionGroup();
    this.currentActionGroup = createActionGroup(name);
  }

  // Add action to current group
  addAction(action: HistoryAction): void {
    const now = Date.now();
    
    // Auto-group actions that happen within the time threshold
    if (!this.currentActionGroup || (now - this.lastActionTime) > HISTORY_CONFIG.maxActionGroupTime) {
      this.startActionGroup(this.getActionGroupName(action.type));
    }
    
    if (this.currentActionGroup) {
      this.currentActionGroup.actions.push(action);
    }
    
    this.lastActionTime = now;
  }

  // Finish current action group and save to history
  finishCurrentActionGroup(): void {
    if (this.currentActionGroup && this.currentActionGroup.actions.length > 0) {
      this.actionGroups.push(this.currentActionGroup);
      this.currentActionGroup = null;
    }
  }

  // Save canvas state to history
  saveToHistory(canvas: DesignCanvas, actionName?: string): void {
    this.finishCurrentActionGroup();
    
    // Remove any future history if we're not at the end
    if (this.historyIndex < this.history.length - 1) {
      this.history = this.history.slice(0, this.historyIndex + 1);
      this.actionGroups = this.actionGroups.slice(0, this.historyIndex);
    }
    
    // Add new state
    this.history.push({ ...canvas });
    this.historyIndex++;
    
    // Limit history size
    if (this.history.length > HISTORY_CONFIG.maxEntries) {
      this.history.shift();
      this.actionGroups.shift();
      this.historyIndex--;
    }
  }

  // Undo last action
  undo(): { canvas: DesignCanvas | null; actionName: string } {
    if (!this.canUndo()) {
      return { canvas: null, actionName: '' };
    }
    
    this.historyIndex--;
    const canvas = this.history[this.historyIndex];
    const actionGroup = this.actionGroups[this.historyIndex];
    
    // Trigger haptic feedback
    triggerHapticFeedback('medium');
    
    return {
      canvas: { ...canvas },
      actionName: actionGroup?.name || 'Undo',
    };
  }

  // Redo next action
  redo(): { canvas: DesignCanvas | null; actionName: string } {
    if (!this.canRedo()) {
      return { canvas: null, actionName: '' };
    }
    
    this.historyIndex++;
    const canvas = this.history[this.historyIndex];
    const actionGroup = this.actionGroups[this.historyIndex - 1];
    
    // Trigger haptic feedback
    triggerHapticFeedback('medium');
    
    return {
      canvas: { ...canvas },
      actionName: actionGroup?.name || 'Redo',
    };
  }

  // Get history summary for UI
  getHistorySummary(): {
    canUndo: boolean;
    canRedo: boolean;
    undoActionName: string;
    redoActionName: string;
    totalActions: number;
    currentIndex: number;
  } {
    const undoGroup = this.historyIndex > 0 ? this.actionGroups[this.historyIndex - 1] : null;
    const redoGroup = this.historyIndex < this.actionGroups.length ? this.actionGroups[this.historyIndex] : null;
    
    return {
      canUndo: this.canUndo(),
      canRedo: this.canRedo(),
      undoActionName: undoGroup?.name || '',
      redoActionName: redoGroup?.name || '',
      totalActions: this.history.length,
      currentIndex: this.historyIndex,
    };
  }

  // Get action group name based on action type
  private getActionGroupName(actionType: HistoryAction['type']): string {
    switch (actionType) {
      case 'add':
        return 'Add Element';
      case 'update':
        return 'Update Element';
      case 'delete':
        return 'Delete Element';
      case 'move':
        return 'Move Element';
      case 'transform':
        return 'Transform Element';
      case 'template':
        return 'Apply Template';
      default:
        return 'Edit';
    }
  }

  // Clear all history
  clear(initialCanvas: DesignCanvas): void {
    this.history = [{ ...initialCanvas }];
    this.historyIndex = 0;
    this.actionGroups = [];
    this.currentActionGroup = null;
    this.lastActionTime = 0;
  }

  // Get action groups for debugging/UI
  getActionGroups(): ActionGroup[] {
    return [...this.actionGroups];
  }

  // Batch operations
  startBatch(name: string): void {
    this.startActionGroup(name);
  }

  endBatch(canvas: DesignCanvas): void {
    this.finishCurrentActionGroup();
    this.saveToHistory(canvas);
  }

  // Auto-save functionality
  shouldAutoSave(): boolean {
    return Date.now() - this.lastActionTime > HISTORY_CONFIG.autoSaveInterval;
  }
}

// Utility functions for common operations
export const createElementAction = (
  type: 'add' | 'update' | 'delete',
  elementId: string,
  beforeElement: any,
  afterElement: any
): HistoryAction => {
  return createAction(type, elementId, beforeElement, afterElement);
};

export const createMoveAction = (
  elementId: string,
  beforePosition: { x: number; y: number },
  afterPosition: { x: number; y: number }
): HistoryAction => {
  return createAction('move', elementId, beforePosition, afterPosition);
};

export const createTransformAction = (
  elementId: string,
  beforeTransform: any,
  afterTransform: any
): HistoryAction => {
  return createAction('transform', elementId, beforeTransform, afterTransform);
};

// Performance monitoring
export const measureHistoryPerformance = <T>(
  operation: string,
  fn: () => T
): T => {
  const start = performance.now();
  const result = fn();
  const end = performance.now();
  
  if (end - start > 16) { // More than one frame
    console.warn(`History operation "${operation}" took ${end - start}ms`);
  }
  
  return result;
};
