'use client';

import { SnapGuide, SnapResult, DesignElement, DesignCanvas } from '@/types';
import { triggerHapticFeedback } from './touchGestures';

// Default snap guides configuration
export const DEFAULT_SNAP_THRESHOLD = 10;
export const GUIDE_COLORS = {
  horizontal: '#4F46E5',
  vertical: '#4F46E5',
  center: '#F59E0B',
  edge: '#EF4444',
};

// Generate snap guides for canvas
export const generateSnapGuides = (
  canvas: DesignCanvas,
  elements: DesignElement[],
  excludeElementId?: string
): SnapGuide[] => {
  const guides: SnapGuide[] = [];
  
  // Canvas center guides
  guides.push({
    id: 'canvas-center-h',
    type: 'horizontal',
    position: canvas.height / 2,
    isActive: false,
    strength: 0.8,
    color: GUIDE_COLORS.center,
  });
  
  guides.push({
    id: 'canvas-center-v',
    type: 'vertical',
    position: canvas.width / 2,
    isActive: false,
    strength: 0.8,
    color: GUI<PERSON>_COLORS.center,
  });
  
  // Canvas edge guides
  guides.push(
    {
      id: 'canvas-top',
      type: 'horizontal',
      position: 0,
      isActive: false,
      strength: 0.6,
      color: GUIDE_COLORS.edge,
    },
    {
      id: 'canvas-bottom',
      type: 'horizontal',
      position: canvas.height,
      isActive: false,
      strength: 0.6,
      color: GUIDE_COLORS.edge,
    },
    {
      id: 'canvas-left',
      type: 'vertical',
      position: 0,
      isActive: false,
      strength: 0.6,
      color: GUIDE_COLORS.edge,
    },
    {
      id: 'canvas-right',
      type: 'vertical',
      position: canvas.width,
      isActive: false,
      strength: 0.6,
      color: GUIDE_COLORS.edge,
    }
  );
  
  // Element-based guides
  elements
    .filter(el => el.id !== excludeElementId && el.visible)
    .forEach(element => {
      const centerX = element.x + element.width / 2;
      const centerY = element.y + element.height / 2;
      
      // Element center guides
      guides.push({
        id: `element-${element.id}-center-h`,
        type: 'horizontal',
        position: centerY,
        isActive: false,
        strength: 0.7,
        color: GUIDE_COLORS.horizontal,
      });
      
      guides.push({
        id: `element-${element.id}-center-v`,
        type: 'vertical',
        position: centerX,
        isActive: false,
        strength: 0.7,
        color: GUIDE_COLORS.vertical,
      });
      
      // Element edge guides
      guides.push(
        {
          id: `element-${element.id}-top`,
          type: 'horizontal',
          position: element.y,
          isActive: false,
          strength: 0.5,
          color: GUIDE_COLORS.horizontal,
        },
        {
          id: `element-${element.id}-bottom`,
          type: 'horizontal',
          position: element.y + element.height,
          isActive: false,
          strength: 0.5,
          color: GUIDE_COLORS.horizontal,
        },
        {
          id: `element-${element.id}-left`,
          type: 'vertical',
          position: element.x,
          isActive: false,
          strength: 0.5,
          color: GUIDE_COLORS.vertical,
        },
        {
          id: `element-${element.id}-right`,
          type: 'vertical',
          position: element.x + element.width,
          isActive: false,
          strength: 0.5,
          color: GUIDE_COLORS.vertical,
        }
      );
    });
  
  return guides;
};

// Calculate snap result for element position
export const calculateSnapResult = (
  elementPosition: { x: number; y: number },
  elementSize: { width: number; height: number },
  guides: SnapGuide[],
  threshold: number = DEFAULT_SNAP_THRESHOLD
): SnapResult => {
  const elementCenterX = elementPosition.x + elementSize.width / 2;
  const elementCenterY = elementPosition.y + elementSize.height / 2;
  
  let snappedX = elementPosition.x;
  let snappedY = elementPosition.y;
  let snapped = false;
  const activeGuides: SnapGuide[] = [];
  
  // Check vertical guides (affect X position)
  const verticalGuides = guides.filter(g => g.type === 'vertical');
  for (const guide of verticalGuides) {
    const distanceToCenter = Math.abs(elementCenterX - guide.position);
    const distanceToLeft = Math.abs(elementPosition.x - guide.position);
    const distanceToRight = Math.abs(elementPosition.x + elementSize.width - guide.position);
    
    const minDistance = Math.min(distanceToCenter, distanceToLeft, distanceToRight);
    
    if (minDistance <= threshold * guide.strength) {
      if (distanceToCenter === minDistance) {
        snappedX = guide.position - elementSize.width / 2;
      } else if (distanceToLeft === minDistance) {
        snappedX = guide.position;
      } else {
        snappedX = guide.position - elementSize.width;
      }
      
      activeGuides.push({ ...guide, isActive: true });
      snapped = true;
      break; // Use the first matching guide
    }
  }
  
  // Check horizontal guides (affect Y position)
  const horizontalGuides = guides.filter(g => g.type === 'horizontal');
  for (const guide of horizontalGuides) {
    const distanceToCenter = Math.abs(elementCenterY - guide.position);
    const distanceToTop = Math.abs(elementPosition.y - guide.position);
    const distanceToBottom = Math.abs(elementPosition.y + elementSize.height - guide.position);
    
    const minDistance = Math.min(distanceToCenter, distanceToTop, distanceToBottom);
    
    if (minDistance <= threshold * guide.strength) {
      if (distanceToCenter === minDistance) {
        snappedY = guide.position - elementSize.height / 2;
      } else if (distanceToTop === minDistance) {
        snappedY = guide.position;
      } else {
        snappedY = guide.position - elementSize.height;
      }
      
      activeGuides.push({ ...guide, isActive: true });
      snapped = true;
      break; // Use the first matching guide
    }
  }
  
  return {
    snapped,
    guides: activeGuides,
    adjustedPosition: { x: snappedX, y: snappedY },
    feedback: snapped ? 'both' : 'visual',
  };
};

// Apply snap feedback
export const applySnapFeedback = (snapResult: SnapResult, hapticEnabled: boolean = true): void => {
  if (snapResult.snapped) {
    // Visual feedback is handled by the UI components
    
    // Haptic feedback
    if (hapticEnabled && snapResult.feedback === 'both') {
      triggerHapticFeedback('selection');
    }
  }
};

// Grid snap utilities
export const snapToGrid = (
  position: { x: number; y: number },
  gridSize: number = 20,
  threshold: number = 10
): { x: number; y: number; snapped: boolean } => {
  const snappedX = Math.round(position.x / gridSize) * gridSize;
  const snappedY = Math.round(position.y / gridSize) * gridSize;
  
  const distanceX = Math.abs(position.x - snappedX);
  const distanceY = Math.abs(position.y - snappedY);
  
  const shouldSnapX = distanceX <= threshold;
  const shouldSnapY = distanceY <= threshold;
  
  return {
    x: shouldSnapX ? snappedX : position.x,
    y: shouldSnapY ? snappedY : position.y,
    snapped: shouldSnapX || shouldSnapY,
  };
};

// Smart alignment detection
export const detectAlignment = (
  elements: DesignElement[],
  threshold: number = 5
): { horizontal: DesignElement[][]; vertical: DesignElement[][] } => {
  const horizontal: DesignElement[][] = [];
  const vertical: DesignElement[][] = [];
  
  // Group elements by similar Y positions (horizontal alignment)
  const yGroups = new Map<number, DesignElement[]>();
  elements.forEach(element => {
    const centerY = element.y + element.height / 2;
    let foundGroup = false;
    
    for (const [groupY, group] of yGroups) {
      if (Math.abs(centerY - groupY) <= threshold) {
        group.push(element);
        foundGroup = true;
        break;
      }
    }
    
    if (!foundGroup) {
      yGroups.set(centerY, [element]);
    }
  });
  
  // Group elements by similar X positions (vertical alignment)
  const xGroups = new Map<number, DesignElement[]>();
  elements.forEach(element => {
    const centerX = element.x + element.width / 2;
    let foundGroup = false;
    
    for (const [groupX, group] of xGroups) {
      if (Math.abs(centerX - groupX) <= threshold) {
        group.push(element);
        foundGroup = true;
        break;
      }
    }
    
    if (!foundGroup) {
      xGroups.set(centerX, [element]);
    }
  });
  
  // Filter groups with multiple elements
  horizontal.push(...Array.from(yGroups.values()).filter(group => group.length > 1));
  vertical.push(...Array.from(xGroups.values()).filter(group => group.length > 1));
  
  return { horizontal, vertical };
};

// Distance calculation utilities
export const getElementDistance = (element1: DesignElement, element2: DesignElement): number => {
  const center1 = {
    x: element1.x + element1.width / 2,
    y: element1.y + element1.height / 2,
  };
  const center2 = {
    x: element2.x + element2.width / 2,
    y: element2.y + element2.height / 2,
  };
  
  const dx = center1.x - center2.x;
  const dy = center1.y - center2.y;
  
  return Math.sqrt(dx * dx + dy * dy);
};
