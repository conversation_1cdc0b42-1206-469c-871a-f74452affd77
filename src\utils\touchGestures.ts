'use client';

import { TouchGestureState, GestureConfig, SnapGuide, SnapResult } from '@/types';

// Default gesture configuration
export const DEFAULT_GESTURE_CONFIG: GestureConfig = {
  enablePinch: true,
  enableRotation: true,
  enablePan: true,
  minScale: 0.1,
  maxScale: 5.0,
  snapToGrid: true,
  snapThreshold: 10,
  hapticFeedback: true,
};

// Initial gesture state
export const createInitialGestureState = (): TouchGestureState => ({
  isActive: false,
  type: 'tap',
  startTime: 0,
  scale: 1,
  rotation: 0,
  translation: { x: 0, y: 0 },
  velocity: { x: 0, y: 0 },
  center: { x: 0, y: 0 },
  fingers: 0,
});

// Gesture recognition utilities
export const recognizeGesture = (
  touches: TouchList,
  previousTouches?: TouchList
): TouchGestureState['type'] => {
  const touchCount = touches.length;
  
  if (touchCount === 0) return 'tap';
  if (touchCount === 1) return 'pan';
  if (touchCount === 2) {
    // Determine if it's pinch or rotate based on movement
    if (previousTouches && previousTouches.length === 2) {
      const currentDistance = getTouchDistance(touches[0], touches[1]);
      const previousDistance = getTouchDistance(previousTouches[0], previousTouches[1]);
      const distanceChange = Math.abs(currentDistance - previousDistance);
      
      const currentAngle = getTouchAngle(touches[0], touches[1]);
      const previousAngle = getTouchAngle(previousTouches[0], previousTouches[1]);
      const angleChange = Math.abs(currentAngle - previousAngle);
      
      if (distanceChange > angleChange) return 'pinch';
      if (angleChange > 10) return 'rotate'; // 10 degrees threshold
    }
    return 'pinch';
  }
  
  return 'pan';
};

// Touch distance calculation
export const getTouchDistance = (touch1: Touch, touch2: Touch): number => {
  const dx = touch1.clientX - touch2.clientX;
  const dy = touch1.clientY - touch2.clientY;
  return Math.sqrt(dx * dx + dy * dy);
};

// Touch angle calculation
export const getTouchAngle = (touch1: Touch, touch2: Touch): number => {
  const dx = touch1.clientX - touch2.clientX;
  const dy = touch1.clientY - touch2.clientY;
  return Math.atan2(dy, dx) * (180 / Math.PI);
};

// Touch center calculation
export const getTouchCenter = (touches: TouchList): { x: number; y: number } => {
  if (touches.length === 0) return { x: 0, y: 0 };
  
  let totalX = 0;
  let totalY = 0;
  
  for (let i = 0; i < touches.length; i++) {
    totalX += touches[i].clientX;
    totalY += touches[i].clientY;
  }
  
  return {
    x: totalX / touches.length,
    y: totalY / touches.length,
  };
};

// Velocity calculation
export const calculateVelocity = (
  currentPosition: { x: number; y: number },
  previousPosition: { x: number; y: number },
  deltaTime: number
): { x: number; y: number } => {
  if (deltaTime === 0) return { x: 0, y: 0 };
  
  return {
    x: (currentPosition.x - previousPosition.x) / deltaTime,
    y: (currentPosition.y - previousPosition.y) / deltaTime,
  };
};

// Haptic feedback utility
export const triggerHapticFeedback = (
  type: 'light' | 'medium' | 'heavy' | 'selection' | 'impact' = 'light'
): void => {
  if (!('vibrate' in navigator)) return;
  
  const patterns = {
    light: [10],
    medium: [20],
    heavy: [30],
    selection: [5, 5, 5],
    impact: [15, 10, 15],
  };
  
  navigator.vibrate(patterns[type]);
};

// Smooth animation utilities
export const easeOutCubic = (t: number): number => {
  return 1 - Math.pow(1 - t, 3);
};

export const easeInOutCubic = (t: number): number => {
  return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
};

// Constraint utilities
export const constrainScale = (scale: number, min: number, max: number): number => {
  return Math.max(min, Math.min(max, scale));
};

export const constrainRotation = (rotation: number): number => {
  // Normalize rotation to 0-360 degrees
  while (rotation < 0) rotation += 360;
  while (rotation >= 360) rotation -= 360;
  return rotation;
};

export const constrainPosition = (
  position: { x: number; y: number },
  bounds: { x: number; y: number; width: number; height: number }
): { x: number; y: number } => {
  return {
    x: Math.max(bounds.x, Math.min(bounds.x + bounds.width, position.x)),
    y: Math.max(bounds.y, Math.min(bounds.y + bounds.height, position.y)),
  };
};

// Debounce utility for performance
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// Throttle utility for performance
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

// Performance monitoring
export const measurePerformance = (name: string, fn: () => void): void => {
  const start = performance.now();
  fn();
  const end = performance.now();
  console.log(`${name} took ${end - start} milliseconds`);
};
